const fs = require('fs');
const { SuiClient } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { Transaction } = require('@mysten/sui/transactions');

const FATE_TYPE = '0x47c6cba4c841b9312e0607bacf56682e11dcdfaeabb4bd279a46c9942eaaaac8::fate::FATE';
const COIN_TYPE = `0x2::coin::Coin<${FATE_TYPE}>`;
const WALLETS_FILE = 'data_sources/wallets_source.json';
const CSV_FILE = 'data_sources/desired_wallet_amounts.csv';
const SUI_RPC_URL = 'https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/';

const suiClient = new SuiClient({ url: SUI_RPC_URL });

// Get FATE balance for a wallet
async function getFateBalanceForWallet(address) {
  let hasNextPage = true;
  let nextCursor = null;
  let totalBalance = 0n;
  let coinObjects = [];

  while (hasNextPage) {
    const resp = await suiClient.getOwnedObjects({
      owner: address,
      filter: { StructType: COIN_TYPE },
      options: { showContent: true },
      cursor: nextCursor,
      limit: 50,
    });

    for (const obj of resp.data) {
      if (
        obj.data &&
        obj.data.content &&
        obj.data.content.fields &&
        obj.data.content.fields.balance
      ) {
        const balance = BigInt(obj.data.content.fields.balance);
        totalBalance += balance;
        coinObjects.push({
          objectId: obj.data.objectId,
          balance: balance
        });
      }
    }

    hasNextPage = resp.hasNextPage;
    nextCursor = resp.nextCursor;
  }

  return { totalBalance, coinObjects };
}

// Load and parse the CSV file
function loadTargetWallets() {
  const csvContent = fs.readFileSync(CSV_FILE, 'utf-8');
  const lines = csvContent.split('\n').slice(1); // Skip header
  const targets = [];
  let skippedCount = 0;

  for (const line of lines) {
    if (line.trim()) {
      const parts = line.split(',');
      const address = parts[0].trim();
      const amount = parseInt(parts[1].trim());
      const txHash = parts[2] ? parts[2].trim() : '';

      if (address && amount > 0) {
        // Skip wallets that already have a transaction hash
        if (txHash && txHash !== '') {
          skippedCount++;
          console.log(`⏭️  Skipping ${address} (already processed, TxHash: ${txHash})`);
          continue;
        }

        targets.push({
          address: address,
          desiredAmount: BigInt(amount) * 1000000000n // Convert to raw units (9 decimals)
        });
      }
    }
  }

  console.log(`📊 CSV Summary: ${skippedCount} wallets already processed, ${targets.length} remaining to process`);
  return targets;
}

// Update CSV file with transaction hash
function updateCsvWithTxHash(walletAddress, txHash) {
  try {
    const csvContent = fs.readFileSync(CSV_FILE, 'utf-8');
    const lines = csvContent.split('\n');

    // Update the line for this wallet
    for (let i = 1; i < lines.length; i++) { // Skip header
      if (lines[i].trim()) {
        const parts = lines[i].split(',');
        const address = parts[0].trim();

        if (address === walletAddress) {
          // Update the TxHash column (index 2)
          parts[2] = txHash;
          lines[i] = parts.join(',');
          break;
        }
      }
    }

    // Write back to file
    fs.writeFileSync(CSV_FILE, lines.join('\n'));
    console.log(`📝 Updated CSV with tx hash for ${walletAddress}`);

  } catch (error) {
    console.error(`Error updating CSV for ${walletAddress}:`, error.message);
  }
}

// Load source wallets and get their FATE balances
async function loadSourceWallets() {
  const wallets = JSON.parse(fs.readFileSync(WALLETS_FILE, 'utf-8'));
  const sourceWallets = [];

  console.log('Checking FATE balances for source wallets...');

  for (const wallet of wallets) {
    try {
      const { totalBalance, coinObjects } = await getFateBalanceForWallet(wallet.address);

      if (totalBalance > 0n) {
        sourceWallets.push({
          address: wallet.address,
          privateKey: wallet.privateKey,
          fateBalance: totalBalance,
          coinObjects: coinObjects
        });

        const fateHuman = Number(totalBalance) / 1e9;
        console.log(`Source wallet ${wallet.address}: ${fateHuman} FATE`);
      }
    } catch (err) {
      console.error(`Error checking wallet ${wallet.address}:`, err.message);
    }
  }

  // Sort by FATE balance descending (largest holders first)
  sourceWallets.sort((a, b) => {
    if (a.fateBalance > b.fateBalance) return -1;
    if (a.fateBalance < b.fateBalance) return 1;
    return 0;
  });

  return sourceWallets;
}

// Create distribution plan
function createDistributionPlan(sourceWallets, targetWallets) {
  const plan = [];
  const unfundedWallets = [];
  let sourceIndex = 0;

  console.log('\n=== DISTRIBUTION PLAN ===');
  console.log(`Source wallets: ${sourceWallets.length}`);
  console.log(`Target wallets: ${targetWallets.length}`);

  for (const target of targetWallets) {
    const targetFateHuman = Number(target.desiredAmount) / 1e9;

    // Find a source wallet with enough balance
    let sourceWallet = null;
    for (let i = sourceIndex; i < sourceWallets.length; i++) {
      if (sourceWallets[i].fateBalance >= target.desiredAmount) {
        sourceWallet = sourceWallets[i];
        sourceIndex = i;
        break;
      }
    }

    if (sourceWallet) {
      plan.push({
        sourceAddress: sourceWallet.address,
        sourcePrivateKey: sourceWallet.privateKey,
        sourceCoinObjects: sourceWallet.coinObjects,
        targetAddress: target.address,
        amount: target.desiredAmount
      });

      // Update source wallet balance
      sourceWallet.fateBalance -= target.desiredAmount;

      const sourceFateHuman = Number(sourceWallet.fateBalance) / 1e9;
      console.log(`${target.address} ← ${targetFateHuman} FATE from ${sourceWallet.address} (remaining: ${sourceFateHuman})`);

      // If source wallet is depleted, move to next one
      if (sourceWallet.fateBalance < 1000000000n) { // Less than 1 FATE
        sourceIndex++;
      }
    } else {
      console.log(`⚠️  Cannot fund ${target.address} (needs ${targetFateHuman} FATE) - insufficient FATE remaining`);
      unfundedWallets.push({
        address: target.address,
        neededAmount: target.desiredAmount,
        neededAmountHuman: targetFateHuman
      });
    }
  }

  // Report unfunded wallets
  if (unfundedWallets.length > 0) {
    console.log(`\n⚠️  ${unfundedWallets.length} wallets could not be funded due to insufficient FATE:`);
    for (const wallet of unfundedWallets) {
      console.log(`  ${wallet.address}: needs ${wallet.neededAmountHuman} FATE`);
    }
  }

  return { plan, unfundedWallets };
}

// Execute a single transfer
async function executeTransfer(transfer) {
  try {
    // Create keypair from private key
    // Handle both hex format (0x...) and suiprivkey format (suiprivkey1...)
    let keypair;
    if (transfer.sourcePrivateKey.startsWith('0x')) {
      // Raw hex format
      const privateKeyBytes = Buffer.from(transfer.sourcePrivateKey.slice(2), 'hex');
      keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
    } else if (transfer.sourcePrivateKey.startsWith('suiprivkey1')) {
      // Base64 encoded format, remove prefix and decode
      const base64Key = transfer.sourcePrivateKey.replace('suiprivkey1', '');
      const privateKeyBytes = Buffer.from(base64Key, 'base64');
      keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
    } else {
      throw new Error('Unsupported private key format');
    }

    console.log(`\nTransferring ${Number(transfer.amount) / 1e9} FATE from ${transfer.sourceAddress} to ${transfer.targetAddress}`);

    // Create transaction
    const tx = new Transaction();

    // Find coins to cover the amount
    let remainingAmount = transfer.amount;
    const coinsToMerge = [];

    for (const coin of transfer.sourceCoinObjects) {
      if (remainingAmount <= 0n) break;

      coinsToMerge.push(coin.objectId);
      remainingAmount -= coin.balance;
    }

    if (coinsToMerge.length === 0) {
      throw new Error('No coins available for transfer');
    }

    let coinToSplit;
    if (coinsToMerge.length === 1) {
      coinToSplit = tx.object(coinsToMerge[0]);
    } else {
      // Merge multiple coins
      coinToSplit = tx.object(coinsToMerge[0]);
      if (coinsToMerge.length > 1) {
        tx.mergeCoins(coinToSplit, coinsToMerge.slice(1).map(id => tx.object(id)));
      }
    }

    // Split the exact amount needed
    const splitCoin = tx.splitCoins(coinToSplit, [transfer.amount.toString()]);

    // Transfer to target address
    tx.transferObjects([splitCoin], transfer.targetAddress);

    // Sign and execute transaction
    const result = await suiClient.signAndExecuteTransaction({
      signer: keypair,
      transaction: tx,
    });

    if (result.digest) {
      console.log(`✅ Transfer successful! Tx: ${result.digest}`);

      // Update CSV with transaction hash
      updateCsvWithTxHash(transfer.targetAddress, result.digest);

      return { success: true, txHash: result.digest, targetAddress: transfer.targetAddress };
    } else {
      console.log(`❌ Transfer failed:`, result);
      return { success: false, error: result, targetAddress: transfer.targetAddress };
    }

  } catch (error) {
    console.error(`❌ Transfer error:`, error.message);
    return { success: false, error: error.message, targetAddress: transfer.targetAddress };
  }
}

// Execute all transfers with delays
async function executeAllTransfers(distributionPlan) {
  console.log('\n=== EXECUTING TRANSFERS ===');

  const results = [];

  for (let i = 0; i < distributionPlan.length; i++) {
    const transfer = distributionPlan[i];

    console.log(`\nTransfer ${i + 1}/${distributionPlan.length}`);
    const result = await executeTransfer(transfer);
    results.push(result);

    // Add delay between transfers to avoid rate limiting
    if (i < distributionPlan.length - 1) {
      console.log('Waiting 2 seconds before next transfer...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // Summary
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  console.log('\n=== TRANSFER SUMMARY ===');
  console.log(`Total transfers: ${results.length}`);
  console.log(`Successful: ${successful}`);
  console.log(`Failed: ${failed}`);

  // Show failed transfers
  const failedTransfers = results.filter(r => !r.success);
  if (failedTransfers.length > 0) {
    console.log('\n❌ Failed transfers:');
    for (const failed of failedTransfers) {
      console.log(`  ${failed.targetAddress}: ${failed.error}`);
    }
  }

  return results;
}

// Main function
async function main() {
  try {
    console.log('🚀 Starting FATE token distribution...\n');

    // Load target wallets from CSV
    console.log('Loading target wallets from CSV...');
    const targetWallets = loadTargetWallets();
    console.log(`Loaded ${targetWallets.length} target wallets`);

    // Load source wallets and check FATE balances
    console.log('\nLoading source wallets and checking FATE balances...');
    const sourceWallets = await loadSourceWallets();
    console.log(`Found ${sourceWallets.length} source wallets with FATE tokens`);

    if (sourceWallets.length === 0) {
      console.log('❌ No source wallets with FATE tokens found!');
      return;
    }

    // Calculate total available FATE
    const totalAvailableFate = sourceWallets.reduce((sum, wallet) => sum + wallet.fateBalance, 0n);
    const totalNeededFate = targetWallets.reduce((sum, target) => sum + target.desiredAmount, 0n);

    console.log(`\nTotal FATE available: ${Number(totalAvailableFate) / 1e9}`);
    console.log(`Total FATE needed: ${Number(totalNeededFate) / 1e9}`);

    if (totalAvailableFate < totalNeededFate) {
      console.log('⚠️  Not enough FATE tokens available for all distributions.');
      console.log('Will distribute what we can until FATE runs out...\n');
    }

    // Create distribution plan
    console.log('Creating distribution plan...');
    const { plan, unfundedWallets } = createDistributionPlan(sourceWallets, targetWallets);

    if (plan.length === 0) {
      console.log('❌ No valid transfers could be planned!');
      return;
    }

    // Confirm before executing
    console.log(`\n⚠️  Ready to execute ${plan.length} FATE token transfers.`);
    console.log('This will distribute FATE tokens from your source wallets to the target wallets.');
    console.log('The CSV file will be updated with transaction hashes for successful transfers.');
    console.log('Press Ctrl+C to cancel, or any key to continue...');

    // Wait for user input (simplified - in production you might want proper input handling)
    await new Promise(resolve => {
      process.stdin.once('data', () => resolve());
    });

    // Execute transfers
    await executeAllTransfers(plan);

    console.log('\n✅ FATE token distribution complete!');
    console.log('📝 CSV file has been updated with transaction hashes for successful transfers.');

    // Final summary including unfunded wallets
    if (unfundedWallets.length > 0) {
      console.log(`\n📋 SUMMARY:`);
      console.log(`  ✅ Successfully planned transfers: ${plan.length}`);
      console.log(`  ❌ Could not fund due to insufficient FATE: ${unfundedWallets.length}`);
      console.log(`\n📝 Unfunded wallets (add more FATE to source wallets to fund these):`);
      for (const wallet of unfundedWallets) {
        console.log(`  ${wallet.address}: needs ${wallet.neededAmountHuman} FATE`);
      }
    }

  } catch (error) {
    console.error('❌ Error in main function:', error);
  }
}

// Run the script
main(); 