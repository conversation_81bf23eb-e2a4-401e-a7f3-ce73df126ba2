const { SuiClient, getFullnodeUrl } = require('@mysten/sui.js/client');
const { Network, TurbosSdk } = require('turbos-clmm-sdk');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { MIST_PER_SUI } = require('@mysten/sui/utils');
const BN = require('bn.js');
require('dotenv').config();

// Cache SDK instances for better performance
const sdkCache = new Map();
const poolCache = new Map();

async function buyTokenTurbos(poolAddress, secretKey, amountToSpend, tokenToBuy, slippage = 0.05) {
    try {
        // Handle keypair
        const keypair = Ed25519Keypair.fromSecretKey(secretKey);
        const address = keypair.getPublicKey().toSuiAddress();

        // Use SDK cache
        let sdk;
        if (sdkCache.has(address)) {
            sdk = sdkCache.get(address);
        } else {
            const client = new SuiClient({ url: "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/" });
            sdk = new TurbosSdk(Network.mainnet, client);
            sdkCache.set(address, sdk);
        }

        console.log(`Buying ${tokenToBuy} with ${amountToSpend} SUI...`);

        // Get pool info
        const pool = await sdk.pool.getPool(poolAddress);
        const poolTypes = sdk.pool.parsePoolType(pool.type, 3);
        const tokenA = poolTypes[0];
        const tokenB = poolTypes[1];

        // Determine swap direction (SUI to token)
        const tokenToSell = '0x2::sui::SUI';
        const a2b = tokenA === tokenToSell;

        console.log(`Pool: ${tokenA} <-> ${tokenB}`);
        console.log(`Direction (a2b): ${a2b}`);

        // Calculate swap
        const swapResult = await sdk.trade.computeSwapResult({
            pools: [{
                pool: poolAddress,
                a2b: a2b
            }],
            address: address,
            amountSpecified: amountToSpend,
            amountSpecifiedIsInput: true
        });

        const result = swapResult[0];
        const nextTickIndex = sdk.math.bitsToNumber(result.tick_current_index.bits);

        console.log(`Expected output: ${a2b ? result.amount_b : result.amount_a}`);

        // Execute swap using swapWithReturn - handles coin objects internally
        const swapResponse = await sdk.trade.swapWithReturn({
            poolId: poolAddress,
            coinType: tokenToSell, // SUI - the coin we're spending
            amountA: a2b ? amountToSpend : result.amount_a,
            amountB: a2b ? result.amount_b : amountToSpend,
            swapAmount: amountToSpend,
            nextTickIndex: nextTickIndex,
            slippage: slippage.toString(),
            amountSpecifiedIsInput: true,
            a2b: a2b,
            address: address,
            deadline: Date.now() + 300000
        });

        // Handle the returned coins properly to avoid UnusedValueWithoutDrop
        const txb = swapResponse.txb;

        // Transfer the output coins to the sender to avoid unused value error
        if (swapResponse.coinVecA) {
            txb.transferObjects([swapResponse.coinVecA], address);
        }
        if (swapResponse.coinVecB) {
            txb.transferObjects([swapResponse.coinVecB], address);
        }

        // Execute transaction using the modified transaction
        const response = await sdk.provider.signAndExecuteTransaction({
            signer: keypair,
            transaction: txb,
            options: {
                showEffects: true,
                showEvents: true,
            }
        });

        console.log(`✅ Swap successful! Digest: ${response.digest}`);
        return response.digest;

    } catch (error) {
        console.error(`❌ Swap failed: ${error.message}`);
        throw error;
    }
}

class TurbosCoinBuyer {
    constructor(privateKey, network = 'mainnet') {
        this.privateKey = privateKey;
        this.keypair = Ed25519Keypair.fromSecretKey(privateKey);
        this.address = this.keypair.getPublicKey().toSuiAddress();

        console.log(`Initialized for address: ${this.address}`);
    }

    async buyCoin(config) {
        const {
            poolAddress,
            tokenToBuy,
            amountToSpend,
            slippage = 0.05
        } = config;

        return await buyTokenTurbos(
            poolAddress,
            this.privateKey,
            amountToSpend,
            tokenToBuy,
            slippage
        );
    }

    async checkBalance(coinType = '0x2::sui::SUI') {
        const client = new SuiClient({ url: "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/" });
        const balance = await client.getBalance({
            owner: this.address,
            coinType: coinType
        });

        if (coinType === '0x2::sui::SUI') {
            console.log(`SUI Balance: ${balance.totalBalance / 1e9} SUI`);
        } else {
            console.log(`Balance: ${balance.totalBalance}`);
        }

        return balance;
    }
}



// Quick buy function like Cetus example
async function quickBuy(poolAddress, privateKey, amountToSpend) {
    let tokenToBuy = process.env.TOKEN_ADDRESS;
    let isSlippage = process.env.SLIPPAGE;
    let amountToBuy = Number(amountToSpend) * Number(MIST_PER_SUI);
    return await buyTokenTurbos(poolAddress, privateKey, amountToBuy, tokenToBuy, isSlippage);
}



module.exports = { TurbosCoinBuyer, buyTokenTurbos, quickBuy };

// Run if called directly
// if (require.main === module) {
//     main();
// }