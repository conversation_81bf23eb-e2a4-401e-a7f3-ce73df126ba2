const fs = require('fs');
const { SuiClient, getFullnodeUrl } = require('@mysten/sui.js/client');
const { Ed25519Keypair } = require('@mysten/sui.js/keypairs/ed25519');
const { TransactionBlock } = require('@mysten/sui.js/transactions');
const { fromB64 } = require('@mysten/sui.js/utils');
const readline = require('readline');

const FATE_TYPE = '0x47c6cba4c841b9312e0607bacf56682e11dcdfaeabb4bd279a46c9942eaaaac8::fate::FATE';
const FATE_COIN_TYPE = `0x2::coin::Coin<${FATE_TYPE}>`;
const SUI_COIN_TYPE = '0x2::coin::Coin<0x2::sui::SUI>';
const WALLETS_FILE = 'wallets1.json';
const SUI_RPC_URL = 'https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/';
const TRANSFER_AMOUNT = 0.1; // 0.1 SUI in human units
const TRANSFER_AMOUNT_RAW = BigInt(TRANSFER_AMOUNT * 1e9); // Convert to raw units

const suiClient = new SuiClient({ url: SUI_RPC_URL });

async function getSuiBalanceForWallet(address) {
  let hasNextPage = true;
  let nextCursor = null;
  let totalBalance = 0n;

  while (hasNextPage) {
    const resp = await suiClient.getOwnedObjects({
      owner: address,
      filter: { StructType: SUI_COIN_TYPE },
      options: { showContent: true },
      cursor: nextCursor,
      limit: 50,
    });

    for (const obj of resp.data) {
      if (
        obj.data &&
        obj.data.content &&
        obj.data.content.fields &&
        obj.data.content.fields.balance
      ) {
        totalBalance += BigInt(obj.data.content.fields.balance);
      }
    }

    hasNextPage = resp.hasNextPage;
    nextCursor = resp.nextCursor;
  }

  return totalBalance;
}

async function getFateBalanceForWallet(address) {
  let hasNextPage = true;
  let nextCursor = null;
  let totalBalance = 0n;

  while (hasNextPage) {
    const resp = await suiClient.getOwnedObjects({
      owner: address,
      filter: { StructType: FATE_COIN_TYPE },
      options: { showContent: true },
      cursor: nextCursor,
      limit: 50,
    });

    for (const obj of resp.data) {
      if (
        obj.data &&
        obj.data.content &&
        obj.data.content.fields &&
        obj.data.content.fields.balance
      ) {
        totalBalance += BigInt(obj.data.content.fields.balance);
      }
    }

    hasNextPage = resp.hasNextPage;
    nextCursor = resp.nextCursor;
  }

  return totalBalance;
}

async function getWalletBalances() {
  const wallets = JSON.parse(fs.readFileSync(WALLETS_FILE, 'utf-8'));
  const balances = [];

  console.log('Getting SUI and FATE balances for all wallets...\n');

  for (const wallet of wallets) {
    const address = wallet.address;
    try {
      const suiBalance = await getSuiBalanceForWallet(address);
      const fateBalance = await getFateBalanceForWallet(address);
      const suiBalanceHuman = Number(suiBalance) / 1e9;
      const fateBalanceHuman = Number(fateBalance) / 1e9;

      balances.push({
        address,
        suiBalance,
        suiBalanceHuman,
        fateBalance,
        fateBalanceHuman,
        privateKey: wallet.privateKey
      });

      console.log(`${address}: ${suiBalanceHuman} SUI, ${fateBalanceHuman} FATE`);
    } catch (err) {
      console.error(`Error getting balance for ${address}:`, err.message);
      balances.push({
        address,
        suiBalance: 0n,
        suiBalanceHuman: 0,
        fateBalance: 0n,
        fateBalanceHuman: 0,
        privateKey: wallet.privateKey
      });
    }
  }

  return balances;
}

function findTransferPlan(balances) {
  // Find wallet with highest SUI balance
  const sourceWallet = balances.reduce((max, wallet) =>
    wallet.suiBalance > max.suiBalance ? wallet : max
  );

  // Find wallets with FATE tokens AND less than 0.1 SUI (excluding the source wallet)
  const targetWallets = balances.filter(wallet =>
    wallet.address !== sourceWallet.address &&
    wallet.fateBalance > 0n &&
    wallet.suiBalanceHuman < TRANSFER_AMOUNT
  );

  return { sourceWallet, targetWallets };
}

async function confirmTransfer(sourceWallet, targetWallets) {
  const totalTransferAmount = targetWallets.length * TRANSFER_AMOUNT;
  const remainingBalance = sourceWallet.suiBalanceHuman - totalTransferAmount;

  console.log('\n==========================================');
  console.log('TRANSFER PLAN');
  console.log('==========================================');
  console.log(`Source wallet: ${sourceWallet.address}`);
  console.log(`Source balance: ${sourceWallet.suiBalanceHuman} SUI`);
  console.log(`Number of target wallets: ${targetWallets.length}`);
  console.log(`Transfer amount per wallet: ${TRANSFER_AMOUNT} SUI`);
  console.log(`Total transfer amount: ${totalTransferAmount} SUI`);
  console.log(`Estimated remaining balance: ${remainingBalance} SUI (excluding gas fees)`);
  console.log('\nTarget wallets (with FATE tokens):');
  targetWallets.forEach(wallet => {
    console.log(`  ${wallet.address} (SUI: ${wallet.suiBalanceHuman}, FATE: ${wallet.fateBalanceHuman})`);
  });

  if (remainingBalance < 0) {
    console.log('\n❌ ERROR: Source wallet does not have enough SUI for all transfers!');
    return false;
  }

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('\nDo you want to proceed with these transfers? (yes/no): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

async function executeTransfers(sourceWallet, targetWallets, keypair) {
  console.log('\nExecuting transfers...\n');

  // Get SUI coins from source wallet
  const coins = await suiClient.getCoins({
    owner: sourceWallet.address,
    coinType: '0x2::sui::SUI',
  });

  if (coins.data.length === 0) {
    throw new Error('No SUI coins found in source wallet');
  }

  // Create transaction block for batch transfer
  const txb = new TransactionBlock();

  // Split coins and transfer to each target wallet
  for (const targetWallet of targetWallets) {
    const coin = txb.splitCoins(txb.gas, [txb.pure(TRANSFER_AMOUNT_RAW)]);
    txb.transferObjects([coin], txb.pure(targetWallet.address));
  }

  try {
    // Execute the transaction
    const result = await suiClient.signAndExecuteTransactionBlock({
      signer: keypair,
      transactionBlock: txb,
      options: {
        showEffects: true,
        showObjectChanges: true,
      },
    });

    console.log('✅ Transfer successful!');
    console.log(`Transaction digest: ${result.digest}`);
    console.log(`Gas used: ${result.effects.gasUsed.computationCost} computation units`);

    return result;
  } catch (error) {
    console.error('❌ Transfer failed:', error.message);
    throw error;
  }
}

async function main() {
  try {
    console.log('SUI Transfer Script (FATE Token Holders Only)');
    console.log('==========================================\n');

    // Step 1: Get all wallet balances (both SUI and FATE)
    const balances = await getWalletBalances();

    // Step 2: Create transfer plan
    const { sourceWallet, targetWallets } = findTransferPlan(balances);

    if (targetWallets.length === 0) {
      console.log('\n✅ No wallets with FATE tokens need SUI transfers (all already have 0.1+ SUI).');
      return;
    }

    // Step 3: Show plan and get confirmation
    const confirmed = await confirmTransfer(sourceWallet, targetWallets);
    if (!confirmed) {
      console.log('\n❌ Transfer cancelled.');
      return;
    }

    // Step 4: Create keypair from the source wallet's private key
    console.log(`\nUsing private key from wallets1.json for source wallet: ${sourceWallet.address}`);

    let keypair;
    try {
      // Use the hex privateKey from the wallet data (remove 0x prefix if present)
      let privateKeyHex = sourceWallet.privateKey;
      if (privateKeyHex.startsWith('0x')) {
        privateKeyHex = privateKeyHex.slice(2);
      }

      console.log(`Attempting to decode hex private key (length: ${privateKeyHex.length})`);

      // Convert hex string to Uint8Array
      const privateKeyBytes = new Uint8Array(privateKeyHex.match(/.{1,2}/g).map(byte => parseInt(byte, 16)));

      if (privateKeyBytes.length !== 32) {
        throw new Error(`Invalid private key length: expected 32 bytes, got ${privateKeyBytes.length}`);
      }

      keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
    } catch (error) {
      console.error('❌ Failed to decode private key:', error.message);
      return;
    }

    // Verify the keypair matches the source wallet
    const derivedAddress = keypair.getPublicKey().toSuiAddress();
    if (derivedAddress !== sourceWallet.address) {
      console.error(`❌ Private key does not match source wallet address.`);
      console.error(`Expected: ${sourceWallet.address}`);
      console.error(`Got: ${derivedAddress}`);
      return;
    }

    await executeTransfers(sourceWallet, targetWallets, keypair);

    console.log('\n✅ All transfers to FATE token holders completed successfully!');

  } catch (error) {
    console.error('❌ Script failed:', error.message);
    process.exit(1);
  }
}

main(); 