const { SuiClient, getFullnodeUrl } = require('@mysten/sui.js/client');
const { Network, TurbosSdk } = require('turbos-clmm-sdk');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');

// Cache SDK instances for better performance
const sdkCache = new Map();

async function sellTokenTurbos(poolAddress, secretKey, amountToSell, tokenToSell, slippage = 0.05) {
    try {
        // Handle keypair
        const keypair = Ed25519Keypair.fromSecretKey(secretKey);
        const address = keypair.getPublicKey().toSuiAddress();

        // Use SDK cache
        let sdk;
        if (sdkCache.has(address)) {
            sdk = sdkCache.get(address);
        } else {
            const client = new SuiClient({ url: "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/" });
            sdk = new TurbosSdk(Network.mainnet, client);
            sdkCache.set(address, sdk);
        }

        console.log(`Selling ${amountToSell} of ${tokenToSell} for SUI...`);

        // Get pool info
        const pool = await sdk.pool.getPool(poolAddress);
        const poolTypes = sdk.pool.parsePoolType(pool.type, 3);
        const tokenA = poolTypes[0];
        const tokenB = poolTypes[1];

        // Determine swap direction (token to SUI)
        const targetToken = '0x2::sui::SUI';
        const a2b = tokenA === tokenToSell; // We're selling tokenToSell for SUI

        console.log(`Pool: ${tokenA} <-> ${tokenB}`);
        console.log(`Direction (a2b): ${a2b}`);
        console.log(`Selling ${tokenToSell} for ${targetToken}`);

        // Calculate swap
        const swapResult = await sdk.trade.computeSwapResult({
            pools: [{
                pool: poolAddress,
                a2b: a2b
            }],
            address: address,
            amountSpecified: Number(amountToSell),
            amountSpecifiedIsInput: true
        });

        const result = swapResult[0];
        const nextTickIndex = sdk.math.bitsToNumber(result.tick_current_index.bits);

        console.log(`Expected SUI output: ${a2b ? result.amount_b : result.amount_a}`);

        // Execute swap using swapWithReturn - handles coin objects internally
        const swapResponse = await sdk.trade.swapWithReturn({
            poolId: poolAddress,
            coinType: tokenToSell, // The token we're selling
            amountA: a2b ? amountToSell : result.amount_a,
            amountB: a2b ? result.amount_b : amountToSell,
            swapAmount: amountToSell,
            nextTickIndex: nextTickIndex,
            slippage: slippage.toString(),
            amountSpecifiedIsInput: true,
            a2b: a2b,
            address: address,
            deadline: Date.now() + 300000
        });

        // Handle the returned coins properly to avoid UnusedValueWithoutDrop
        const txb = swapResponse.txb;

        // Transfer the output coins to the sender to avoid unused value error
        if (swapResponse.coinVecA) {
            txb.transferObjects([swapResponse.coinVecA], address);
        }
        if (swapResponse.coinVecB) {
            txb.transferObjects([swapResponse.coinVecB], address);
        }

        // Execute transaction using the modified transaction
        const response = await sdk.provider.signAndExecuteTransaction({
            signer: keypair,
            transaction: txb,
            options: {
                showEffects: true,
                showEvents: true,
            }
        });

        console.log(`✅ Sell successful! Digest: ${response.digest}`);
        return response.digest;

    } catch (error) {
        console.error(`❌ Sell failed: ${error.message}`);
        throw error;
    }
}

class TurbosCoinSeller {
    constructor(privateKey, network = 'mainnet') {
        this.privateKey = privateKey;
        this.keypair = Ed25519Keypair.fromSecretKey(privateKey);
        this.address = this.keypair.getPublicKey().toSuiAddress();

        console.log(`Initialized seller for address: ${this.address}`);
    }

    async sellCoin(config) {
        const {
            poolAddress,
            tokenToSell,
            amountToSell,
            slippage = 0.05
        } = config;

        return await sellTokenTurbos(
            poolAddress,
            this.privateKey,
            amountToSell,
            tokenToSell,
            slippage
        );
    }

    async sellAllTokens(config) {
        const {
            poolAddress,
            tokenToSell,
            slippage = 0.05,
            keepAmount = 0 // Amount to keep (don't sell everything)
        } = config;

        // Get current balance first
        const balance = await this.checkBalance(tokenToSell);
        const totalBalance = parseInt(balance.totalBalance);

        if (totalBalance <= keepAmount) {
            throw new Error(`Insufficient balance. Have: ${totalBalance}, Keep: ${keepAmount}`);
        }

        const amountToSell = totalBalance - keepAmount;
        console.log(`Selling ${amountToSell} out of ${totalBalance} tokens`);

        return await sellTokenTurbos(
            poolAddress,
            this.privateKey,
            amountToSell.toString(),
            tokenToSell,
            slippage
        );
    }

    async checkBalance(coinType = '0x2::sui::SUI') {
        const client = new SuiClient({ url: "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/" });
        const balance = await client.getBalance({
            owner: this.address,
            coinType: coinType
        });

        if (coinType === '0x2::sui::SUI') {
            console.log(`SUI Balance: ${balance.totalBalance / 1e9} SUI`);
        } else {
            console.log(`${coinType} Balance: ${balance.totalBalance}`);
        }

        return balance;
    }

    // Get all coin balances for the address
    async getAllBalances() {
        const client = new SuiClient({ url: "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/" });
        const balances = await client.getAllBalances({
            owner: this.address
        });

        console.log('All token balances:');
        balances.forEach(balance => {
            if (balance.coinType === '0x2::sui::SUI') {
                console.log(`  SUI: ${balance.totalBalance / 1e9} SUI`);
            } else {
                console.log(`  ${balance.coinType}: ${balance.totalBalance}`);
            }
        });

        return balances;
    }
}

// Example usage
// async function main() {
//     const PRIVATE_KEY = '';

//     try {
//         // Method 1: Using the function directly
//         const result = await sellTokenTurbos(
//             '0x6a3be30a31f88d9055da7f26f53dd34c85bc5aab9028212361ccf67f5f00fd46',
//             PRIVATE_KEY,
//             '1000000', // Amount of tokens to sell
//             '0xfa7ac3951fdca92c5200d468d31a365eb03b2be9936fde615e69f0c1274ad3a0::BLUB::BLUB',
//             0.05
//         );
//         console.log(`Transaction: https://suiscan.xyz/mainnet/tx/${result}`);

//         // Method 2: Using the class
//         /*
//         const seller = new TurbosCoinSeller(PRIVATE_KEY);

//         // Check balances first
//         await seller.getAllBalances();

//         // Sell specific amount
//         const result2 = await seller.sellCoin({
//             poolAddress: '0xea75ce260cf3706f36564c1a432eadd1a45ba8fca62dc91401360b3d587fc4fc',
//             tokenToSell: '0xf325ce1300e8dac124071d3152c5c5ee6174914f8bc2161e88329cf579246efc::afsui::AFSUI',
//             amountToSell: '1000000',
//             slippage: 0.05
//         });

//         // Or sell all tokens (minus keep amount)
//         const result3 = await seller.sellAllTokens({
//             poolAddress: '0xea75ce260cf3706f36564c1a432eadd1a45ba8fca62dc91401360b3d587fc4fc',
//             tokenToSell: '0xf325ce1300e8dac124071d3152c5c5ee6174914f8bc2161e88329cf579246efc::afsui::AFSUI',
//             keepAmount: 0, // Sell everything
//             slippage: 0.05
//         });
//         */

//     } catch (error) {
//         console.error('Failed:', error.message);
//     }
// }

// Quick sell function
async function quickSell(privateKey, poolAddress, tokenToSell, amountToSell, slippage = 0.05) {
    return await sellTokenTurbos(poolAddress, privateKey, amountToSell, tokenToSell, slippage);
}

// Quick sell all function
async function quickSellAll(privateKey, poolAddress, tokenToSell, slippage = 0.05, keepAmount = 0) {
    const seller = new TurbosCoinSeller(privateKey);
    return await seller.sellAllTokens({
        poolAddress,
        tokenToSell,
        slippage,
        keepAmount
    });
}

module.exports = { TurbosCoinSeller, sellTokenTurbos, quickSell, quickSellAll };

// Run if called directly
// if (require.main === module) {
//     main();
// }