require('dotenv').config();

const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { Transaction } = require('@mysten/sui/transactions');
const { Network, TurbosSdk } = require('turbos-clmm-sdk');

// Define colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    magenta: '\x1b[35m',
    white: '\x1b[37m'
};

async function removeLiquidityFromPool(tokenAddress = null) {
    try {
        // Use provided token address or fall back to env variable
        const targetTokenAddress = tokenAddress || process.env.TOKEN_ADDRESS;
        const poolAddress = process.env.POOL_ADDRESS;

        if (!poolAddress) {
            throw new Error('POOL_ADDRESS not found in environment variables');
        }

        if (!targetTokenAddress) {
            console.log(colors.yellow + 'No token address provided.' + colors.reset);
            throw new Error('Token address is required');
        } else {
            console.log(colors.cyan + `Target token address: ${targetTokenAddress}` + colors.reset);
            console.log(colors.cyan + `Pool address: ${poolAddress}` + colors.reset);
        }

        // Initialize clients
        const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
        console.log(colors.cyan + 'Using fullnode URL:' + colors.reset, fullNodeUrl);

        const client = new SuiClient({ url: fullNodeUrl });
        const privateKey = process.env.PRIVATE_KEY;

        if (!privateKey) {
            throw new Error('PRIVATE_KEY not found in environment variables');
        }

        // Initialize keypair directly
        const keypair = Ed25519Keypair.fromSecretKey(privateKey);
        const address = keypair.getPublicKey().toSuiAddress();
        console.log(colors.cyan + `Using wallet address: ${address}` + colors.reset);

        // Initialize Turbo SDK with network enum
        const sdk = new TurbosSdk(Network.mainnet, client);
        console.log(colors.green + 'Turbo SDK initialized' + colors.reset);

        // Get pool and vault details
        try {
            // Get user's vaults
            const myVaults = await sdk.vault.getMyVaults(address);
            console.log(colors.cyan + 'User vaults:' + colors.reset, JSON.stringify(myVaults, null, 2));

            // Find the vault for this pool
            const userVault = myVaults.find(vault => vault.clmm_pool_id === poolAddress);

            if (!userVault) {
                throw new Error('No vault found for this pool');
            }

            console.log(colors.cyan + 'Found vault:' + colors.reset, JSON.stringify(userVault, null, 2));

            // Create a new transaction
            const tx = new Transaction({
                sender: address,
                gasConfig: {
                    budget: 100000000,
                    price: 1000,
                    payment: []
                }
            });

            // Prepare withdraw vault parameters (withdraw 10% of position)
            const withdrawParams = {
                strategyId: userVault.strategyId,
                vaultId: userVault.vaultId,
                poolId: poolAddress,
                address: address,
                percentage: 100000, // 10% = 100000 (percentage is in parts per million)
                slippage: '1',
                deadline: Math.floor(Date.now() / 1000) + 60 * 60, // 1 hour deadline
                transaction: tx
            };

            console.log(colors.cyan + 'Preparing to withdraw from vault with params:' + colors.reset, JSON.stringify({
                ...withdrawParams,
                transaction: 'Transaction Object' // Just for logging
            }, null, 2));

            // Create withdraw transaction
            const txResult = await sdk.vault.withdrawVaultV2(withdrawParams);

            if (!txResult) {
                throw new Error('Failed to create withdraw transaction');
            }

            console.log(colors.green + 'Transaction created successfully, executing...' + colors.reset);

            // Execute the transaction
            const result = await client.signAndExecuteTransaction({
                signer: keypair,
                transaction: txResult,
                options: {
                    showEffects: true,
                    showEvents: true,
                    showObjectChanges: true,
                    showInput: true,
                    gasConfig: {
                        budget: 100000000
                    }
                }
            });

            // Check transaction result
            if (result.effects?.status?.status === 'success') {
                console.log(colors.green + 'Successfully removed liquidity!' + colors.reset);
                console.log(colors.cyan + `TX: https://suivision.xyz/txblock/${result.digest}` + colors.reset);
                return result;
            } else {
                console.error(colors.red + 'Transaction failed:' + colors.reset, result.effects?.status);
                console.error('Full Effects:', JSON.stringify(result.effects, null, 2));
                throw new Error('Failed to remove liquidity');
            }

        } catch (error) {
            console.error(colors.red + 'Error in removeLiquidityFromPool:' + colors.reset, error);
            throw error;
        }

    } catch (error) {
        console.error(colors.red + 'Error in removeLiquidityFromPool:' + colors.reset, error);
        throw error;
    }
}

// Only call the function if this file is being run directly
// if (require.main === module) {
//     removeLiquidityFromPool("0x4d09b319b71a045134d4e8044ba6ef67e02fa9802e8b5860b50384c4a1d271ee::sheh::SHEH")
//         .then(() => process.exit(0))
//         .catch(error => {
//             console.error(colors.red + 'Failed to remove liquidity:' + colors.reset, error);
//             process.exit(1);
//         });
// }

module.exports = removeLiquidityFromPool;