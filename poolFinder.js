const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');

async function getPoolIdFromTxHash(txHash, retries = 3, delay = 2000) {
    // Initialize clients
    const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
    const client = new SuiClient({ url: fullNodeUrl });

    for (let attempt = 0; attempt < retries; attempt++) {
        try {
            console.log(`Attempt ${attempt + 1}/${retries} to get pool ID from transaction ${txHash}`);

            // Fetch transaction
            const transaction = await client.getTransactionBlock({
                digest: txHash,
                options: {
                    showEvents: true,
                    showInput: true,
                    showEffects: true,
                    showObjectChanges: true,
                    showBalanceChanges: true,
                }
            });

            // First, look specifically for Turbos pool in created objects
            if (transaction && transaction.objectChanges) {
                const turbosPool = transaction.objectChanges.find(change =>
                    change.type === 'created' &&
                    change.objectType &&
                    change.objectType.includes('0x91bfbc386a41afcfd9b2533058d7e915a1d3829089cc268ff4333d54d6339ca1::pool::Pool')
                );

                if (turbosPool) {
                    console.log(`Found Turbos pool ID: ${turbosPool.objectId}`);
                    return turbosPool.objectId;
                }
            }

            // Look for pool creation event as backup
            if (transaction && transaction.events) {
                const poolCreatedEvent = transaction.events.find(event =>
                    event.type &&
                    event.type.includes('0x91bfbc386a41afcfd9b2533058d7e915a1d3829089cc268ff4333d54d6339ca1::pool_factory::PoolCreatedEvent')
                );

                if (poolCreatedEvent && poolCreatedEvent.parsedJson && poolCreatedEvent.parsedJson.pool_id) {
                    console.log(`Found pool ID from event: ${poolCreatedEvent.parsedJson.pool_id}`);
                    return poolCreatedEvent.parsedJson.pool_id;
                }
            }

            console.log("Pool ID not found in transaction data, waiting before retry...");

            if (attempt < retries - 1) {
                await new Promise(resolve => setTimeout(resolve, delay));
            }

        } catch (error) {
            console.error(`Error fetching transaction (attempt ${attempt + 1}/${retries}):`, error.message);

            if (attempt < retries - 1) {
                console.log(`Waiting ${delay}ms before retrying...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay = Math.min(delay * 2, 10000);
            }
        }
    }

    console.log("Failed to get pool ID after multiple attempts.");
    return null;
}

module.exports = getPoolIdFromTxHash;

//Example usage with your transaction hash
// const txHash = 'GmUvv9iTzMGqEcLop4AkWiFLK9TuPGnpSFS45ujJTxDH';
// getPoolIdFromTxHash(txHash)
//   .then(poolId => {
//     if (poolId) {
//       // Just print the pool ID and nothing else
//       console.log(poolId);
//     }
//   })
//   .catch(error => console.error('Failed to get Pool ID:', error));