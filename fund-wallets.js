// Dynamic Wallet Funding Tool
// This tool funds wallets based on the buyAmount values in wallets.json

require('dotenv').config();
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');
const { Transaction } = require('@mysten/sui/transactions');
const { MIST_PER_SUI } = require('@mysten/sui/utils');
const fs = require('fs').promises;
const readline = require('readline');

// Initialize SUI client
const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
const client = new SuiClient({ url: fullNodeUrl });

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function for prompts
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

// ANSI colors for better readability
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Function to check master wallet balance
async function checkMasterWalletBalance() {
  try {
    // Validate that PRIVATE_KEY exists in environment
    if (!process.env.PRIVATE_KEY) {
      throw new Error('PRIVATE_KEY not found in .env file');
    }

    console.log('Debug: PRIVATE_KEY format in .env:', process.env.PRIVATE_KEY.substring(0, 15) + '...');

    // Create keypair from private key
    // The private key in .env may be in different formats - handle both possibilities
    let keypair;
    try {
      // First try to parse as is
      keypair = Ed25519Keypair.fromSecretKey(process.env.PRIVATE_KEY);
    } catch (e) {
      console.log('Debug: First attempt failed, trying alternate format');
    }
    // Check if it's a Sui base64 format (starts with suiprivkey1)
    //   if (process.env.PRIVATE_KEY.startsWith('suiprivkey1')) {
    //     const base64Part = process.env.PRIVATE_KEY.substring(11); // Remove 'suiprivkey1' prefix
    //     const privateKeyBytes = Buffer.from(base64Part, 'base64');
    //     keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
    //   } else {
    //     // Handle hex format if needed
    //     console.log('Debug: Trying to parse as hex string');
    //     const privateKeyBytes = Buffer.from(process.env.PRIVATE_KEY, 'hex');
    //     keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
    //   }
    // }

    // Get the address from the keypair
    const address = keypair.getPublicKey().toSuiAddress();
    console.log('Debug: Generated address:', address);

    // Fetch balance
    const balance = await client.getBalance({ owner: address });
    const suiBalance = Number.parseInt(balance.totalBalance) / Number(MIST_PER_SUI);

    return { address, balance: suiBalance };
  } catch (error) {
    console.log('Debug: Error in checkMasterWalletBalance:', error.message);
    throw new Error(`Error checking master wallet balance: ${error.message}`);
  }
}

// Function to load wallets from wallets.json
async function loadWallets() {
  try {
    const data = await fs.readFile('wallets.json', 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`Could not read wallets.json: ${error.message}`);
  }
}

// Function to fund a single wallet
async function fundWallet(wallet, actualAmount, masterKeypair, retries = 3) {
  try {
    // Skip wallets with zero or undefined buyAmount
    if (!actualAmount || actualAmount <= 0) {
      return {
        address: wallet.address,
        status: 'skipped',
        message: 'No buyAmount configured'
      };
    }

    // Convert SUI to MIST (multiply by 10^9)
    const amountInMist = Math.floor(actualAmount * Number(MIST_PER_SUI));

    // Create transaction
    const tx = new Transaction();
    const [coin] = tx.splitCoins(tx.gas, [amountInMist]);
    tx.transferObjects([coin], wallet.address);

    // Execute with retries
    let attempt = 0;
    let lastError;

    while (attempt < retries) {
      try {
        const txResult = await client.signAndExecuteTransaction({
          signer: masterKeypair,
          transaction: tx
        });

        return {
          address: wallet.address,
          status: 'success',
          amount: actualAmount,
          buyAmount: wallet.buyAmount,
          bonus: (actualAmount - wallet.buyAmount).toFixed(6),
          txHash: txResult.digest
        };
      } catch (error) {
        lastError = error;
        attempt++;

        if (attempt < retries) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000 + Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    return {
      address: wallet.address,
      status: 'failed',
      message: lastError.message
    };
  } catch (error) {
    return {
      address: wallet.address,
      status: 'failed',
      message: error.message
    };
  }
}

// Main function to fund all wallets
async function fundWallets() {
  console.log(`${colors.bright}${colors.cyan}===== DYNAMIC WALLET FUNDING TOOL =====\n${colors.reset}`);

  try {
    // Ask about adding extra SUI
    const addExtraSui = await question(`${colors.yellow}Add 1 extra SUI to each wallet? (y/n): ${colors.reset}`);
    const extraSuiAmount = (addExtraSui.toLowerCase() === 'y' || addExtraSui.toLowerCase() === 'yes') ? 1.0 : 0;

    // Check master wallet
    console.log(`${colors.yellow}Checking master wallet...${colors.reset}`);
    const masterWallet = await checkMasterWalletBalance();
    console.log(`${colors.green}✓ Master wallet: ${masterWallet.address}${colors.reset}`);
    console.log(`${colors.green}✓ Balance: ${masterWallet.balance.toFixed(6)} SUI${colors.reset}`);

    // Load wallets
    console.log(`\n${colors.yellow}Loading wallets from wallets.json...${colors.reset}`);
    const wallets = await loadWallets();
    console.log(`${colors.green}✓ Loaded ${wallets.length} wallets${colors.reset}`);

    // Calculate total required funding
    const walletsToFund = wallets.filter(w => w.buyAmount && w.buyAmount > 0);
    const extraSuiTotal = walletsToFund.length * extraSuiAmount;
    const baseRequired = walletsToFund.reduce((sum, w) => sum + (w.buyAmount || 0), 0);
    const totalRequired = baseRequired + extraSuiTotal;

    console.log(`\n${colors.cyan}Found ${walletsToFund.length} wallets to fund${colors.reset}`);
    console.log(`${colors.cyan}Base SUI required: ${baseRequired.toFixed(6)} SUI${colors.reset}`);

    if (extraSuiAmount > 0) {
      console.log(`${colors.cyan}Extra SUI (${extraSuiAmount} per wallet): ${extraSuiTotal.toFixed(6)} SUI${colors.reset}`);
    }

    console.log(`${colors.cyan}Total SUI required: ${totalRequired.toFixed(6)} SUI${colors.reset}`);

    // Check if master wallet has enough balance
    if (masterWallet.balance < totalRequired) {
      console.log(`${colors.red}⚠ WARNING: Master wallet balance (${masterWallet.balance.toFixed(6)} SUI) is less than required (${totalRequired.toFixed(6)} SUI)${colors.reset}`);
      const proceed = await question(`${colors.yellow}Do you want to proceed anyway? (y/n): ${colors.reset}`);

      if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
        console.log(`${colors.yellow}Operation cancelled by user${colors.reset}`);
        rl.close();
        return;
      }
    }

    // Confirm funding
    const confirm = await question(`${colors.yellow}Ready to fund ${walletsToFund.length} wallets${extraSuiAmount > 0 ? ` with ${extraSuiAmount} extra SUI each` : ''}. Proceed? (y/n): ${colors.reset}`);

    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log(`${colors.yellow}Operation cancelled by user${colors.reset}`);
      rl.close();
      return;
    }

    // Create keypair from the master private key
    console.log(`${colors.yellow}Preparing master wallet for transactions...${colors.reset}`);

    // Parse the private key with the same logic as in checkMasterWalletBalance
    let masterKeypair;
    try {
      // First try to parse as is
      masterKeypair = Ed25519Keypair.fromSecretKey(process.env.PRIVATE_KEY);
    } catch (e) {
      // Check if it's a Sui base64 format (starts with suiprivkey1)
      if (process.env.PRIVATE_KEY.startsWith('suiprivkey1')) {
        const base64Part = process.env.PRIVATE_KEY.substring(11); // Remove 'suiprivkey1' prefix
        const privateKeyBytes = Buffer.from(base64Part, 'base64');
        masterKeypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
      } else {
        // Handle hex format if needed
        const privateKeyBytes = Buffer.from(process.env.PRIVATE_KEY, 'hex');
        masterKeypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
      }
    }

    // Verify the keypair worked by getting its address
    const masterAddress = masterKeypair.getPublicKey().toSuiAddress();
    console.log(`${colors.green}✓ Using master wallet: ${masterAddress} for transactions${colors.reset}`);
    if (masterAddress !== masterWallet.address) {
      console.log(`${colors.yellow}⚠ WARNING: Address mismatch between balance check and transaction signing!${colors.reset}`);
      console.log(`${colors.yellow}  Balance checked: ${masterWallet.address}${colors.reset}`);
      console.log(`${colors.yellow}  Transaction signing: ${masterAddress}${colors.reset}`);
      const proceedDespiteMismatch = await question(`${colors.yellow}Proceed anyway? (y/n): ${colors.reset}`);
      if (proceedDespiteMismatch.toLowerCase() !== 'y' && proceedDespiteMismatch.toLowerCase() !== 'yes') {
        console.log(`${colors.yellow}Operation cancelled due to address mismatch${colors.reset}`);
        rl.close();
        return;
      }
    }

    // Fund wallets sequentially to avoid nonce issues
    console.log(`\n${colors.cyan}Starting funding process...${colors.reset}`);

    const results = [];
    for (let i = 0; i < wallets.length; i++) {
      const wallet = wallets[i];

      if (!wallet.buyAmount || wallet.buyAmount <= 0) {
        console.log(`${colors.dim}Skipping wallet ${i + 1}/${wallets.length} (${wallet.address.substring(0, 8)}...): No buyAmount${colors.reset}`);
        results.push({
          address: wallet.address,
          status: 'skipped',
          message: 'No buyAmount configured'
        });
        continue;
      }

      // Calculate actual amount to send (base + extra)
      const actualAmount = wallet.buyAmount + extraSuiAmount;

      console.log(`${colors.yellow}Funding wallet ${i + 1}/${wallets.length} (${wallet.address.substring(0, 8)}...): ${actualAmount.toFixed(6)} SUI ${extraSuiAmount > 0 ? `(includes ${extraSuiAmount} extra SUI)` : ''}${colors.reset}`);

      const result = await fundWallet(wallet, actualAmount, masterKeypair);
      results.push(result);

      if (result.status === 'success') {
        console.log(`${colors.green}✓ Successfully funded: ${result.amount.toFixed(6)} SUI${colors.reset}`);
        if (extraSuiAmount > 0) {
          console.log(`${colors.green}  Base: ${result.buyAmount.toFixed(6)} SUI + Bonus: ${result.bonus} SUI${colors.reset}`);
        }
        console.log(`${colors.dim}  Transaction: https://suiscan.xyz/mainnet/tx/${result.txHash}${colors.reset}`);
      } else {
        console.log(`${colors.red}✗ Failed: ${result.message}${colors.reset}`);
      }

      // Add a small delay between transactions to avoid rate limits
      if (i < wallets.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Summary
    const successful = results.filter(r => r.status === 'success').length;
    const failed = results.filter(r => r.status === 'failed').length;
    const skipped = results.filter(r => r.status === 'skipped').length;

    console.log(`\n${colors.bright}${colors.cyan}===== FUNDING SUMMARY =====\n${colors.reset}`);
    console.log(`${colors.green}✓ Successfully funded: ${successful} wallets${colors.reset}`);

    // Calculate total SUI sent
    const totalSent = results.reduce((sum, r) => sum + (r.status === 'success' ? r.amount : 0), 0);
    console.log(`${colors.green}✓ Total SUI sent: ${totalSent.toFixed(6)} SUI${colors.reset}`);

    if (extraSuiAmount > 0) {
      const totalExtra = successful * extraSuiAmount;
      console.log(`${colors.green}✓ Total extra SUI sent: ${totalExtra.toFixed(6)} SUI${colors.reset}`);
    }

    if (failed > 0) {
      console.log(`${colors.red}✗ Failed to fund: ${failed} wallets${colors.reset}`);
    }

    if (skipped > 0) {
      console.log(`${colors.yellow}ℹ Skipped (no buyAmount): ${skipped} wallets${colors.reset}`);
    }

    console.log(`\n${colors.green}Funding operation complete!${colors.reset}`);

  } catch (error) {
    console.error(`${colors.red}Error: ${error.message}${colors.reset}`);
  } finally {
    rl.close();
  }
}

// Run the script
fundWallets().catch(error => {
  console.error(`${colors.red}Fatal error: ${error.message}${colors.reset}`);
  process.exit(1);
}); 