require('dotenv').config();

const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { TransactionBlock } = require('@mysten/sui/transactions');
const { initCetusSDK, Percentage, d, TickMath } = require('@cetusprotocol/cetus-sui-clmm-sdk');
const BN = require('bn.js');

async function createPoolWithExactAmounts(tokenAddress, suiAmount, tokenAmount) {
    try {
        // Initialize clients
        console.log("Initializing client connections...");
        const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
        const client = new SuiClient({ url: fullNodeUrl });
        const privateKey = process.env.PRIVATE_KEY;

        if (!privateKey) {
            throw new Error("PRIVATE_KEY not set in .env file");
        }

        // Parse private key correctly
        let keypairData;
        if (privateKey.startsWith('0x')) {
            keypairData = Buffer.from(privateKey.substring(2), 'hex');
        } else if (privateKey.startsWith('suiprivkey1')) {
            keypairData = Buffer.from(privateKey.substring(11), 'base64');
        } else {
            keypairData = Buffer.from(privateKey, 'hex');
        }

        const keypair = Ed25519Keypair.fromSecretKey(keypairData);
        const address = keypair.getPublicKey().toSuiAddress();
        console.log('Using wallet address:', address);

        // Define token types
        const TOKEN_TYPE = tokenAddress;
        const SUI_TYPE = '0x2::sui::SUI';

        // Verify token exists
        console.log(`Verifying token type: ${TOKEN_TYPE}`);
        try {
            const tokenMetadata = await client.getCoinMetadata({ coinType: TOKEN_TYPE });
            console.log(`Token metadata:`, tokenMetadata);

            if (!tokenMetadata) {
                throw new Error(`Token type ${TOKEN_TYPE} not found`);
            }

            // Get token decimals
            const tokenDecimals = tokenMetadata.decimals;
            console.log(`Token decimals: ${tokenDecimals}`);

            // Initialize SDK with explicit parameters
            const sdkConfig = {
                network: 'mainnet',
                fullNodeUrl,
                faucetURL: "",
                simulationAccount: {
                    address
                }
            };

            console.log("Initializing Cetus SDK...");
            const sdk = await initCetusSDK(sdkConfig);
            sdk.senderAddress = address;

            // Calculate prices and ticks
            const initialPrice = suiAmount / tokenAmount;
            console.log(`Initial price ratio: ${initialPrice} SUI per token`);

            // Converting to raw values
            const suiDecimals = 9; // SUI always has 9 decimals
            const rawSuiAmount = BigInt(Math.floor(suiAmount * 10 ** suiDecimals));
            const rawTokenAmount = BigInt(Math.floor(tokenAmount * 10 ** tokenDecimals));

            console.log(`Raw SUI amount: ${rawSuiAmount}`);
            console.log(`Raw token amount: ${rawTokenAmount}`);

            // Calculate sqrt price
            const sqrtPriceX64 = TickMath.priceToSqrtPriceX64(
                d(initialPrice),
                tokenDecimals,
                suiDecimals
            ).toString();

            console.log(`Calculated sqrtPriceX64: ${sqrtPriceX64}`);

            // Calculate current tick based on sqrt price
            const currentTickIndex = TickMath.sqrtPriceX64ToTickIndex(new BN(sqrtPriceX64));
            console.log(`Current tick index: ${currentTickIndex.toString()}`);

            // Use a wider tick range to ensure the price fits
            const tickSpacing = 2000;

            // Calculate a very wide range around the current tick
            const tickLower = TickMath.getPrevInitializableTickIndex(
                Math.round(currentTickIndex.toNumber() - 100000),
                tickSpacing
            );

            const tickUpper = TickMath.getNextInitializableTickIndex(
                Math.round(currentTickIndex.toNumber() + 100000),
                tickSpacing
            );

            console.log(`Using tick range: ${tickLower} to ${tickUpper} with spacing ${tickSpacing}`);

            // Create pool data structure
            const poolData = {
                coinTypeA: TOKEN_TYPE,
                coinTypeB: SUI_TYPE,
                tick_spacing: tickSpacing,
                initialize_sqrt_price: sqrtPriceX64,
                uri: '',
                amount_a: rawTokenAmount.toString(),
                amount_b: rawSuiAmount.toString(),
                fix_amount_a: true,
                fix_amount_b: true,
                tick_lower: tickLower,
                tick_upper: tickUpper
            };

            console.log("Preparing to create pool with parameters:", JSON.stringify(poolData, null, 2));

            // Build transaction
            console.log("Building transaction...");
            const txb = await sdk.Pool.createPoolAndAddLiquidity(poolData);

            // Execute with explicit gas budget
            console.log("Executing transaction...");
            const result = await client.signAndExecuteTransaction({
                signer: keypair,
                transaction: txb,
                options: {
                    showEffects: true,
                    showEvents: true,
                    gasBudget: 200000000 // 0.2 SUI
                }
            });

            // Process result
            if (result.effects?.status?.status === 'success') {
                console.log("SUCCESS! Pool created successfully!");
                console.log(`Transaction hash: ${result.digest}`);
                console.log(`Transaction URL: https://suivision.xyz/txblock/${result.digest}`);

                // Try to find pool ID from events
                if (result.events && result.events.length > 0) {
                    const poolCreatedEvent = result.events.find(e =>
                        e.type && e.type.includes('PoolCreated'));

                    if (poolCreatedEvent && poolCreatedEvent.parsedJson) {
                        console.log("Pool ID:", poolCreatedEvent.parsedJson.pool_id);
                    }
                }

                return result;
            } else {
                console.error("Transaction failed:");
                console.error(JSON.stringify(result.effects, null, 2));
                throw new Error("Transaction failed");
            }

        } catch (err) {
            console.error(`Error verifying token: ${err.message}`);
            throw err;
        }

    } catch (error) {
        console.error("ERROR:", error);
        throw error;
    }
}

// Set the values you want
const TOKEN_ADDRESS = process.argv[2] || "0x15d9a2717e594804c7a00665907395ee3eb695b9106eabdf320c3ddaa6c76585::abc::ABC"; // Token type 
const SUI_AMOUNT = process.argv[3] ? parseFloat(process.argv[3]) : 400; // SUI amount
const TOKEN_AMOUNT = process.argv[4] ? parseFloat(process.argv[4]) : 1_000_000_000; // Token amount

// Run the creation
console.log(`Creating pool for ${TOKEN_ADDRESS} with ${SUI_AMOUNT} SUI and ${TOKEN_AMOUNT} tokens...`);
createPoolWithExactAmounts(TOKEN_ADDRESS, SUI_AMOUNT, TOKEN_AMOUNT)
    .then(() => {
        console.log("Pool creation complete!");
        process.exit(0);
    })
    .catch(err => {
        console.error("Pool creation failed:", err);
        process.exit(1);
    }); 