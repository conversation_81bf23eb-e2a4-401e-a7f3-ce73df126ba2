const { Ed25519Keypair, Ed25519<PERSON><PERSON><PERSON><PERSON><PERSON> } = require('@mysten/sui/keypairs/ed25519');
const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client')
const { HopApi } = require('@hop.ag/sdk');
const { Transaction } = require('@mysten/sui/transactions');
require('dotenv').config();
const addTree = require('./tree')

class SuiHopSwap {
    constructor(privateKey, apiKey = '') {
        // Initialize with private key
        this.keypair = Ed25519Keypair.fromSecretKey(privateKey);
        this.address = this.keypair.getPublicKey().toSuiAddress();

        // Initialize Sui client
        const rpcUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
        this.client = new SuiClient({ url: rpcUrl });

        // Initialize Hop SDK
        const hopApiOptions = {
            api_key: apiKey,
            fee_bps: 0,
            fee_wallet: this.address,
            charge_fees_in_sui: true
        };

        this.sdk = new HopApi(rpcUrl, hopApiOptions);
    }

    async getQuote(tokenIn, tokenOut, amountIn) {
        try {
            const quote = await this.sdk.fetchQuote({
                token_in: tokenIn,
                token_out: tokenOut,
                amount_in: amountIn
            });

            return quote;
        } catch (error) {
            console.error('Error fetching quote:', error);
            throw error;
        }
    }

    async getQuoteByToken(tokenIn, tokenOut, amountIn) {
        try {
            const fetchSupply = await addTree(amountIn)

            let amountIn_ = Math.floor(fetchSupply * Math.pow(10, process.env.decimals))
            console.log(amountIn_)
            const quote = await this.sdk.fetchQuote({
                token_in: tokenIn,
                token_out: tokenOut,
                amount_in: amountIn_
            });
            console.log(Number(quote.amount_out_with_fee) / Math.pow(10, 9))

            return await this.getQuote(tokenOut, tokenIn, quote.amount_out_with_fee);
        } catch (error) {
            console.error('Error fetching quote:', error);
            throw error;
        }
    }



    async executeSwap(tokenIn, tokenOut, amountIn, slippageBps = 100) {
        try {
            // 1. Get quote
            const quote = await this.getQuoteByToken(tokenOut, tokenIn, amountIn);

            // console.log('Quote received:', {
            //     estimatedOutput: quote.trade.amount_out
            // });

            //console.log(quote.trade)

            // 2. Sign the quote first
            // const txn = new Transaction();
            // txn.add(quote);
            // const signedTx = this.keypair.sign(txn.serialize());
            // console.log(signedTx)

            // 3. After signing quote, fetch and execute transaction
            const tx = await this.sdk.fetchTx({
                trade: quote.trade,
                sui_address: this.address,
                gas_budget: 0.03e9, // 0.03 SUI
                max_slippage_bps: slippageBps,
                return_output_coin_argument: false
            });

            //console.log("Raw transaction:", tx.transaction);
            let txn = await this.client.signAndExecuteTransaction({ signer: this.keypair, transaction: tx.transaction });
            //console.log(`TX HASH: https://suiscan.xyz/mainnet/tx/${txn.digest}`)

            return {
                success: true,
                transaction: `TX HASH: https://suiscan.xyz/mainnet/tx/${txn.digest}`
            };

        } catch (error) {
            console.error('Error executing swap:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Get token balance



}

// Example usage
async function hopSwap(signer, tokenIn, tokenOut, amountIn) {
    // Your private key without '0x' prefix
    const privateKey = signer;
    const apiKey = 'hopapiREaYWi3jykZIhl3zEBk0guT6N4eOyxKH'; // Optional

    const swapper = new SuiHopSwap(privateKey, apiKey);

    // Example: Swap SUI for USDC
    const swap = {
        tokenIn: tokenIn,
        tokenOut: tokenOut,
        amountIn: BigInt(amountIn), // 1 SUI (9 decimals)
        slippageBps: 100 // 1% max slippage
    };

    try {
        // Optional: Check balance before swap
        //const balance = await swapper.getBalance(swap.tokenIn);
        //console.log('Current balance:', balance);

        // Execute swap
        const result = await swapper.executeSwap(
            swap.tokenIn,
            swap.tokenOut,
            swap.amountIn,
            swap.slippageBps
        );

        console.log('Swap result:', result);
    } catch (error) {
        console.error('Error in main:', error);
    }

    // try {
    //     // Call getQuoteByToken through the instance
    //     await swapper.getQuoteByToken(
    //         "0xd5ac1605b404bc71911adca1a694279a99fbc45565aed89e5f33e88e5f7146b2::trumpai::TRUMPAI",
    //         "0x0000000000000000000000000000000000000000000000000000000000000002::sui::SUI",
    //         1
    //     );
    // } catch (error) {
    //     console.error('Error:', error);
    // }
}





module.exports = hopSwap;