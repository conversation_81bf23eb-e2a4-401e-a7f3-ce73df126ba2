const fs = require('fs');
const { SuiClient } = require('@mysten/sui.js/client');

const FATE_TYPE = '0x47c6cba4c841b9312e0607bacf56682e11dcdfaeabb4bd279a46c9942eaaaac8::fate::FATE';
const COIN_TYPE = `0x2::coin::Coin<${FATE_TYPE}>`;
const SUI_COIN_TYPE = '0x2::coin::Coin<0x2::sui::SUI>';
const WALLETS_FILE = 'wallets.json';
const SUI_RPC_URL = 'https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/';

const suiClient = new SuiClient({ url: SUI_RPC_URL });

async function getFateBalanceForWallet(address) {
  let hasNextPage = true;
  let nextCursor = null;
  let totalBalance = 0n;

  while (hasNextPage) {
    const resp = await suiClient.getOwnedObjects({
      owner: address,
      filter: { StructType: COIN_TYPE },
      options: { showContent: true },
      cursor: nextCursor,
      limit: 50,
    });

    for (const obj of resp.data) {
      if (
        obj.data &&
        obj.data.content &&
        obj.data.content.fields &&
        obj.data.content.fields.balance
      ) {
        totalBalance += BigInt(obj.data.content.fields.balance);
      }
    }

    hasNextPage = resp.hasNextPage;
    nextCursor = resp.nextCursor;
  }

  return totalBalance;
}

async function getSuiBalanceForWallet(address) {
  let hasNextPage = true;
  let nextCursor = null;
  let totalBalance = 0n;

  while (hasNextPage) {
    const resp = await suiClient.getOwnedObjects({
      owner: address,
      filter: { StructType: SUI_COIN_TYPE },
      options: { showContent: true },
      cursor: nextCursor,
      limit: 50,
    });

    for (const obj of resp.data) {
      if (
        obj.data &&
        obj.data.content &&
        obj.data.content.fields &&
        obj.data.content.fields.balance
      ) {
        totalBalance += BigInt(obj.data.content.fields.balance);
      }
    }

    hasNextPage = resp.hasNextPage;
    nextCursor = resp.nextCursor;
  }

  return totalBalance;
}

async function main() {
  const wallets = JSON.parse(fs.readFileSync(WALLETS_FILE, 'utf-8'));
  let grandTotalFate = 0n;
  let grandTotalSui = 0n;
  const TOTAL_SUPPLY = 1_000_000_000; // 1 billion FATE (human units)

  for (const wallet of wallets) {
    const address = wallet.address;
    try {
      const fateBalance = await getFateBalanceForWallet(address);
      const suiBalance = await getSuiBalanceForWallet(address);

      const fateHuman = Number(fateBalance) / 1e9;
      const suiHuman = Number(suiBalance) / 1e9;

      if (fateBalance > 0n) {
        const fatePercent = ((fateHuman / TOTAL_SUPPLY) * 100).toFixed(6);
        console.log(`Wallet: ${address}`);
        console.log(`  FATE: ${fateBalance} (raw), ${fateHuman} (human), ${fatePercent}% of supply`);
        console.log(`  SUI:  ${suiBalance} (raw), ${suiHuman} (human)`);
        console.log('');
      }

      grandTotalFate += fateBalance;
      grandTotalSui += suiBalance;
    } catch (err) {
      console.error(`Error for wallet ${address}:`, err.message);
    }
  }

  const grandTotalFateHuman = Number(grandTotalFate) / 1e9;
  const grandTotalSuiHuman = Number(grandTotalSui) / 1e9;
  const grandTotalFatePercent = ((grandTotalFateHuman / TOTAL_SUPPLY) * 100).toFixed(6);

  console.log('==========================================');
  console.log(`Total FATE across all wallets: ${grandTotalFate} (raw), ${grandTotalFateHuman} (human), ${grandTotalFatePercent}% of supply`);
  console.log(`Total SUI across all wallets:  ${grandTotalSui} (raw), ${grandTotalSuiHuman} (human)`);
}

main(); 