require('dotenv').config();
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');
const { Transaction } = require('@mysten/sui/transactions');
const { MIST_PER_SUI } = require('@mysten/sui/utils');
const fs = require('fs').promises;
const readline = require('readline');
const poolCalculator = require('./calculator')
const { createTurbosPool } = require('./turbo.js');
const removeLiquidityFromPool = require('./removeLiquidity')
const { execSync } = require('child_process');
const path = require('path');
const { quickSell } = require('./turbosell.js');

// Initialize SUI client
const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
const client = new SuiClient({ url: fullNodeUrl });

// Create readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Helper function to create directory if it doesn't exist
async function ensureDirectoryExists(dirPath) {
    try {
        await fs.access(dirPath);
    } catch {
        await fs.mkdir(dirPath, { recursive: true });
    }
}

// Helper function to save wallets to file with backup
async function saveWallets(wallets) {
    const walletsDir = path.join(process.cwd(), 'wallets');
    await ensureDirectoryExists(walletsDir);

    // Create backup of existing wallets.json if it exists
    try {
        const existingData = await fs.readFile(path.join(walletsDir, 'wallets.json'), 'utf8');
        if (existingData) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupPath = path.join(walletsDir, `wallets_backup_${timestamp}.json`);
            await fs.writeFile(backupPath, existingData);
            console.log(`Backup created at: ${backupPath}`);
        }
    } catch (error) {
        // No existing file to backup
    }

    // Save new wallets
    await fs.writeFile(
        path.join(walletsDir, 'wallets.json'),
        JSON.stringify(wallets, null, 2)
    );
    console.log('New wallets saved to wallets/wallets.json');
}

// Helper function for prompts
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

// Helper function to save wallets to file
async function saveWallets(wallets) {
    await fs.writeFile('wallets.json', JSON.stringify(wallets, null, 2));
    console.log('Wallets saved to wallets.json');
}

// Helper function to load wallets from file
async function loadWallets() {
    try {
        const data = await fs.readFile('wallets.json', 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.log('No existing wallets found');
        return [];
    }
}

async function generateWallets() {
    const count = parseInt(await question('How many wallets do you want to generate? '));
    const wallets = [];

    for (let i = 0; i < count; i++) {
        const keypair = new Ed25519Keypair();
        const privateKeyArray = keypair.getSecretKey();

        // Generate both key formats
        const suiKey = `suiprivkey1${Buffer.from(privateKeyArray).toString('base64')}`;

        // Convert to hex and ensure it's properly padded to 64 characters (32 bytes)
        let hexString = Buffer.from(privateKeyArray).toString('hex');
        // Pad to 64 characters if needed
        while (hexString.length < 64) {
            hexString = '0' + hexString;
        }
        const privateKey = `0x${hexString}`;

        wallets.push({
            address: keypair.getPublicKey().toSuiAddress(),
            privateKey: privateKey,
            suiKey: suiKey,
            buyAmount: 0,
            realPk: keypair.getSecretKey()
        });
        console.log(`Generated wallet ${i + 1}: ${wallets[i].address}`);
    }

    await saveWallets(wallets);
    return wallets;
}

// Helper function to load wallets from file
async function loadWallets() {
    try {
        const walletsDir = path.join(process.cwd(), 'wallets');
        const data = await fs.readFile(path.join(walletsDir, 'wallets.json'), 'utf8');
        return JSON.parse(data);
    } catch (error) {
        console.log('No existing wallets found');
        return [];
    }
}

const generate_account_balance = async (addr) => {
    const balance = await client.getBalance({ owner: addr })
    const getBal = Number.parseInt(balance.totalBalance) / Number(MIST_PER_SUI)
    return getBal
}

const check_wallet_balances = async () => {
    // Read wallets from wallets.json
    const walletData = await fs.readFile('wallets.json', 'utf-8');
    const wallets = JSON.parse(walletData);

    // Iterate over each wallet and check its balance
    for (let wallet of wallets) {
        try {
            const balance = await generate_account_balance(wallet.address);
            console.log(`Wallet (${wallet.address}) has a balance of ${balance} SUI`);

            // Optionally update the wallet object with the new balance
            wallet.balance = balance;
        } catch (err) {
            console.error(`Error fetching balance for Wallet ${wallet.walletNumber}: ${err.message}`);
        }
    }

};

const check_wallet_token_balances = async () => {
    // Read wallets from wallets.json
    const walletData = await fs.readFile('wallets.json', 'utf-8');
    const wallets = JSON.parse(walletData);

    // Define the token type for SQUIRTLE
    const squirtleTokenType = process.env.TOKEN_ADDRESS;

    // Iterate over each wallet and check its SQUIRTLE balance
    for (let wallet of wallets) {
        try {
            // Use getBalance with the specific coin type
            const tokenBalance = await client.getBalance({
                owner: wallet.address,
                coinType: squirtleTokenType
            });

            console.log(`Wallet (${wallet.address}) has a balance of ${tokenBalance.totalBalance} Tokens`);
            //console.log(`Coin Object Count: ${tokenBalance.coinObjectCount}`);

            // Optionally update the wallet object with the new balance
            wallet.squirtleBalance = tokenBalance.totalBalance;
        } catch (err) {
            console.error(`Error fetching Tokens balance for Wallet ${wallet.walletNumber}: ${err.message}`);
        }
    }
};



const fundWallets = async () => {
    const amount = parseFloat(await question('How many SUI do you want to fund each account? '));

    try {
        // Read wallet data from JSON file using fs.promises
        const walletData = await fs.readFile('wallets.json', 'utf-8');
        const wallets = JSON.parse(walletData);

        // Process all wallet transactions concurrently
        const transactionPromises = wallets.map(wallet =>
            processWalletTransaction(wallet, amount)
                .then(txn => ({
                    success: !!txn,
                    wallet: wallet.walletNumber,
                    txHash: txn?.digest
                }))
                .catch(error => ({
                    success: false,
                    wallet: wallet.walletNumber,
                    error: error.message
                }))
        );

        // Wait for all transactions to complete
        const results = await Promise.all(transactionPromises);

        // Process results
        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        // Log results
        console.log(`Successfully funded ${successful.length} accounts`);
        successful.forEach(result =>
            console.log(`Wallet funded: https://suiscan.xyz/mainnet/tx/${result.txHash}`)
        );

        if (failed.length > 0) {
            console.log(`Failed to fund ${failed.length} accounts:`);
            failed.forEach(result =>
                console.log(`Wallet ${result.wallet} failed: ${result.error || 'Max retries reached'}`)
            );
        }

        return true;
    } catch (error) {
        console.error('Error in fundWallets:', error);
        throw error;
    }
};

const processWalletTransaction = async (wallet, amount, retryLimit = 5) => {
    let attempts = 0;
    let txn;
    let amountSend = Math.floor(amount * **********)
    //console.log(wallet)
    while (attempts < retryLimit) {
        try {
            const tx = new Transaction();
            const [coin] = tx.splitCoins(tx.gas, [amountSend]);
            tx.transferObjects([coin], wallet.address);
            const keypair = Ed25519Keypair.fromSecretKey(process.env.PRIVATE_KEY);
            console.log(keypair.getPublicKey().toSuiAddress())
            // Sign and execute the transaction
            txn = await client.signAndExecuteTransaction({
                signer: keypair,
                transaction: tx
            });

            // If successful, return the transaction
            return txn;

        } catch (err) {
            attempts++;

            if (attempts >= retryLimit) {
                console.error(`Failed after ${retryLimit} attempts for Wallet ${wallet.walletNumber}`);
                return null;
            }

            // Add exponential backoff with some randomization
            const backoffTime = Math.min(1000 * Math.pow(2, attempts) + Math.random() * 1000, 10000);
            await new Promise(resolve => setTimeout(resolve, backoffTime));

            console.error(`Retrying transaction for Wallet ${wallet.walletNumber} (Attempt ${attempts + 1}/${retryLimit})`);
        }
    }
};

// Fund wallets from distributor

// error coming here.
const bundleSellModule = async () => {
    try {
        const walletData = await fs.readFile('wallets.json', 'utf-8');
        const wallets = JSON.parse(walletData);

        console.log(`Starting concurrent transactions for ${wallets.length} wallets...`);

        // First get all balances
        const balancePromises = wallets.map(wallet => tokenBalance(wallet.address));
        const balances = await Promise.all(balancePromises);

        // Create transaction promises with resolved balances
        const transactionPromises = wallets.map(async (wallet, index) => {
            const sellAmount = balances[index];

            if (!sellAmount || sellAmount === '0') {
                console.log(`Skipping wallet ${wallet.address} - no balance found`);
                return null;
            }

            console.log(`Wallet ${wallet.address} selling amount: ${sellAmount}`);
            let _sellerToken = process.env.TOKEN_ADDRESS;
            let _sellerSlippage = process.env.SLIPPAGE;

            try {
                let result = await quickSell(wallet.realPk, process.env.POOL_ADDRESS, _sellerToken, sellAmount, _sellerSlippage)
                return result
            } catch (error) {
                console.log(`Failed transaction for wallet: ${wallet.address}`, error.message);
                return null;
            }
        });

        const results = await Promise.all(transactionPromises);

        // Process results
        const successfulTransactions = results.filter(result => result !== null);
        console.log(`\nCompleted ${successfulTransactions.length} out of ${wallets.length} transactions`);

        // Log successful transactions
        successfulTransactions.forEach((result, index) => {
            console.log(`Transaction ${index + 1} successful:`);
        });

        return successfulTransactions;
    } catch (error) {
        console.error('Error in bundleSellModule:', error);
        throw error;
    }
};

const bundleSellModulePercentage = async (percentage) => {
    try {
        const walletData = await fs.readFile('wallets.json', 'utf-8');
        const wallets = JSON.parse(walletData);

        console.log(`Starting concurrent transactions for ${wallets.length} wallets...`);

        // First get all balances
        const balancePromises = wallets.map(wallet => tokenBalance(wallet.address));
        const balances = await Promise.all(balancePromises);

        // Create transaction promises with resolved balances
        const transactionPromises = wallets.map(async (wallet, index) => {
            const sellAmount = balances[index];

            if (!sellAmount || sellAmount === '0') {
                console.log(`Skipping wallet ${wallet.address} - no balance found`);
                return null;
            }

            console.log(`Wallet ${wallet.address} selling amount: ${sellAmount}`);

            try {
                let totalSell = Number(sellAmount) / 100 * Number(percentage)
                let _sellerToken = process.env.TOKEN_ADDRESS;
                let _sellerSlippage = process.env.SLIPPAGE;
                let result = await quickSell(wallet.realPk, process.env.POOL_ADDRESS, _sellerToken, totalSell, _sellerSlippage)
                return result
            } catch (error) {
                console.log(`Failed transaction for wallet: ${wallet.address}`, error.message);
                return null;
            }
        });

        const results = await Promise.all(transactionPromises);

        // Process results
        const successfulTransactions = results.filter(result => result !== null);
        console.log(`\nCompleted ${successfulTransactions.length} out of ${wallets.length} transactions`);

        // Log successful transactions
        successfulTransactions.forEach((result, index) => {
            console.log(`Transaction ${index + 1} successful:`);
        });

        return successfulTransactions;
    } catch (error) {
        console.error('Error in bundleSellModule:', error);
        throw error;
    }
};


const bundleSellModuleSpecificAddress = async (wallet_address_select, percentage) => {
    try {
        const walletData = await fs.readFile('wallets.json', 'utf-8');
        const wallets = JSON.parse(walletData);

        console.log(`Starting concurrent transactions for ${wallets.length} wallets...`);

        // First get all balances
        const balancePromises = wallets.map(wallet => tokenBalance(wallet.address));
        const balances = await Promise.all(balancePromises);

        // Create transaction promises with resolved balances
        const transactionPromises = wallets.map(async (wallet, index) => {
            const sellAmount = balances[index];
            if (wallet.address === wallet_address_select) {
                if (!sellAmount || sellAmount === '0') {
                    console.log(`Skipping wallet ${wallet.address} - no balance found`);
                    return null;
                }

                console.log(`Wallet ${wallet.address} selling amount: ${sellAmount}`);

                try {
                    let sellOut = Number(sellAmount) / 100 * Number(percentage)
                    let _sellerToken = process.env.TOKEN_ADDRESS;
                    let _sellerSlippage = process.env.SLIPPAGE;
                    let result = await quickSell(wallet.realPk, process.env.POOL_ADDRESS, _sellerToken, sellOut, _sellerSlippage)
                    return result
                } catch (error) {
                    console.log(`Failed transaction for wallet: ${wallet.address}`, error.message);
                    return null;
                }
            }
        });

        const results = await Promise.all(transactionPromises);

        // Process results
        const successfulTransactions = results.filter(result => result !== null);
        console.log(`\nCompleted ${successfulTransactions.length} out of ${wallets.length} transactions`);

        // Log successful transactions
        successfulTransactions.forEach((result, index) => {
            console.log(`Transaction ${index + 1} successful:`);
        });

        return successfulTransactions;
    } catch (error) {
        console.error('Error in bundleSellModule:', error);
        throw error;
    }
};

const tokenBalance = async (pubkeyOwner) => {
    try {
        // Fetch all balances for the owner
        const data = await client.getAllBalances({
            owner: pubkeyOwner
        });
        console.log(data)
        //console.log(process.env.TOKEN_ADDRESS)

        // Filter the result to get the balance for the specific token
        const specificTokenBalance = data.find(balance =>
            balance.coinType === process.env.TOKEN_ADDRESS
        );

        if (specificTokenBalance) {
            const balance = specificTokenBalance.totalBalance;
            console.log(`Token Balance for ${pubkeyOwner}: ${balance}`);
            return balance;
        } else {
            console.log(`No balance found for ${pubkeyOwner}`);
            return '0';
        }
    } catch (error) {
        console.error('Error fetching balance:', error);
        return '0';
    }
};

const refund_master_config = async () => {
    const walletData = await fs.readFile('wallets.json', 'utf-8');
    const wallets = JSON.parse(walletData);

    if (!process.env.MASTER_WALLET) {
        console.log(colors.red + 'Error: MASTER_WALLET not set in .env file' + colors.reset);
        return;
    }

    console.log(colors.yellow + `Will refund to master wallet: ${process.env.MASTER_WALLET}` + colors.reset);

    // Iterate over each wallet and check its balance
    for (let wallet of wallets) {
        try {
            // Create keypair from privateKey
            let keypair;

            // Prioritize suiKey over privateKey
            const privateKeyStr = wallet.realPk;

            // if (!privateKeyStr) {
            //     throw new Error('No private key found in wallet object');
            // }

            // // Handle string format (could be hex with 0x prefix or suiprivkey1 format)
            // if (typeof privateKeyStr === 'string') {
            //     let privateKeyBytes;

            //     if (privateKeyStr.startsWith('0x')) {
            //         // Hex format with 0x prefix
            //         let hexString = privateKeyStr.substring(2);
            //         // Pad to 64 characters (32 bytes) if necessary
            //         while (hexString.length < 64) {
            //             hexString = '0' + hexString;
            //         }
            //         privateKeyBytes = Buffer.from(hexString, 'hex');
            //     } else if (privateKeyStr.startsWith('suiprivkey1')) {
            //         // Sui format (starts with suiprivkey1)
            //         const base64Part = privateKeyStr.substring(11); // Remove 'suiprivkey1' prefix
            //         privateKeyBytes = Buffer.from(base64Part, 'base64');
            //     } else {
            //         // Try as raw hex string
            //         let hexString = privateKeyStr;
            //         // Pad to 64 characters (32 bytes) if necessary
            //         while (hexString.length < 64) {
            //             hexString = '0' + hexString;
            //         }
            //         privateKeyBytes = Buffer.from(hexString, 'hex');
            //     }

            //     // Validate we have the right key length
            //     if (privateKeyBytes.length !== 32) {
            //         throw new Error(`Invalid private key length: ${privateKeyBytes.length} bytes, expected 32`);
            //     }

            //     keypair = Ed25519Keypair.fromSecretKey(privateKeyBytes);
            // } else if (Array.isArray(privateKeyStr)) {
            //     // Handle array format (as stored by generateWallets)
            //     const privateKeyUint8Array = new Uint8Array(privateKeyStr);
            //     keypair = Ed25519Keypair.fromSecretKey(privateKeyUint8Array);
            // } else {
            //     throw new Error(`Unsupported private key format for wallet: ${wallet.address}`);
            // }

            keypair = Ed25519Keypair.fromSecretKey(privateKeyStr);

            const pubKey = keypair.getPublicKey().toSuiAddress();

            // Compare derived address with stored address
            if (pubKey !== wallet.address) {
                console.log(colors.red + `Address mismatch for wallet: 
                   - Stored address: ${wallet.address}
                   - Derived address: ${pubKey}` + colors.reset);
                console.log(colors.yellow + `Using derived address for operations` + colors.reset);
            } else {
                console.log(colors.green + `Address match confirmed for: ${wallet.address}` + colors.reset);
            }

            console.log(colors.green + `Processing wallet: ${pubKey.substring(0, 8)}...` + colors.reset);

            // Check balance on mainnet
            const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
            const mainnetClient = new SuiClient({ url: fullNodeUrl });
            const mainnetBalance = await mainnetClient.getBalance({ owner: pubKey });
            console.log(colors.blue + `Mainnet Balance: ${Number(mainnetBalance.totalBalance) / Number(MIST_PER_SUI)} SUI` + colors.reset);

            // Also check on testnet
            const testnetUrl = getFullnodeUrl('testnet');
            const testnetClient = new SuiClient({ url: testnetUrl });
            try {
                const testnetBalance = await testnetClient.getBalance({ owner: pubKey });
                console.log(colors.blue + `Testnet Balance: ${Number(testnetBalance.totalBalance) / Number(MIST_PER_SUI)} SUI` + colors.reset);
            } catch (error) {
                console.log(colors.yellow + `Could not fetch testnet balance: ${error.message}` + colors.reset);
            }

            const balance = mainnetBalance; // Use mainnet balance for refund decision

            try {
                // Only proceed if balance > dust amount (0.0035 SUI)
                if (Number(balance.totalBalance) > 3500000) {
                    const tx = new Transaction();
                    const value = Math.floor(Number(balance.totalBalance) - 3500000);
                    const [coin] = tx.splitCoins(tx.gas, [value]);
                    tx.transferObjects([coin], process.env.MASTER_WALLET);

                    const txn = await mainnetClient.signAndExecuteTransaction({
                        signer: keypair,
                        transaction: tx
                    });

                    console.log(colors.green + `Success! Tx Hash: https://suiscan.xyz/mainnet/tx/${txn.digest}` + colors.reset);
                } else {
                    console.log(colors.yellow + "Skipped: Insufficient funds (less than 0.0035 SUI)" + colors.reset);
                }
            } catch (error) {
                console.log(colors.red + `Transaction error: ${error.message}` + colors.reset);
            }
        } catch (err) {
            console.error(colors.red + `Error with wallet ${wallet.address}: ${err.message}` + colors.reset);
        }
    }
    console.log(colors.green + "Refund operation completed." + colors.reset);
}

// Remove liquidity
async function removeLiquidity(poolId, wallet) {
    try {
        const keypair = Ed25519Keypair.fromSecretKey(Buffer.from(wallet.privateKey, 'hex'));
        // Implement your remove liquidity logic here using cetusSDK
        console.log(`Removed liquidity from pool ${poolId}`);
    } catch (error) {
        console.error('Error removing liquidity:', error);
    }
}

const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    magenta: '\x1b[35m',
    white: '\x1b[37m'
};

const banner = `DEFIBITCH`;

// Clear screen function
const clearScreen = () => {
    process.stdout.write('\x1b[2J\x1b[0f');
};

// Display banner with support info
const displayBanner = () => {
    clearScreen();
    console.log(colors.cyan + banner + colors.reset);
    console.log(colors.yellow + '  Support: https://t.me/defibitch\n' + colors.reset);
    console.log(colors.green + '  Turbo Super Bundler\n' + colors.reset);
    console.log(colors.blue + '═'.repeat(60) + '\n' + colors.reset);
};

async function mainMenu() {
    while (true) {
        displayBanner();

        // Wallet Configuration Section
        console.log(colors.bright + colors.magenta + '💼 Wallet Configuration' + colors.reset);
        console.log(colors.green + '  1. Generate New Wallets');
        console.log('  2. View Wallet Balances');
        console.log('  3. Fund Wallets (Static Amount)');
        console.log('  4. Fund Wallets from Distribution Model');
        console.log('  5. Drain All Wallets to Address');
        console.log('  6. Refund to Master Wallet');
        console.log('  7. Update Master Wallet');
        console.log('  8. Regenerate Wallet Keys\n' + colors.reset);

        // Pool Operations Section
        console.log(colors.bright + colors.magenta + '🌊 Pool Operations' + colors.reset);
        console.log(colors.green + '  9. Pool Simulation');
        console.log('  10. Create Pool & Add Liquidity');
        console.log('  11. Remove Liquidity\n' + colors.reset);

        // Trading Operations Section
        console.log(colors.bright + colors.magenta + '📈 Trading Operations' + colors.reset);
        console.log(colors.green + '  12. Execute Sell Orders' + colors.reset);
        console.log(colors.green + '  13. Execute Sell Orders by Percentage' + colors.reset);
        console.log(colors.green + '  14. Execute Sell by Account and Percentage\n' + colors.reset);

        // Configuration Verification
        console.log(colors.bright + colors.magenta + '🔍 Configuration Tools' + colors.reset);
        console.log(colors.green + '  15. Verify Wallet Configuration');
        console.log('  16. Run Distribution Calculator');
        console.log('  17. Setup Environment\n' + colors.reset);

        // Exit Option
        console.log(colors.red + '  18. Exit\n' + colors.reset);

        const choice = await question(colors.yellow + 'Enter your choice (1-18): ' + colors.reset);

        switch (choice) {
            case '1':
                console.log(colors.cyan + '\n[*] Generating New Wallets...\n' + colors.reset);
                await generateWallets();
                break;

            case '2':
                console.log(colors.cyan + '\n[*] Checking Wallet Balances...\n' + colors.reset);
                await check_wallet_balances();
                break;

            case '3':
                console.log(colors.cyan + '\n[*] Funding Wallets (Static Amount)...\n' + colors.reset);
                await fundWallets();
                break;

            case '4':
                console.log(colors.cyan + '\n[*] Funding Wallets from Distribution Model...\n' + colors.reset);
                // Execute the fund-wallets.js script
                try {
                    execSync('node fund-wallets.js', { stdio: 'inherit' });
                } catch (error) {
                    console.error(colors.red + '\n[!] Error running fund-wallets.js: ' + error.message + colors.reset);
                }
                break;

            case '5':
                console.log(colors.cyan + '\n[*] Draining All Wallets to Address...\n' + colors.reset);
                try {
                    execSync('node drain-wallets.js', { stdio: 'inherit' });
                } catch (error) {
                    console.error(colors.red + '\n[!] Error running drain-wallets.js: ' + error.message + colors.reset);
                }
                break;

            case '6':
                console.log(colors.cyan + '\n[*] Initiating Refund Operation...\n' + colors.reset);
                await refund_master_config();
                break;

            case '7':
                console.log(colors.cyan + '\n[*] Updating Master Wallet...\n' + colors.reset);
                try {
                    execSync('node update-master-wallet.js', { stdio: 'inherit' });
                } catch (error) {
                    console.error(colors.red + '\n[!] Error running update-master-wallet.js: ' + error.message + colors.reset);
                }
                break;

            case '8':
                console.log(colors.cyan + '\n[*] Regenerating Wallet Keys...\n' + colors.reset);
                const sampleWallets = await regenerateWalletKeys();
                console.log(colors.cyan + '\nSample of regenerated wallets:' + colors.reset);
                console.log(sampleWallets);
                break;

            case '9':
                console.log(colors.cyan + '\n[*] Starting Pool Simulation...\n' + colors.reset);
                await poolCalculator(rl);
                break;

            case '10':
                console.log(colors.cyan + '\n[*] Creating New Pool...\n' + colors.reset);

                let tokenAddress, suiAmount, tokenAmount;

                // Check if pool-config/pool-config.json exists
                try {
                    const poolConfigPath = path.join(process.cwd(), 'pool-config', 'pool-config.json');
                    const configExists = await fs.access(poolConfigPath).then(() => true).catch(() => false);

                    if (configExists) {
                        const configData = await fs.readFile(poolConfigPath, 'utf8');
                        const poolConfig = JSON.parse(configData);

                        const useConfig = await question(colors.yellow + `Found pool configuration from distribution calculator:\n` +
                            `Token: ${poolConfig.tokenType}\n` +
                            `Token A (Your token): ${poolConfig.tokenAmount}\n` +
                            `Token B (SUI): ${poolConfig.suiAmount}\n` +
                            `Initial price: ${poolConfig.initialPrice.toFixed(12)} SUI per token\n` +
                            `Position: Global Liquidity (${poolConfig.tickLower} to ${poolConfig.tickUpper})\n` +
                            `Tick Spacing: ${poolConfig.tickSpacing} (${poolConfig.tickSpacing == 10 ? "high tier/1% fee" :
                                poolConfig.tickSpacing == 200 ? "medium tier/0.3% fee" :
                                    poolConfig.tickSpacing == 2000 ? "low tier/0.05% fee" :
                                        "lowest tier/0.01% fee"
                            })\n` +
                            `Created: ${new Date(poolConfig.timestamp).toLocaleString()}\n` +
                            `Use this configuration to create pool? (y/n): ` + colors.reset);

                        if (useConfig.toLowerCase() === 'y') {
                            tokenAddress = poolConfig.tokenType;
                            suiAmount = poolConfig.suiAmount;
                            tokenAmount = poolConfig.tokenAmount;
                        }
                    }

                    if (!tokenAddress) {
                        tokenAddress = await question(colors.yellow + 'Enter token address: ' + colors.reset);
                        suiAmount = await question(colors.yellow + 'Enter SUI amount: ' + colors.reset);
                        tokenAmount = await question(colors.yellow + 'Enter token amount: ' + colors.reset);
                    }

                    console.log('\nCreating Turbos pool with the following parameters:');
                    console.log(colors.cyan + 'Token Address:' + colors.reset, tokenAddress);
                    console.log(colors.cyan + 'SUI Amount:' + colors.reset, suiAmount);
                    console.log(colors.cyan + 'Token Amount:' + colors.reset, tokenAmount);



                    try {
                        const result = await createTurbosPool(tokenAddress, suiAmount, tokenAmount);
                        console.log(colors.green + '\nPool creation successful!' + colors.reset);
                        console.log('Transaction hash:', result.digest);

                        if (result.poolID) {
                            console.log(colors.cyan + 'Pool ID:' + colors.reset, result.poolID);
                            // Save pool ID for future reference if needed
                            try {
                                const poolInfoPath = path.join(process.cwd(), 'pool-config', 'last-pool.json');
                                await fs.writeFile(poolInfoPath, JSON.stringify({
                                    poolID: result.poolID,
                                    tokenAddress,
                                    suiAmount,
                                    tokenAmount,
                                    timestamp: Date.now()
                                }, null, 2));
                                console.log(colors.green + 'Pool information saved to last-pool.json' + colors.reset);
                            } catch (saveError) {
                                console.error(colors.yellow + 'Warning: Could not save pool information:' + colors.reset, saveError.message);
                            }
                        }
                    } catch (error) {
                        console.error(colors.red + '\nFailed to create pool:' + colors.reset, error.message);
                        if (error.message.includes('InsufficientBalance')) {
                            console.log(colors.yellow + 'Tip: Make sure you have enough SUI and tokens in your wallet.' + colors.reset);
                        }
                    }
                } catch (error) {
                    console.error(colors.red + 'Error in pool creation:' + colors.reset, error.message);
                }

                await question(colors.yellow + '\nPress Enter to continue...' + colors.reset);
                break;

            case '11':
                console.log(colors.cyan + '\n[*] Removing Liquidity...\n' + colors.reset);

                // Check if TOKEN_ADDRESS is set in .env
                if (!process.env.TOKEN_ADDRESS) {
                    console.log(colors.yellow + 'Warning: TOKEN_ADDRESS not set in .env file.' + colors.reset);
                    console.log('Will attempt to use first available position');

                    const proceed = await question(colors.yellow + 'Proceed without token address? (y/n): ' + colors.reset);
                    if (proceed.toLowerCase() !== 'y' && proceed.toLowerCase() !== 'yes') {
                        console.log(colors.yellow + 'Liquidity removal cancelled.' + colors.reset);
                        break;
                    }
                }

                try {
                    // Provide option to enter token address directly
                    const useSpecificToken = await question(colors.yellow + 'Do you want to specify a token address? (y/n): ' + colors.reset);

                    if (useSpecificToken.toLowerCase() === 'y' || useSpecificToken.toLowerCase() === 'yes') {
                        const tokenAddress = await question(colors.yellow + 'Enter token address: ' + colors.reset);
                        console.log(colors.green + `Using specified token address: ${tokenAddress}` + colors.reset);
                        await removeLiquidityFromPool(tokenAddress);
                    } else {
                        // Use the token from .env or let the function handle it
                        await removeLiquidityFromPool();
                    }
                } catch (error) {
                    console.error(colors.red + 'Error removing liquidity: ' + error.message + colors.reset);

                    // Offer to try again with a manually entered address if there was an error
                    if (error.message.includes('token')) {
                        console.log(colors.yellow + '\nThe error might be related to the token address.' + colors.reset);
                        const tryAgain = await question(colors.yellow + 'Do you want to try again with a manually entered token address? (y/n): ' + colors.reset);

                        if (tryAgain.toLowerCase() === 'y' || tryAgain.toLowerCase() === 'yes') {
                            const manualTokenAddress = await question(colors.yellow + 'Enter token address: ' + colors.reset);
                            console.log(colors.green + `Trying again with token address: ${manualTokenAddress}` + colors.reset);
                            await removeLiquidityFromPool(manualTokenAddress);
                        }
                    }
                }
                break;

            case '12':
                // working in here nigga's
                console.log(colors.cyan + '\n[*] Executing Sell Orders...\n' + colors.reset);
                await bundleSellModule();
                break;

            case '13':
                console.log("SELL PERCENTAGE FROM ALL ACCOUNTS")
                const count = parseInt(await question('How many percentage from each wallet (Remember the percentage gonna apply on every wallet)? '));
                if (Number(count) > 0 && Number(count) <= 100)
                    bundleSellModulePercentage(count)
                else {
                    console.log("Enter Between 1% to 100%")
                }
                break;

            case '14':
                console.log("SELL FROM SINGLE ACCOUNT WITH PERCENTAGE\n")
                await check_wallet_token_balances()
                const Waladdress = await question('How many percentage from each wallet (Remember the percentage gonna apply on every wallet)? ');
                const perSell = parseInt(await question('How many percentage You want to Sell? '));
                await bundleSellModuleSpecificAddress(Waladdress.toString(), perSell)
                break;

            case '15':
                console.log(colors.cyan + '\n[*] Verifying Wallet Configuration...\n' + colors.reset);
                try {
                    execSync('node verify-config.js', { stdio: 'inherit' });
                } catch (error) {
                    console.error(colors.red + '\n[!] Error running verify-config.js: ' + error.message + colors.reset);
                }
                break;

            case '16':
                console.log(colors.cyan + '\n[*] Running Distribution Calculator...\n' + colors.reset);
                try {
                    execSync('node distro.js', { stdio: 'inherit' });
                } catch (error) {
                    console.error(colors.red + '\n[!] Error running distro.js: ' + error.message + colors.reset);
                }
                break;

            case '17':
                console.log(colors.cyan + '\n[*] Running Setup Environment...\n' + colors.reset);
                try {
                    execSync('node setup.js', { stdio: 'inherit' });
                } catch (error) {
                    console.error(colors.red + '\n[!] Error running setup.js: ' + error.message + colors.reset);
                }
                break;




            case '18':
                console.log(colors.magenta + '\n[*] Thank you for using BOOM ROOM CLI! Goodbye!\n' + colors.reset);
                rl.close();
                return;

            default:
                console.log(colors.red + '\n[!] Invalid choice. Please try again.\n' + colors.reset);
        }

        // Pause before showing menu again
        await new Promise(resolve => {
            console.log(colors.yellow + '\nPress Enter to continue...' + colors.reset);
            rl.once('line', resolve);
        });
    }
}

// Add this new function after the generateWallets function
async function regenerateWalletKeys() {
    console.log(colors.cyan + '\n[*] Regenerating wallet keys to ensure consistency...\n' + colors.reset);

    try {
        // Read existing wallets
        const walletData = await fs.readFile('wallets.json', 'utf-8');
        const existingWallets = JSON.parse(walletData);

        // Create backup of existing wallets.json
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = `wallets_backup_${timestamp}.json`;
        await fs.writeFile(backupPath, walletData);
        console.log(colors.green + `Created backup of wallets.json at ${backupPath}` + colors.reset);

        // Generate new wallets with proper key formats
        const newWallets = [];

        for (let i = 0; i < existingWallets.length; i++) {
            // Create a new keypair
            const keypair = new Ed25519Keypair();
            const privateKeyArray = keypair.getSecretKey();
            const address = keypair.getPublicKey().toSuiAddress();

            // Format both key types properly
            // Base64 format with suiprivkey1 prefix
            const suiKey = `suiprivkey1${Buffer.from(privateKeyArray).toString('base64')}`;

            // Hex format with proper padding
            let hexString = Buffer.from(privateKeyArray).toString('hex');
            // Ensure it's padded to 64 characters (32 bytes)
            while (hexString.length < 64) {
                hexString = '0' + hexString;
            }
            const privateKey = `0x${hexString}`;

            // Preserve the buyAmount from the existing wallet
            const buyAmount = existingWallets[i].buyAmount || 0;

            // Add the new wallet
            newWallets.push({
                address,
                privateKey,
                suiKey,
                buyAmount
            });

            console.log(colors.green + `Regenerated wallet ${i + 1}/${existingWallets.length}: ${address}` + colors.reset);
        }

        // Save new wallets
        await fs.writeFile('wallets.json', JSON.stringify(newWallets, null, 2));
        console.log(colors.cyan + `\nSuccessfully regenerated ${newWallets.length} wallets with consistent keys` + colors.reset);

        // Return the first few wallets for verification
        return newWallets.slice(0, 3);
    } catch (error) {
        console.error(colors.red + `Error regenerating wallet keys: ${error.message}` + colors.reset);
        throw error;
    }
}

// Start the script with error handling
mainMenu().catch(error => {
    console.log(colors.red + '\n[!] An error occurred:' + colors.reset, error);
    process.exit(1);
});