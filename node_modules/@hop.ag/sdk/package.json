{"name": "@hop.ag/sdk", "version": "4.0.3", "description": "Official Hop SDK to access Hop Aggregator", "type": "module", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "scripts": {"test:quote": "npx tsx src/tests/quote.test.ts", "test:tx": "npx tsx src/tests/tx.test.ts", "test:tokens": "npx tsx src/tests/tokens.test.ts", "test:base": "npx tsx src/tests/base.test.ts", "test:price": "npx tsx src/tests/price.test.ts", "test:schema": "npx tsx src/tests/schema.test.ts", "build": "npm run build:esm && npm run build:cjs", "build:esm": "tsc && echo '{\"type\":\"module\"}' > dist/esm/package.json", "build:cjs": "tsc --project tsconfig.cjs.json && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json", "pretty": "prettier . --write"}, "author": "Hop Aggregator", "license": "ISC", "devDependencies": {"@types/node": "^20.14.2", "prettier": "3.2.5", "typescript": "^5.4.5"}, "dependencies": {"@mysten/sui": "^1.2.0", "cross-fetch": "^4.0.0", "tslib": "^2.6.2", "zod": "^3.23.8"}}