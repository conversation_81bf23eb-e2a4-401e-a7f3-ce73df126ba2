{
  "compilerOptions": {
    "module": "NodeNext",
    "moduleResolution": "NodeNext",
    "target": "ES2022",
    "outDir": "./dist/esm",

    // build
    "types": ["node"],
    "esModuleInterop": true,
    "preserveConstEnums": true,
    "skipLibCheck": true,
    "importHelpers": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowJs": true,
    "checkJs": true,

    // linting
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitOverride": true,
    "noUncheckedIndexedAccess": true,
    "noFallthroughCasesInSwitch": true,
    "forceConsistentCasingInFileNames": true,
    "noErrorTruncation": true,

    // sources
    "sourceMap": true,
    "declaration": true,
    "declarationMap": true,
    "inlineSources": true
  },
  "include": ["src/**/*"],
  "exclude": ["src/tests"]
}
