{"version": 3, "file": "util.js", "sourceRoot": "", "sources": ["../../../src/sdk/util.ts"], "names": [], "mappings": ";;;;AAAA,sEAAgC;AAEhC,iDAAoE;AACpE,6CAAuD;AAShD,KAAK,UAAU,cAAc,CAAI,EACtC,KAAK,EACL,OAAO,EACP,cAAc,GAKf;IACC,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAK,EAC1B,GAAG,OAAO,CAAC,cAAc,IAAI,gCAAiB,IAAI,KAAK,EAAE,EACzD;YACE,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,IAAI,EAAE,IAAI,CAAC,SAAS,CAClB;gBACE,GAAG,OAAO,CAAC,IAAI;gBACf,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,EACD,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACP,MAAM,cAAc,GAAG,OAAO,CAAC,KAAK,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;gBACjE,IAAI,cAAc;oBAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAChC,OAAO,OAAO,CAAC,KAAK,QAAQ,IAAI,cAAc,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC9E,CAAC,CACF;YACD,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;SACF,CACF,CAAC;QAEF,IAAI,QAAQ,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;YAC5B,MAAM,IAAI,KAAK,CACb,+BAA+B,KAAK,OAAO,QAAQ,CAAC,UAAU,EAAE,CACjE,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,cAAc,CAAC,SAAS,CAAC,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAC/D,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,OAAO,MAAM,CAAC,IAAI,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC5B,MAAM,IAAI,KAAK,CAAC,qBAAqB,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,MAAM,IAAI,KAAK,CACb,+BAA+B,KAAK,OAAQ,KAAe,CAAC,OAAO,EAAE,CACtE,CAAC;IACJ,CAAC;AACH,CAAC;AAlDD,wCAkDC;AAED,SAAgB,0BAA0B,CACxC,UAAkB,EAClB,OAAe;IAEf,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC;QACjB,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CACL,CAAC,UAAU,GAAG,CAAC,8BAAe,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,8BAAe,CAAC,CAC7E,CAAC;AACJ,CAAC;AAXD,gEAWC;AAED,MAAM,wBAAwB,GAAG,IAAA,0BAAkB,EAAC,eAAe,CAAC,CAAC;AAErE,SAAgB,SAAS,CAAC,SAAiB;IACzC,OAAO,wBAAwB,IAAI,IAAA,0BAAkB,EAAC,SAAS,CAAC,CAAC;AACnE,CAAC;AAFD,8BAEC", "sourcesContent": ["import fetch from \"cross-fetch\";\nimport { z } from \"zod\";\nimport { API_SERVER_PREFIX, FEE_DENOMINATOR } from \"./constants.js\";\nimport { normalizeStructTag } from \"@mysten/sui/utils\";\n\nexport interface RequestParams {\n  hop_server_url?: string;\n  api_key: string;\n  data: object;\n  method: \"get\" | \"post\";\n}\n\nexport async function makeAPIRequest<O>({\n  route,\n  options,\n  responseSchema,\n}: {\n  route: string;\n  options: RequestParams;\n  responseSchema: z.ZodSchema<O>;\n}): Promise<O> {\n  try {\n    const response = await fetch(\n      `${options.hop_server_url ?? API_SERVER_PREFIX}/${route}`,\n      {\n        method: options.method,\n        body: JSON.stringify(\n          {\n            ...options.data,\n            api_key: options.api_key\n          },\n          (_, v) => {\n            const isBigIntString = typeof v === 'string' && /^\\d+n$/.test(v);\n            if (isBigIntString) v.slice(-1);\n            return typeof v === 'bigint' || isBigIntString ? parseInt(v.toString()) : v;\n          },\n        ),\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n      },\n    );\n\n    if (response.status !== 200) {\n      throw new Error(\n        `HopApi > Error on request '/${route}' : ${response.statusText}`,\n      );\n    }\n\n    const result = responseSchema.safeParse(await response.json());\n    if (result.success) {\n      return result.data;\n    } else {\n      console.error(result.error);\n      throw new Error(`Invalid response: ${result.error.message}`);\n    }\n  } catch (error) {\n    console.error(error);\n    throw new Error(\n      `HopApi > Error on request '/${route}' : ${(error as Error).message}`,\n    );\n  }\n}\n\nexport function getAmountOutWithCommission(\n  amount_out: bigint,\n  fee_bps: number,\n): bigint {\n  if (fee_bps == 0) {\n    return amount_out;\n  }\n\n  return (\n    (amount_out * (FEE_DENOMINATOR - BigInt(fee_bps))) / BigInt(FEE_DENOMINATOR)\n  );\n}\n\nconst NORMALIZED_SUI_COIN_TYPE = normalizeStructTag(\"0x2::sui::SUI\");\n\nexport function isSuiType(coin_type: string) {\n  return NORMALIZED_SUI_COIN_TYPE == normalizeStructTag(coin_type);\n}"]}