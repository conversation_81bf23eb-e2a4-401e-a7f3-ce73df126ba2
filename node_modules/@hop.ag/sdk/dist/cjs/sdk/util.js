"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isSuiType = exports.getAmountOutWithCommission = exports.makeAPIRequest = void 0;
const tslib_1 = require("tslib");
const cross_fetch_1 = tslib_1.__importDefault(require("cross-fetch"));
const constants_js_1 = require("./constants.js");
const utils_1 = require("@mysten/sui/utils");
async function makeAPIRequest({ route, options, responseSchema, }) {
    try {
        const response = await (0, cross_fetch_1.default)(`${options.hop_server_url ?? constants_js_1.API_SERVER_PREFIX}/${route}`, {
            method: options.method,
            body: JSON.stringify({
                ...options.data,
                api_key: options.api_key
            }, (_, v) => {
                const isBigIntString = typeof v === 'string' && /^\d+n$/.test(v);
                if (isBigIntString)
                    v.slice(-1);
                return typeof v === 'bigint' || isBigIntString ? parseInt(v.toString()) : v;
            }),
            headers: {
                "Content-Type": "application/json",
            },
        });
        if (response.status !== 200) {
            throw new Error(`HopApi > Error on request '/${route}' : ${response.statusText}`);
        }
        const result = responseSchema.safeParse(await response.json());
        if (result.success) {
            return result.data;
        }
        else {
            console.error(result.error);
            throw new Error(`Invalid response: ${result.error.message}`);
        }
    }
    catch (error) {
        console.error(error);
        throw new Error(`HopApi > Error on request '/${route}' : ${error.message}`);
    }
}
exports.makeAPIRequest = makeAPIRequest;
function getAmountOutWithCommission(amount_out, fee_bps) {
    if (fee_bps == 0) {
        return amount_out;
    }
    return ((amount_out * (constants_js_1.FEE_DENOMINATOR - BigInt(fee_bps))) / BigInt(constants_js_1.FEE_DENOMINATOR));
}
exports.getAmountOutWithCommission = getAmountOutWithCommission;
const NORMALIZED_SUI_COIN_TYPE = (0, utils_1.normalizeStructTag)("0x2::sui::SUI");
function isSuiType(coin_type) {
    return NORMALIZED_SUI_COIN_TYPE == (0, utils_1.normalizeStructTag)(coin_type);
}
exports.isSuiType = isSuiType;
//# sourceMappingURL=util.js.map