{"version": 3, "file": "price.js", "sourceRoot": "", "sources": ["../../../../src/sdk/routes/price.ts"], "names": [], "mappings": ";;;AACA,wCAA4C;AAC5C,4CAAsD;AAe/C,KAAK,UAAU,UAAU,CAC9B,MAAc,EACd,MAAsB;IAEtB,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAc,EAAC;QACpC,KAAK,EAAE,OAAO;QACd,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;YAC/B,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc;YAC7C,IAAI,EAAE;gBACJ,SAAS,EAAE,MAAM,CAAC,SAAS;aAC5B;YACD,MAAM,EAAE,MAAM;SACf;QACD,cAAc,EAAE,4BAAmB;KACpC,CAAC,CAAC;IAEH,IAAI,QAAQ,EAAE,SAAS,EAAE,CAAC;QACxB,IAAI,SAAS,GAAG,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC;QAExD,OAAO;YACL,SAAS,EAAE,QAAQ,EAAE,SAAS;YAC9B,SAAS,EAAE,QAAQ,EAAE,SAAS;YAC9B,SAAS;YACT,SAAS,EAAE,QAAQ,EAAE,SAAS;SAC/B,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AAEzC,CAAC;AA9BD,gCA8BC", "sourcesContent": ["import { Hop<PERSON>pi } from \"../api.js\";\nimport { makeAPIRequest } from \"../util.js\";\nimport { priceResponseSchema } from \"../types/api.js\";\n\nexport interface GetPriceParams {\n  coin_type: string;\n}\n\nexport interface GetPriceResponse {\n  coin_type: string;\n\n  price_sui: number; // returns sui per token\n  price_usd: number; // returns usd per token\n\n  sui_price: number; // returns usdc per token\n}\n\nexport async function fetchPrice(\n  client: HopApi,\n  params: GetPriceParams\n): Promise<GetPriceResponse> {\n  const response = await makeAPIRequest({\n    route: `price`,\n    options: {\n      api_key: client.options.api_key,\n      hop_server_url: client.options.hop_server_url,\n      data: {\n        coin_type: params.coin_type,\n      },\n      method: \"post\",\n    },\n    responseSchema: priceResponseSchema,\n  });\n\n  if (response?.coin_type) {\n    let price_usd = response.price_sui * response.sui_price;\n\n    return {\n      coin_type: response?.coin_type,\n      price_sui: response?.price_sui,\n      price_usd,\n      sui_price: response?.sui_price\n    };\n  }\n\n  throw new Error(\"Unable to get price\");\n\n}"]}