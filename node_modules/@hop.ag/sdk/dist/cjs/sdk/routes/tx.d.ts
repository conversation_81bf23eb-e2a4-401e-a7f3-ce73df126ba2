import { Transaction, Argument, TransactionResult } from "@mysten/sui/transactions";
import { HopApi } from "../api.js";
import { Trade } from "../types/trade.js";
export interface GetTxParams {
    trade: Trade;
    sui_address: string;
    gas_budget?: number;
    max_slippage_bps?: number;
    sponsored?: boolean;
    base_transaction?: Transaction;
    input_coin_argument?: Argument;
    return_output_coin_argument?: boolean;
}
export interface GetTxResponse {
    transaction: Transaction;
    output_coin: TransactionResult | undefined;
}
export declare function fetchTx(client: HopApi, params: GetTxParams): Promise<GetTxResponse>;
//# sourceMappingURL=tx.d.ts.map