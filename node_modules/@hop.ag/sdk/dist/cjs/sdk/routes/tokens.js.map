{"version": 3, "file": "tokens.js", "sourceRoot": "", "sources": ["../../../../src/sdk/routes/tokens.ts"], "names": [], "mappings": ";;;AACA,4CAAuD;AACvD,wCAA4C;AAerC,KAAK,UAAU,WAAW,CAAC,MAAc;IAC9C,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAc,EAAC;QACpC,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;YAC/B,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc;YAC7C,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,MAAM;SACf;QACD,cAAc,EAAE,6BAAoB;KACrC,CAAC,CAAC;IAEH,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;QACrB,OAAO;YACL,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAC1C,CAAC;AAnBD,kCAmBC", "sourcesContent": ["import { HopApi } from \"../api.js\";\nimport { tokensResponseSchema } from \"../types/api.js\";\nimport { makeAPIRequest } from \"../util.js\";\n\nexport interface VerifiedToken {\n  coin_type: string;\n  name: string;\n  ticker: string;\n  icon_url: string;\n  decimals: number;\n  token_order?: number | null; // used for internal reasons\n}\n\nexport interface GetTokensResponse {\n  tokens: VerifiedToken[];\n}\n\nexport async function fetchTokens(client: HopApi): Promise<GetTokensResponse> {\n  const response = await makeAPIRequest({\n    route: \"tokens\",\n    options: {\n      api_key: client.options.api_key,\n      hop_server_url: client.options.hop_server_url,\n      data: {},\n      method: \"post\",\n    },\n    responseSchema: tokensResponseSchema,\n  });\n\n  if (response?.tokens) {\n    return {\n      tokens: response.tokens,\n    };\n  }\n\n  throw new Error(\"Unable to get tokens\");\n}\n"]}