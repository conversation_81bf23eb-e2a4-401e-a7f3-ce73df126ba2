"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchTx = void 0;
const transactions_1 = require("@mysten/sui/transactions");
const util_js_1 = require("../util.js");
const api_js_1 = require("../types/api.js");
const utils_1 = require("@mysten/sui/utils");
async function fetchCoins(client, sui_address, coin_type, max = -1) {
    let coins = [];
    let cursor = null;
    do {
        let coin_response = await client.client.getCoins({
            owner: sui_address,
            coinType: coin_type,
            cursor: cursor,
        });
        coins.push(...coin_response.data);
        // if you only want x coins
        if (max != -1 && coins.length >= max) {
            break;
        }
        if (coin_response.hasNextPage) {
            cursor = coin_response.nextCursor;
        }
        else {
            cursor = null;
        }
    } while (cursor != null);
    return coins.map((coin_struct) => ({
        object_id: {
            object_id: coin_struct.coinObjectId,
            version: coin_struct.version,
            digest: coin_struct.digest,
        },
        coin_type: coin_struct.coinType,
        amount: coin_struct.balance,
    }));
}
async function fetchTx(client, params) {
    // get input coins
    let gas_coins = [];
    let user_input_coins = [];
    if (!params.input_coin_argument) {
        user_input_coins = await fetchCoins(client, params.sui_address, params.trade.amount_in.token);
        if (user_input_coins.length == 0) {
            throw new Error(`HopApi > Error: sui address ${params.sui_address} does not have any input coins for tx.`);
        }
        let total_input = user_input_coins.reduce((c, t) => c + BigInt(t.amount), 0n);
        if (total_input < params.trade.amount_in.amount) {
            throw new Error(`HopApi > Error: user does not have enough amount in for trade. 
      User amount: ${total_input}. 
      Trade amount: ${params.trade.amount_in.amount}`);
        }
    }
    // gas coins
    if (!params.sponsored) {
        if ((0, utils_1.normalizeStructTag)(params.trade.amount_in.token) != (0, utils_1.normalizeStructTag)("0x2::sui::SUI") || user_input_coins.length == 0) {
            let fetched_gas_coins = await fetchCoins(client, params.sui_address, "0x2::sui::SUI", 60);
            gas_coins = fetched_gas_coins.filter((struct) => Number(struct.amount) > 0).map((struct) => struct.object_id);
        }
        else {
            gas_coins = user_input_coins.filter((struct) => Number(struct.amount) > 0).map((struct) => struct.object_id);
        }
    }
    // add any input coins that match user type
    if (!params.input_coin_argument) {
        let single_output_coin = await fetchCoins(client, params.sui_address, params.trade.amount_out.token, 1);
        user_input_coins.push(...single_output_coin);
    }
    if (!params.sponsored && gas_coins.length === 0) {
        throw new Error(`HopApi > Error: sui address ${params.sui_address} does not have any gas coins for tx.`);
    }
    if (params.input_coin_argument && !params.base_transaction) {
        throw new Error("Input coin argument must be result from base transaction!");
    }
    let input_coin_argument = undefined;
    let input_coin_argument_nested = undefined;
    let input_coin_argument_input = undefined;
    // @ts-expect-error
    if (params.input_coin_argument?.$kind === "Result" || params.input_coin_argument?.Result) {
        // @ts-expect-error
        input_coin_argument = params?.input_coin_argument?.Result;
        // @ts-expect-error
    }
    else if (params.input_coin_argument?.$kind === "NestedResult" || params.input_coin_argument?.NestedResult) {
        // @ts-expect-error
        input_coin_argument_nested = params?.input_coin_argument?.NestedResult;
        // @ts-expect-error
    }
    else if (params.input_coin_argument?.$kind === "Input" || params.input_coin_argument?.Input) {
        // @ts-expect-error
        input_coin_argument_input = params?.input_coin_argument?.Input;
    }
    let base_transaction = undefined;
    if (params.base_transaction) {
        const built_tx_array = await params.base_transaction.build({
            client: client.client,
            onlyTransactionKind: true
        });
        base_transaction = (0, utils_1.toB64)(built_tx_array);
    }
    const compileRequest = api_js_1.compileRequestSchema.parse({
        trade: params.trade,
        builder_request: {
            sender_address: params.sui_address,
            user_input_coins,
            gas_coins,
            gas_budget: params.gas_budget ?? 0.03e9,
            max_slippage_bps: params.max_slippage_bps,
            api_fee_wallet: client.options.fee_wallet,
            api_fee_bps: client.options.fee_bps,
            charge_fees_in_sui: client.options.charge_fees_in_sui,
            sponsored: params.sponsored,
            base_transaction,
            input_coin_argument,
            input_coin_argument_nested,
            input_coin_argument_input,
            return_output_coin_argument: !!params.return_output_coin_argument,
        },
    });
    const response = await (0, util_js_1.makeAPIRequest)({
        route: "tx/compile",
        options: {
            api_key: client.options.api_key,
            hop_server_url: client.options.hop_server_url,
            data: compileRequest,
            method: "post",
        },
        responseSchema: api_js_1.compileResponseSchema,
    });
    if (response.tx) {
        const tx_block = createFrontendTxBlock(response.tx);
        let output_coin = undefined;
        if (params.return_output_coin_argument) {
            // order
            // last merge into final output coin
            // slippage check
            // fee
            // @ts-ignore
            output_coin = tx_block
                .getData()
                .commands.find((tx) => tx.$kind == "MoveCall" &&
                tx.MoveCall.function === "check_slippage_v2" &&
                tx.MoveCall.module === "slippage")?.MoveCall.arguments[0];
        }
        return {
            transaction: tx_block,
            output_coin,
        };
    }
    throw new Error("Could not construct transaction");
}
exports.fetchTx = fetchTx;
// const ensure_array = (value: number | number[]): number[] => {
//   if (typeof value == "number") {
//     return [value];
//   } else {
//     return value;
//   }
// }
const createFrontendTxBlock = (serialized) => {
    const txb = transactions_1.Transaction.from(serialized);
    const inputs = txb.getData().inputs;
    const newInputs = inputs.map((input) => {
        if (input.$kind === "Object") {
            const objectId = input.Object?.SharedObject?.objectId ??
                input.Object?.ImmOrOwnedObject?.objectId;
            if (!objectId) {
                throw new Error(`Missing object ID for input ${input.$kind}`);
            }
            return {
                $kind: "UnresolvedObject",
                UnresolvedObject: {
                    objectId,
                }
            };
        }
        return input;
    });
    return transactions_1.Transaction.from(JSON.stringify({
        ...txb.getData(),
        gasConfig: {},
        inputs: newInputs,
    }));
};
//# sourceMappingURL=tx.js.map