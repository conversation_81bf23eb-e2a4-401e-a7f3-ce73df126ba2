"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchTokens = void 0;
const api_js_1 = require("../types/api.js");
const util_js_1 = require("../util.js");
async function fetchTokens(client) {
    const response = await (0, util_js_1.makeAPIRequest)({
        route: "tokens",
        options: {
            api_key: client.options.api_key,
            hop_server_url: client.options.hop_server_url,
            data: {},
            method: "post",
        },
        responseSchema: api_js_1.tokensResponseSchema,
    });
    if (response?.tokens) {
        return {
            tokens: response.tokens,
        };
    }
    throw new Error("Unable to get tokens");
}
exports.fetchTokens = fetchTokens;
//# sourceMappingURL=tokens.js.map