{"version": 3, "file": "tx.js", "sourceRoot": "", "sources": ["../../../../src/sdk/routes/tx.ts"], "names": [], "mappings": ";;;AAAA,2DAAoF;AAGpF,wCAA4C;AAC5C,4CAA8E;AAE9E,6CAA8D;AAiC9D,KAAK,UAAU,UAAU,CACvB,MAAc,EACd,WAAmB,EACnB,SAAiB,EACjB,GAAG,GAAG,CAAC,CAAC;IAER,IAAI,KAAK,GAAiB,EAAE,CAAC;IAC7B,IAAI,MAAM,GAAG,IAAI,CAAC;IAElB,GAAG,CAAC;QACF,IAAI,aAAa,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC/C,KAAK,EAAE,WAAW;YAClB,QAAQ,EAAE,SAAS;YACnB,MAAM,EAAE,MAAM;SACf,CAAC,CAAC;QACH,KAAK,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,IAAI,CAAC,CAAC;QAElC,2BAA2B;QAC3B,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;YACrC,MAAM;QACR,CAAC;QAED,IAAI,aAAa,CAAC,WAAW,EAAE,CAAC;YAC9B,MAAM,GAAG,aAAa,CAAC,UAAU,CAAC;QACpC,CAAC;aAAM,CAAC;YACN,MAAM,GAAG,IAAI,CAAC;QAChB,CAAC;IACH,CAAC,QAAQ,MAAM,IAAI,IAAI,EAAE;IAEzB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;QACjC,SAAS,EAAE;YACT,SAAS,EAAE,WAAW,CAAC,YAAY;YACnC,OAAO,EAAE,WAAW,CAAC,OAAO;YAC5B,MAAM,EAAE,WAAW,CAAC,MAAM;SAC3B;QACD,SAAS,EAAE,WAAW,CAAC,QAAQ;QAC/B,MAAM,EAAE,WAAW,CAAC,OAAO;KAC5B,CAAC,CAAC,CAAC;AACN,CAAC;AAEM,KAAK,UAAU,OAAO,CAC3B,MAAc,EACd,MAAmB;IAEnB,kBAAkB;IAClB,IAAI,SAAS,GAAa,EAAE,CAAC;IAC7B,IAAI,gBAAgB,GAAiB,EAAE,CAAC;IAExC,IAAG,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC9B,gBAAgB,GAAG,MAAM,UAAU,CAClC,MAAM,EACN,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAC7B,CAAC;QACF,IAAI,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACjC,MAAM,IAAI,KAAK,CACb,+BAA+B,MAAM,CAAC,WAAW,wCAAwC,CAC1F,CAAC;QACJ,CAAC;QACD,IAAI,WAAW,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;QAC9E,IAAI,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YAChD,MAAM,IAAI,KAAK,CACb;qBACa,WAAW;sBACV,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,CAC9C,CAAA;QACH,CAAC;IACH,CAAC;IAED,YAAY;IACZ,IAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;QACrB,IAAI,IAAA,0BAAkB,EAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,IAAA,0BAAkB,EAAC,eAAe,CAAC,IAAI,gBAAgB,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAC5H,IAAI,iBAAiB,GAAG,MAAM,UAAU,CACtC,MAAM,EACN,MAAM,CAAC,WAAW,EAClB,eAAe,EACf,EAAE,CACH,CAAC;YACF,SAAS,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAChH,CAAC;aAAM,CAAC;YACN,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/G,CAAC;IACH,CAAC;IAED,2CAA2C;IAC3C,IAAG,CAAC,MAAM,CAAC,mBAAmB,EAAE,CAAC;QAC/B,IAAI,kBAAkB,GAAiB,MAAM,UAAU,CACrD,MAAM,EACN,MAAM,CAAC,WAAW,EAClB,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,KAAK,EAC7B,CAAC,CACF,CAAC;QACF,gBAAgB,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,CAAC;IAC/C,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAChD,MAAM,IAAI,KAAK,CACb,+BAA+B,MAAM,CAAC,WAAW,sCAAsC,CACxF,CAAC;IACJ,CAAC;IAED,IAAG,MAAM,CAAC,mBAAmB,IAAI,CAAC,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC1D,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;IAC/E,CAAC;IAED,IAAI,mBAAmB,GAAG,SAAS,CAAC;IACpC,IAAI,0BAA0B,GAAG,SAAS,CAAC;IAC3C,IAAI,yBAAyB,GAAG,SAAS,CAAC;IAE1C,mBAAmB;IACnB,IAAG,MAAM,CAAC,mBAAmB,EAAE,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,mBAAmB,EAAE,MAAM,EAAE,CAAC;QACxF,mBAAmB;QACnB,mBAAmB,GAAG,MAAM,EAAE,mBAAmB,EAAE,MAAM,CAAC;QAC1D,mBAAmB;IACrB,CAAC;SAAM,IAAG,MAAM,CAAC,mBAAmB,EAAE,KAAK,KAAK,cAAc,IAAI,MAAM,CAAC,mBAAmB,EAAE,YAAY,EAAE,CAAC;QAC3G,mBAAmB;QACnB,0BAA0B,GAAG,MAAM,EAAE,mBAAmB,EAAE,YAAY,CAAC;QACvE,mBAAmB;IACrB,CAAC;SAAM,IAAG,MAAM,CAAC,mBAAmB,EAAE,KAAK,KAAK,OAAO,IAAI,MAAM,CAAC,mBAAmB,EAAE,KAAK,EAAE,CAAC;QAC7F,mBAAmB;QACnB,yBAAyB,GAAG,MAAM,EAAE,mBAAmB,EAAE,KAAK,CAAC;IACjE,CAAC;IAED,IAAI,gBAAgB,GAAG,SAAS,CAAC;IAEjC,IAAG,MAAM,CAAC,gBAAgB,EAAE,CAAC;QAC3B,MAAM,cAAc,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC,KAAK,CAAC;YACzD,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,mBAAmB,EAAE,IAAI;SAC1B,CAAC,CAAC;QAEH,gBAAgB,GAAG,IAAA,aAAK,EAAC,cAAc,CAAC,CAAC;IAC3C,CAAC;IAED,MAAM,cAAc,GAAG,6BAAoB,CAAC,KAAK,CAAC;QAChD,KAAK,EAAE,MAAM,CAAC,KAAK;QACnB,eAAe,EAAE;YACf,cAAc,EAAE,MAAM,CAAC,WAAW;YAClC,gBAAgB;YAChB,SAAS;YAET,UAAU,EAAE,MAAM,CAAC,UAAU,IAAI,MAAM;YACvC,gBAAgB,EAAE,MAAM,CAAC,gBAAgB;YAEzC,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,UAAU;YACzC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;YACnC,kBAAkB,EAAE,MAAM,CAAC,OAAO,CAAC,kBAAkB;YAErD,SAAS,EAAE,MAAM,CAAC,SAAS;YAC3B,gBAAgB;YAEhB,mBAAmB;YACnB,0BAA0B;YAC1B,yBAAyB;YAEzB,2BAA2B,EAAE,CAAC,CAAC,MAAM,CAAC,2BAA2B;SAClE;KACF,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,wBAAc,EAAC;QACpC,KAAK,EAAE,YAAY;QACnB,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;YAC/B,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc;YAC7C,IAAI,EAAE,cAAc;YACpB,MAAM,EAAE,MAAM;SACf;QACD,cAAc,EAAE,8BAAqB;KACtC,CAAC,CAAC;IAEH,IAAI,QAAQ,CAAC,EAAE,EAAE,CAAC;QAChB,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACpD,IAAI,WAAW,GAAkC,SAAS,CAAC;QAE3D,IAAG,MAAM,CAAC,2BAA2B,EAAE,CAAC;YACtC,QAAQ;YACR,oCAAoC;YACpC,iBAAiB;YACjB,MAAM;YACN,aAAa;YACb,WAAW,GAAG,QAAQ;iBACnB,OAAO,EAAE;iBACT,QAAQ,CAAC,IAAI,CACZ,CAAC,EAAE,EAAE,EAAE,CACL,EAAE,CAAC,KAAK,IAAI,UAAU;gBACtB,EAAE,CAAC,QAAQ,CAAC,QAAQ,KAAK,mBAAmB;gBAC5C,EAAE,CAAC,QAAQ,CAAC,MAAM,KAAK,UAAU,CACpC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO;YACL,WAAW,EAAE,QAAQ;YACrB,WAAW;SACZ,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;AACrD,CAAC;AA7JD,0BA6JC;AAED,iEAAiE;AACjE,oCAAoC;AACpC,sBAAsB;AACtB,aAAa;AACb,oBAAoB;AACpB,MAAM;AACN,IAAI;AAEJ,MAAM,qBAAqB,GAAG,CAAC,UAAkB,EAAe,EAAE;IAChE,MAAM,GAAG,GAAG,0BAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IACzC,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;IAEpC,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACrC,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC7B,MAAM,QAAQ,GACZ,KAAK,CAAC,MAAM,EAAE,YAAY,EAAE,QAAQ;gBACpC,KAAK,CAAC,MAAM,EAAE,gBAAgB,EAAE,QAAQ,CAAC;YAC3C,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;YAChE,CAAC;YAED,OAAO;gBACL,KAAK,EAAE,kBAAkB;gBACzB,gBAAgB,EAAE;oBAChB,QAAQ;iBACT;aACF,CAAA;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,OAAO,0BAAW,CAAC,IAAI,CACrB,IAAI,CAAC,SAAS,CAAC;QACb,GAAG,GAAG,CAAC,OAAO,EAAE;QAChB,SAAS,EAAE,EAAE;QACb,MAAM,EAAE,SAAS;KAClB,CAAC,CACH,CAAC;AACJ,CAAC,CAAC", "sourcesContent": ["import { Transaction, Argument, TransactionResult } from \"@mysten/sui/transactions\";\nimport { CoinStruct } from \"@mysten/sui/client\";\nimport { <PERSON><PERSON><PERSON> } from \"../api.js\";\nimport { makeAPIRequest } from \"../util.js\";\nimport { compileRequestSchema, compileResponseSchema } from \"../types/api.js\";\nimport { Trade } from \"../types/trade.js\";\nimport { normalizeStructTag, toB64 } from \"@mysten/sui/utils\";\n\nexport interface GetTxParams {\n  trade: Trade;\n  sui_address: string;\n\n  gas_budget?: number;\n  max_slippage_bps?: number;\n\n  /* FOR PTB USE */\n  sponsored?: boolean;\n\n  base_transaction?: Transaction;\n  input_coin_argument?: Argument;\n  return_output_coin_argument?: boolean;\n}\n\nexport interface GetTxResponse {\n  transaction: Transaction;\n  output_coin: TransactionResult | undefined;\n}\n\ninterface CoinId {\n  object_id: string;\n  version: string;\n}\n\ninterface InputToken {\n  object_id: CoinId;\n  coin_type: string;\n  amount: string;\n}\n\nasync function fetchCoins(\n  client: <PERSON><PERSON><PERSON>,\n  sui_address: string,\n  coin_type: string,\n  max = -1,\n): Promise<InputToken[]> {\n  let coins: CoinStruct[] = [];\n  let cursor = null;\n\n  do {\n    let coin_response = await client.client.getCoins({\n      owner: sui_address,\n      coinType: coin_type,\n      cursor: cursor,\n    });\n    coins.push(...coin_response.data);\n\n    // if you only want x coins\n    if (max != -1 && coins.length >= max) {\n      break;\n    }\n\n    if (coin_response.hasNextPage) {\n      cursor = coin_response.nextCursor;\n    } else {\n      cursor = null;\n    }\n  } while (cursor != null);\n\n  return coins.map((coin_struct) => ({\n    object_id: {\n      object_id: coin_struct.coinObjectId,\n      version: coin_struct.version,\n      digest: coin_struct.digest,\n    },\n    coin_type: coin_struct.coinType,\n    amount: coin_struct.balance,\n  }));\n}\n\nexport async function fetchTx(\n  client: HopApi,\n  params: GetTxParams,\n): Promise<GetTxResponse> {\n  // get input coins\n  let gas_coins: CoinId[] = [];\n  let user_input_coins: InputToken[] = [];\n\n  if(!params.input_coin_argument) {\n     user_input_coins = await fetchCoins(\n      client,\n      params.sui_address,\n      params.trade.amount_in.token\n    );\n    if (user_input_coins.length == 0) {\n      throw new Error(\n        `HopApi > Error: sui address ${params.sui_address} does not have any input coins for tx.`,\n      );\n    }\n    let total_input = user_input_coins.reduce((c, t) => c + BigInt(t.amount), 0n);\n    if (total_input < params.trade.amount_in.amount) {\n      throw new Error(\n        `HopApi > Error: user does not have enough amount in for trade. \n      User amount: ${total_input}. \n      Trade amount: ${params.trade.amount_in.amount}`\n      )\n    }\n  }\n\n  // gas coins\n  if(!params.sponsored) {\n    if (normalizeStructTag(params.trade.amount_in.token) != normalizeStructTag(\"0x2::sui::SUI\") || user_input_coins.length == 0) {\n      let fetched_gas_coins = await fetchCoins(\n        client,\n        params.sui_address,\n        \"0x2::sui::SUI\",\n        60\n      );\n      gas_coins = fetched_gas_coins.filter((struct) => Number(struct.amount) > 0).map((struct) => struct.object_id);\n    } else {\n      gas_coins = user_input_coins.filter((struct) => Number(struct.amount) > 0).map((struct) => struct.object_id);\n    }\n  }\n\n  // add any input coins that match user type\n  if(!params.input_coin_argument) {\n    let single_output_coin: InputToken[] = await fetchCoins(\n      client,\n      params.sui_address,\n      params.trade.amount_out.token,\n      1,\n    );\n    user_input_coins.push(...single_output_coin);\n  }\n\n  if (!params.sponsored && gas_coins.length === 0) {\n    throw new Error(\n      `HopApi > Error: sui address ${params.sui_address} does not have any gas coins for tx.`,\n    );\n  }\n\n  if(params.input_coin_argument && !params.base_transaction) {\n    throw new Error(\"Input coin argument must be result from base transaction!\");\n  }\n\n  let input_coin_argument = undefined;\n  let input_coin_argument_nested = undefined;\n  let input_coin_argument_input = undefined;\n\n  // @ts-expect-error\n  if(params.input_coin_argument?.$kind === \"Result\" || params.input_coin_argument?.Result) {\n    // @ts-expect-error\n    input_coin_argument = params?.input_coin_argument?.Result;\n    // @ts-expect-error\n  } else if(params.input_coin_argument?.$kind === \"NestedResult\" || params.input_coin_argument?.NestedResult) {\n    // @ts-expect-error\n    input_coin_argument_nested = params?.input_coin_argument?.NestedResult;\n    // @ts-expect-error\n  } else if(params.input_coin_argument?.$kind === \"Input\" || params.input_coin_argument?.Input) {\n    // @ts-expect-error\n    input_coin_argument_input = params?.input_coin_argument?.Input;\n  }\n\n  let base_transaction = undefined;\n\n  if(params.base_transaction) {\n    const built_tx_array = await params.base_transaction.build({\n      client: client.client,\n      onlyTransactionKind: true\n    });\n\n    base_transaction = toB64(built_tx_array);\n  }\n\n  const compileRequest = compileRequestSchema.parse({\n    trade: params.trade,\n    builder_request: {\n      sender_address: params.sui_address,\n      user_input_coins,\n      gas_coins,\n\n      gas_budget: params.gas_budget ?? 0.03e9,\n      max_slippage_bps: params.max_slippage_bps,\n\n      api_fee_wallet: client.options.fee_wallet,\n      api_fee_bps: client.options.fee_bps,\n      charge_fees_in_sui: client.options.charge_fees_in_sui,\n\n      sponsored: params.sponsored,\n      base_transaction,\n\n      input_coin_argument,\n      input_coin_argument_nested,\n      input_coin_argument_input,\n\n      return_output_coin_argument: !!params.return_output_coin_argument,\n    },\n  });\n\n  const response = await makeAPIRequest({\n    route: \"tx/compile\",\n    options: {\n      api_key: client.options.api_key,\n      hop_server_url: client.options.hop_server_url,\n      data: compileRequest,\n      method: \"post\",\n    },\n    responseSchema: compileResponseSchema,\n  });\n\n  if (response.tx) {\n    const tx_block = createFrontendTxBlock(response.tx);\n    let output_coin: TransactionResult | undefined = undefined;\n\n    if(params.return_output_coin_argument) {\n      // order\n      // last merge into final output coin\n      // slippage check\n      // fee\n      // @ts-ignore\n      output_coin = tx_block\n        .getData()\n        .commands.find(\n          (tx) =>\n            tx.$kind == \"MoveCall\" &&\n            tx.MoveCall.function === \"check_slippage_v2\" &&\n            tx.MoveCall.module === \"slippage\",\n        )?.MoveCall.arguments[0];\n    }\n\n    return {\n      transaction: tx_block,\n      output_coin,\n    };\n  }\n\n  throw new Error(\"Could not construct transaction\");\n}\n\n// const ensure_array = (value: number | number[]): number[] => {\n//   if (typeof value == \"number\") {\n//     return [value];\n//   } else {\n//     return value;\n//   }\n// }\n\nconst createFrontendTxBlock = (serialized: string): Transaction => {\n  const txb = Transaction.from(serialized);\n  const inputs = txb.getData().inputs;\n\n  const newInputs = inputs.map((input) => {\n    if (input.$kind === \"Object\") {\n      const objectId =\n        input.Object?.SharedObject?.objectId ??\n        input.Object?.ImmOrOwnedObject?.objectId;\n      if (!objectId) {\n        throw new Error(`Missing object ID for input ${input.$kind}`);\n      }\n\n      return {\n        $kind: \"UnresolvedObject\",\n        UnresolvedObject: {\n          objectId,\n        }\n      }\n    }\n    return input;\n  });\n\n  return Transaction.from(\n    JSON.stringify({\n      ...txb.getData(),\n      gasConfig: {},\n      inputs: newInputs,\n    })\n  );\n};\n"]}