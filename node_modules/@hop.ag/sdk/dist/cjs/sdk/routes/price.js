"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchPrice = void 0;
const util_js_1 = require("../util.js");
const api_js_1 = require("../types/api.js");
async function fetchPrice(client, params) {
    const response = await (0, util_js_1.makeAPIRequest)({
        route: `price`,
        options: {
            api_key: client.options.api_key,
            hop_server_url: client.options.hop_server_url,
            data: {
                coin_type: params.coin_type,
            },
            method: "post",
        },
        responseSchema: api_js_1.priceResponseSchema,
    });
    if (response?.coin_type) {
        let price_usd = response.price_sui * response.sui_price;
        return {
            coin_type: response?.coin_type,
            price_sui: response?.price_sui,
            price_usd,
            sui_price: response?.sui_price
        };
    }
    throw new Error("Unable to get price");
}
exports.fetchPrice = fetchPrice;
//# sourceMappingURL=price.js.map