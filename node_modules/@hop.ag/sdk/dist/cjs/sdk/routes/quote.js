"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fetchQuote = void 0;
const api_js_1 = require("../types/api.js");
const util_js_1 = require("../util.js");
async function fetchQuote(client, params) {
    const response = await (0, util_js_1.makeAPIRequest)({
        route: "quote",
        options: {
            api_key: client.options.api_key,
            hop_server_url: client.options.hop_server_url,
            data: {
                token_in: params.token_in,
                token_out: params.token_out,
                amount_in: params.amount_in.toString(),
                use_alpha_router: client.use_v2,
                api_fee_bps: client.options.fee_bps,
                charge_fees_in_sui: client.options.charge_fees_in_sui,
            },
            method: "post",
        },
        responseSchema: api_js_1.swapAPIResponseSchema,
    });
    if (response?.trade) {
        let amount_out_with_fee;
        if (client.options.charge_fees_in_sui && (0, util_js_1.isSuiType)(params.token_in)) {
            // fee already charged
            amount_out_with_fee = response.trade.amount_out.amount;
        }
        else {
            amount_out_with_fee = (0, util_js_1.getAmountOutWithCommission)(response.trade.amount_out.amount, client.options.fee_bps);
        }
        return {
            amount_out_with_fee,
            trade: response.trade,
        };
    }
    throw new Error("Unable to get quote");
}
exports.fetchQuote = fetchQuote;
//# sourceMappingURL=quote.js.map