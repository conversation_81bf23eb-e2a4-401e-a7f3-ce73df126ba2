{"version": 3, "file": "api.js", "sourceRoot": "", "sources": ["../../../src/sdk/api.ts"], "names": [], "mappings": ";;;AAAA,+CAA+C;AAC/C,gDAI2B;AAC3B,0CAAqE;AACrE,kDAAoE;AACpE,gDAAiF;AAWjF,MAAa,MAAM;IAKjB,YAAY,YAAoB,EAAE,OAAsB,EAAE,SAAkB,IAAI;QAC9E,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAS,CAAC,EAAE,GAAG,EAAE,YAAY,EAAE,CAAC,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC/C,OAAO,CAAC,KAAK,CACX,0BAA0B,EAC1B,IAAI,CAAC,OAAO,CAAC,OAAO,EACpB,qDAAqD,CACtD,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,YAAY;QAClB,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;QAEnC,IAAI,OAAO,GAAG,CAAC,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAC/C,CAAC;aAAM,IAAI,OAAO,GAAG,GAAG,EAAE,CAAC;YACzB,OAAO,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IAEH,KAAK,CAAC,UAAU,CAAC,KAAqB;QACpC,OAAO,IAAA,qBAAU,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,EAAe;QAC3B,OAAO,IAAA,eAAO,EAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,WAAW;QACf,OAAO,IAAA,uBAAW,EAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,KAAqB;QACpC,OAAO,IAAA,qBAAU,EAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;CAEF;AA1DD,wBA0DC", "sourcesContent": ["import { SuiClient } from \"@mysten/sui/client\";\nimport {\n  fetchQuote,\n  GetQuoteParams,\n  GetQuoteResponse,\n} from \"./routes/quote.js\";\nimport { fetchTx, GetTxParams, GetTxResponse } from \"./routes/tx.js\";\nimport { fetchTokens, GetTokensResponse } from \"./routes/tokens.js\";\nimport { fetchPrice, GetPriceParams, GetPriceResponse } from \"./routes/price.js\";\n\nexport interface HopApiOptions {\n  api_key: string;\n  fee_bps: number; // fee to charge in bps (50% split with Hop / max fee of 5%)\n  charge_fees_in_sui?: boolean,\n\n  fee_wallet?: string; // sui address\n  hop_server_url?: string;\n}\n\nexport class HopApi {\n  readonly client: SuiClient;\n  readonly options: HopApiOptions;\n  readonly use_v2: boolean;\n\n  constructor(rpc_endpoint: string, options: HopApiOptions, use_v2: boolean = true) {\n    this.client = new SuiClient({ url: rpc_endpoint });\n    this.options = options;\n    this.use_v2 = use_v2;\n\n    this.validate_api_key();\n    this.validate_fee();\n  }\n\n  private validate_api_key() {\n    if (!this.options.api_key.startsWith(\"hopapi\")) {\n      console.error(\n        \"Error > Invalid api key:\",\n        this.options.api_key,\n        \". Please contact us at hop.ag to request a new key.\",\n      );\n    }\n  }\n\n  private validate_fee() {\n    let fee_bps = this.options.fee_bps;\n\n    if (fee_bps < 0) {\n      console.error(\"> fee_bps must be positive.\");\n    } else if (fee_bps > 500) {\n      console.error(\"> fee_bps must be less than or equal to 5% (500 bps).\");\n    }\n\n    this.options.fee_bps = Math.max(this.options.fee_bps, 0);\n    this.options.fee_bps = Math.min(this.options.fee_bps, 500);\n    this.options.fee_bps = Number(this.options.fee_bps.toFixed(0));\n  }\n\n  /*\n   * Routes\n   */\n\n  async fetchQuote(quote: GetQuoteParams): Promise<GetQuoteResponse> {\n    return fetchQuote(this, quote);\n  }\n\n  async fetchTx(tx: GetTxParams): Promise<GetTxResponse> {\n    return fetchTx(this, tx);\n  }\n\n  async fetchTokens(): Promise<GetTokensResponse> {\n    return fetchTokens(this);\n  }\n  \n  async fetchPrice(price: GetPriceParams): Promise<GetPriceResponse> {\n    return fetchPrice(this, price);\n  }\n  \n}\n"]}