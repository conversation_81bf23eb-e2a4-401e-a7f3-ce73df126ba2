import { z } from "zod";
export declare enum SuiExchange {
    CETUS = "CETUS",
    FLOWX = "FLOWX",
    TURBOS = "TURBOS",
    AFTERMATH = "AFTERMATH",
    KRIYA = "KRIYA",
    BLUEMOVE = "BLUEMOVE",
    DEEPBOOK = "DEEPBOOK",
    SUISWAP = "SUISWAP",
    HOPFUN = "HOPFUN",
    BLUEFIN = "BLUEFIN"
}
export declare const poolExtraSchema: z.ZodUnion<[z.ZodObject<{
    AFTERMATH: z.ZodObject<{
        lp_coin_type: z.ZodString;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        lp_coin_type: z.ZodString;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        lp_coin_type: z.ZodString;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    AFTERMATH: {
        lp_coin_type: string;
    } & {
        [k: string]: unknown;
    };
}, {
    AFTERMATH: {
        lp_coin_type: string;
    } & {
        [k: string]: unknown;
    };
}>, z.ZodObject<{
    DEEPBOOK: z.ZodObject<{
        pool_type: z.ZodString;
        lot_size: z.ZodBigInt;
        min_size: z.ZodBigInt;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        pool_type: z.ZodString;
        lot_size: z.ZodBigInt;
        min_size: z.ZodBigInt;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        pool_type: z.ZodString;
        lot_size: z.ZodBigInt;
        min_size: z.ZodBigInt;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    DEEPBOOK: {
        min_size: bigint;
        pool_type: string;
        lot_size: bigint;
    } & {
        [k: string]: unknown;
    };
}, {
    DEEPBOOK: {
        min_size: bigint;
        pool_type: string;
        lot_size: bigint;
    } & {
        [k: string]: unknown;
    };
}>, z.ZodObject<{
    TURBOS: z.ZodObject<{
        coin_type_a: z.ZodString;
        coin_type_b: z.ZodString;
        fee_type: z.ZodString;
        tick_spacing: z.ZodNumber;
        tick_current_index: z.ZodNumber;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        coin_type_a: z.ZodString;
        coin_type_b: z.ZodString;
        fee_type: z.ZodString;
        tick_spacing: z.ZodNumber;
        tick_current_index: z.ZodNumber;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        coin_type_a: z.ZodString;
        coin_type_b: z.ZodString;
        fee_type: z.ZodString;
        tick_spacing: z.ZodNumber;
        tick_current_index: z.ZodNumber;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    TURBOS: {
        coin_type_a: string;
        coin_type_b: string;
        fee_type: string;
        tick_spacing: number;
        tick_current_index: number;
    } & {
        [k: string]: unknown;
    };
}, {
    TURBOS: {
        coin_type_a: string;
        coin_type_b: string;
        fee_type: string;
        tick_spacing: number;
        tick_current_index: number;
    } & {
        [k: string]: unknown;
    };
}>, z.ZodObject<{
    CETUS: z.ZodObject<{
        coin_type_a: z.ZodString;
        coin_type_b: z.ZodString;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        coin_type_a: z.ZodString;
        coin_type_b: z.ZodString;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        coin_type_a: z.ZodString;
        coin_type_b: z.ZodString;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    CETUS: {
        coin_type_a: string;
        coin_type_b: string;
    } & {
        [k: string]: unknown;
    };
}, {
    CETUS: {
        coin_type_a: string;
        coin_type_b: string;
    } & {
        [k: string]: unknown;
    };
}>, z.ZodObject<{
    FLOWX: z.ZodObject<{
        is_v3: z.ZodBoolean;
        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        is_v3: z.ZodBoolean;
        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        is_v3: z.ZodBoolean;
        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    FLOWX: {
        is_v3: boolean;
        fee_rate?: number | null | undefined;
    } & {
        [k: string]: unknown;
    };
}, {
    FLOWX: {
        is_v3: boolean;
        fee_rate?: number | null | undefined;
    } & {
        [k: string]: unknown;
    };
}>, z.ZodObject<{
    KRIYA: z.ZodObject<{
        is_v3: z.ZodBoolean;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        is_v3: z.ZodBoolean;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        is_v3: z.ZodBoolean;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    KRIYA: {
        is_v3: boolean;
    } & {
        [k: string]: unknown;
    };
}, {
    KRIYA: {
        is_v3: boolean;
    } & {
        [k: string]: unknown;
    };
}>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>;
export type PoolExtra = z.infer<typeof poolExtraSchema>;
declare const tradePoolSchema: z.ZodObject<{
    object_id: z.ZodString;
    initial_shared_version: z.ZodNullable<z.ZodNumber>;
    sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
    tokens: z.ZodArray<z.ZodString, "atleastone">;
    is_active: z.ZodBoolean;
    extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
        AFTERMATH: z.ZodObject<{
            lp_coin_type: z.ZodString;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            lp_coin_type: z.ZodString;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            lp_coin_type: z.ZodString;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        AFTERMATH: {
            lp_coin_type: string;
        } & {
            [k: string]: unknown;
        };
    }, {
        AFTERMATH: {
            lp_coin_type: string;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        DEEPBOOK: z.ZodObject<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        DEEPBOOK: {
            min_size: bigint;
            pool_type: string;
            lot_size: bigint;
        } & {
            [k: string]: unknown;
        };
    }, {
        DEEPBOOK: {
            min_size: bigint;
            pool_type: string;
            lot_size: bigint;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        TURBOS: z.ZodObject<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        TURBOS: {
            coin_type_a: string;
            coin_type_b: string;
            fee_type: string;
            tick_spacing: number;
            tick_current_index: number;
        } & {
            [k: string]: unknown;
        };
    }, {
        TURBOS: {
            coin_type_a: string;
            coin_type_b: string;
            fee_type: string;
            tick_spacing: number;
            tick_current_index: number;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        CETUS: z.ZodObject<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        CETUS: {
            coin_type_a: string;
            coin_type_b: string;
        } & {
            [k: string]: unknown;
        };
    }, {
        CETUS: {
            coin_type_a: string;
            coin_type_b: string;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        FLOWX: z.ZodObject<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        FLOWX: {
            is_v3: boolean;
            fee_rate?: number | null | undefined;
        } & {
            [k: string]: unknown;
        };
    }, {
        FLOWX: {
            is_v3: boolean;
            fee_rate?: number | null | undefined;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        KRIYA: z.ZodObject<{
            is_v3: z.ZodBoolean;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            is_v3: z.ZodBoolean;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            is_v3: z.ZodBoolean;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        KRIYA: {
            is_v3: boolean;
        } & {
            [k: string]: unknown;
        };
    }, {
        KRIYA: {
            is_v3: boolean;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    object_id: z.ZodString;
    initial_shared_version: z.ZodNullable<z.ZodNumber>;
    sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
    tokens: z.ZodArray<z.ZodString, "atleastone">;
    is_active: z.ZodBoolean;
    extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
        AFTERMATH: z.ZodObject<{
            lp_coin_type: z.ZodString;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            lp_coin_type: z.ZodString;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            lp_coin_type: z.ZodString;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        AFTERMATH: {
            lp_coin_type: string;
        } & {
            [k: string]: unknown;
        };
    }, {
        AFTERMATH: {
            lp_coin_type: string;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        DEEPBOOK: z.ZodObject<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        DEEPBOOK: {
            min_size: bigint;
            pool_type: string;
            lot_size: bigint;
        } & {
            [k: string]: unknown;
        };
    }, {
        DEEPBOOK: {
            min_size: bigint;
            pool_type: string;
            lot_size: bigint;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        TURBOS: z.ZodObject<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        TURBOS: {
            coin_type_a: string;
            coin_type_b: string;
            fee_type: string;
            tick_spacing: number;
            tick_current_index: number;
        } & {
            [k: string]: unknown;
        };
    }, {
        TURBOS: {
            coin_type_a: string;
            coin_type_b: string;
            fee_type: string;
            tick_spacing: number;
            tick_current_index: number;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        CETUS: z.ZodObject<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        CETUS: {
            coin_type_a: string;
            coin_type_b: string;
        } & {
            [k: string]: unknown;
        };
    }, {
        CETUS: {
            coin_type_a: string;
            coin_type_b: string;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        FLOWX: z.ZodObject<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        FLOWX: {
            is_v3: boolean;
            fee_rate?: number | null | undefined;
        } & {
            [k: string]: unknown;
        };
    }, {
        FLOWX: {
            is_v3: boolean;
            fee_rate?: number | null | undefined;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        KRIYA: z.ZodObject<{
            is_v3: z.ZodBoolean;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            is_v3: z.ZodBoolean;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            is_v3: z.ZodBoolean;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        KRIYA: {
            is_v3: boolean;
        } & {
            [k: string]: unknown;
        };
    }, {
        KRIYA: {
            is_v3: boolean;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    object_id: z.ZodString;
    initial_shared_version: z.ZodNullable<z.ZodNumber>;
    sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
    tokens: z.ZodArray<z.ZodString, "atleastone">;
    is_active: z.ZodBoolean;
    extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
        AFTERMATH: z.ZodObject<{
            lp_coin_type: z.ZodString;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            lp_coin_type: z.ZodString;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            lp_coin_type: z.ZodString;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        AFTERMATH: {
            lp_coin_type: string;
        } & {
            [k: string]: unknown;
        };
    }, {
        AFTERMATH: {
            lp_coin_type: string;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        DEEPBOOK: z.ZodObject<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            pool_type: z.ZodString;
            lot_size: z.ZodBigInt;
            min_size: z.ZodBigInt;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        DEEPBOOK: {
            min_size: bigint;
            pool_type: string;
            lot_size: bigint;
        } & {
            [k: string]: unknown;
        };
    }, {
        DEEPBOOK: {
            min_size: bigint;
            pool_type: string;
            lot_size: bigint;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        TURBOS: z.ZodObject<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
            fee_type: z.ZodString;
            tick_spacing: z.ZodNumber;
            tick_current_index: z.ZodNumber;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        TURBOS: {
            coin_type_a: string;
            coin_type_b: string;
            fee_type: string;
            tick_spacing: number;
            tick_current_index: number;
        } & {
            [k: string]: unknown;
        };
    }, {
        TURBOS: {
            coin_type_a: string;
            coin_type_b: string;
            fee_type: string;
            tick_spacing: number;
            tick_current_index: number;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        CETUS: z.ZodObject<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            coin_type_a: z.ZodString;
            coin_type_b: z.ZodString;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        CETUS: {
            coin_type_a: string;
            coin_type_b: string;
        } & {
            [k: string]: unknown;
        };
    }, {
        CETUS: {
            coin_type_a: string;
            coin_type_b: string;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        FLOWX: z.ZodObject<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            is_v3: z.ZodBoolean;
            fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        FLOWX: {
            is_v3: boolean;
            fee_rate?: number | null | undefined;
        } & {
            [k: string]: unknown;
        };
    }, {
        FLOWX: {
            is_v3: boolean;
            fee_rate?: number | null | undefined;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{
        KRIYA: z.ZodObject<{
            is_v3: z.ZodBoolean;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            is_v3: z.ZodBoolean;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            is_v3: z.ZodBoolean;
        }, z.ZodTypeAny, "passthrough">>;
    }, "strip", z.ZodTypeAny, {
        KRIYA: {
            is_v3: boolean;
        } & {
            [k: string]: unknown;
        };
    }, {
        KRIYA: {
            is_v3: boolean;
        } & {
            [k: string]: unknown;
        };
    }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
}, z.ZodTypeAny, "passthrough">>;
export type TradePool = z.infer<typeof tradePoolSchema>;
declare const tradeNodeSchema: z.ZodObject<{
    pool: z.ZodObject<{
        object_id: z.ZodString;
        initial_shared_version: z.ZodNullable<z.ZodNumber>;
        sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
        tokens: z.ZodArray<z.ZodString, "atleastone">;
        is_active: z.ZodBoolean;
        extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
            AFTERMATH: z.ZodObject<{
                lp_coin_type: z.ZodString;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                lp_coin_type: z.ZodString;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                lp_coin_type: z.ZodString;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        }, {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            DEEPBOOK: z.ZodObject<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        }, {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            TURBOS: z.ZodObject<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        }, {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            CETUS: z.ZodObject<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        }, {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            FLOWX: z.ZodObject<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        }, {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            KRIYA: z.ZodObject<{
                is_v3: z.ZodBoolean;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                is_v3: z.ZodBoolean;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                is_v3: z.ZodBoolean;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        }, {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        object_id: z.ZodString;
        initial_shared_version: z.ZodNullable<z.ZodNumber>;
        sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
        tokens: z.ZodArray<z.ZodString, "atleastone">;
        is_active: z.ZodBoolean;
        extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
            AFTERMATH: z.ZodObject<{
                lp_coin_type: z.ZodString;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                lp_coin_type: z.ZodString;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                lp_coin_type: z.ZodString;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        }, {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            DEEPBOOK: z.ZodObject<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        }, {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            TURBOS: z.ZodObject<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        }, {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            CETUS: z.ZodObject<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        }, {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            FLOWX: z.ZodObject<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        }, {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            KRIYA: z.ZodObject<{
                is_v3: z.ZodBoolean;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                is_v3: z.ZodBoolean;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                is_v3: z.ZodBoolean;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        }, {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        object_id: z.ZodString;
        initial_shared_version: z.ZodNullable<z.ZodNumber>;
        sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
        tokens: z.ZodArray<z.ZodString, "atleastone">;
        is_active: z.ZodBoolean;
        extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
            AFTERMATH: z.ZodObject<{
                lp_coin_type: z.ZodString;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                lp_coin_type: z.ZodString;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                lp_coin_type: z.ZodString;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        }, {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            DEEPBOOK: z.ZodObject<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                pool_type: z.ZodString;
                lot_size: z.ZodBigInt;
                min_size: z.ZodBigInt;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        }, {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            TURBOS: z.ZodObject<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
                fee_type: z.ZodString;
                tick_spacing: z.ZodNumber;
                tick_current_index: z.ZodNumber;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        }, {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            CETUS: z.ZodObject<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                coin_type_a: z.ZodString;
                coin_type_b: z.ZodString;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        }, {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            FLOWX: z.ZodObject<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                is_v3: z.ZodBoolean;
                fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        }, {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{
            KRIYA: z.ZodObject<{
                is_v3: z.ZodBoolean;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                is_v3: z.ZodBoolean;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                is_v3: z.ZodBoolean;
            }, z.ZodTypeAny, "passthrough">>;
        }, "strip", z.ZodTypeAny, {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        }, {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
    }, z.ZodTypeAny, "passthrough">>;
    weight: z.ZodNumber;
    amount_in: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
    amount_out: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
}, "strip", z.ZodTypeAny, {
    pool: {
        tokens: [string, ...string[]];
        object_id: string;
        initial_shared_version: number | null;
        sui_exchange: string;
        is_active: boolean;
        extra: {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        } | {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        } | {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        } | {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        } | {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        } | {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
    } & {
        [k: string]: unknown;
    };
    weight: number;
    amount_in: {
        token: string;
        amount: bigint;
    };
    amount_out: {
        token: string;
        amount: bigint;
    };
}, {
    pool: {
        tokens: [string, ...string[]];
        object_id: string;
        initial_shared_version: number | null;
        sui_exchange: string;
        is_active: boolean;
        extra: {
            AFTERMATH: {
                lp_coin_type: string;
            } & {
                [k: string]: unknown;
            };
        } | {
            DEEPBOOK: {
                min_size: bigint;
                pool_type: string;
                lot_size: bigint;
            } & {
                [k: string]: unknown;
            };
        } | {
            TURBOS: {
                coin_type_a: string;
                coin_type_b: string;
                fee_type: string;
                tick_spacing: number;
                tick_current_index: number;
            } & {
                [k: string]: unknown;
            };
        } | {
            CETUS: {
                coin_type_a: string;
                coin_type_b: string;
            } & {
                [k: string]: unknown;
            };
        } | {
            FLOWX: {
                is_v3: boolean;
                fee_rate?: number | null | undefined;
            } & {
                [k: string]: unknown;
            };
        } | {
            KRIYA: {
                is_v3: boolean;
            } & {
                [k: string]: unknown;
            };
        } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
    } & {
        [k: string]: unknown;
    };
    weight: number;
    amount_in: {
        token: string;
        amount: bigint;
    };
    amount_out: {
        token: string;
        amount: bigint;
    };
}>;
export type TradeNode = z.infer<typeof tradeNodeSchema>;
export declare const tradeSchema: z.ZodObject<{
    nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
        pool: z.ZodObject<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, z.ZodTypeAny, "passthrough">>;
        weight: z.ZodNumber;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "strip", z.ZodTypeAny, {
        pool: {
            tokens: [string, ...string[]];
            object_id: string;
            initial_shared_version: number | null;
            sui_exchange: string;
            is_active: boolean;
            extra: {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            } | {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            } | {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            } | {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
        } & {
            [k: string]: unknown;
        };
        weight: number;
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
    }, {
        pool: {
            tokens: [string, ...string[]];
            object_id: string;
            initial_shared_version: number | null;
            sui_exchange: string;
            is_active: boolean;
            extra: {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            } | {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            } | {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            } | {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
        } & {
            [k: string]: unknown;
        };
        weight: number;
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
    }>>;
    edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
    amount_in: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
    amount_out: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
        pool: z.ZodObject<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, z.ZodTypeAny, "passthrough">>;
        weight: z.ZodNumber;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "strip", z.ZodTypeAny, {
        pool: {
            tokens: [string, ...string[]];
            object_id: string;
            initial_shared_version: number | null;
            sui_exchange: string;
            is_active: boolean;
            extra: {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            } | {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            } | {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            } | {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
        } & {
            [k: string]: unknown;
        };
        weight: number;
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
    }, {
        pool: {
            tokens: [string, ...string[]];
            object_id: string;
            initial_shared_version: number | null;
            sui_exchange: string;
            is_active: boolean;
            extra: {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            } | {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            } | {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            } | {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
        } & {
            [k: string]: unknown;
        };
        weight: number;
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
    }>>;
    edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
    amount_in: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
    amount_out: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
        pool: z.ZodObject<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
            object_id: z.ZodString;
            initial_shared_version: z.ZodNullable<z.ZodNumber>;
            sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof SuiExchange>, z.ZodString]>;
            tokens: z.ZodArray<z.ZodString, "atleastone">;
            is_active: z.ZodBoolean;
            extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                AFTERMATH: z.ZodObject<{
                    lp_coin_type: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    lp_coin_type: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                DEEPBOOK: z.ZodObject<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    pool_type: z.ZodString;
                    lot_size: z.ZodBigInt;
                    min_size: z.ZodBigInt;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }, {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                TURBOS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                    fee_type: z.ZodString;
                    tick_spacing: z.ZodNumber;
                    tick_current_index: z.ZodNumber;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }, {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                CETUS: z.ZodObject<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    coin_type_a: z.ZodString;
                    coin_type_b: z.ZodString;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }, {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                FLOWX: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                    fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }, {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{
                KRIYA: z.ZodObject<{
                    is_v3: z.ZodBoolean;
                }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                    is_v3: z.ZodBoolean;
                }, z.ZodTypeAny, "passthrough">>;
            }, "strip", z.ZodTypeAny, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }, {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
        }, z.ZodTypeAny, "passthrough">>;
        weight: z.ZodNumber;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "strip", z.ZodTypeAny, {
        pool: {
            tokens: [string, ...string[]];
            object_id: string;
            initial_shared_version: number | null;
            sui_exchange: string;
            is_active: boolean;
            extra: {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            } | {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            } | {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            } | {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
        } & {
            [k: string]: unknown;
        };
        weight: number;
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
    }, {
        pool: {
            tokens: [string, ...string[]];
            object_id: string;
            initial_shared_version: number | null;
            sui_exchange: string;
            is_active: boolean;
            extra: {
                AFTERMATH: {
                    lp_coin_type: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                DEEPBOOK: {
                    min_size: bigint;
                    pool_type: string;
                    lot_size: bigint;
                } & {
                    [k: string]: unknown;
                };
            } | {
                TURBOS: {
                    coin_type_a: string;
                    coin_type_b: string;
                    fee_type: string;
                    tick_spacing: number;
                    tick_current_index: number;
                } & {
                    [k: string]: unknown;
                };
            } | {
                CETUS: {
                    coin_type_a: string;
                    coin_type_b: string;
                } & {
                    [k: string]: unknown;
                };
            } | {
                FLOWX: {
                    is_v3: boolean;
                    fee_rate?: number | null | undefined;
                } & {
                    [k: string]: unknown;
                };
            } | {
                KRIYA: {
                    is_v3: boolean;
                } & {
                    [k: string]: unknown;
                };
            } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
        } & {
            [k: string]: unknown;
        };
        weight: number;
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
    }>>;
    edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
    amount_in: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
    amount_out: z.ZodObject<{
        token: z.ZodString;
        amount: z.ZodBigInt;
    }, "strip", z.ZodTypeAny, {
        token: string;
        amount: bigint;
    }, {
        token: string;
        amount: bigint;
    }>;
}, z.ZodTypeAny, "passthrough">>;
export type Trade = z.infer<typeof tradeSchema>;
export {};
//# sourceMappingURL=trade.d.ts.map