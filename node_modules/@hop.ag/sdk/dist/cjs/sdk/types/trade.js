"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tradeSchema = exports.poolExtraSchema = exports.SuiExchange = void 0;
const zod_1 = require("zod");
var SuiExchange;
(function (SuiExchange) {
    SuiExchange["CETUS"] = "CETUS";
    SuiExchange["FLOWX"] = "FLOWX";
    SuiExchange["TURBOS"] = "TURBOS";
    SuiExchange["AFTERMATH"] = "AFTERMATH";
    SuiExchange["KRIYA"] = "KRIYA";
    SuiExchange["BLUEMOVE"] = "BLUEMOVE";
    SuiExchange["DEEPBOOK"] = "DEEPBOOK";
    SuiExchange["SUISWAP"] = "SUISWAP";
    SuiExchange["HOPFUN"] = "HOPFUN";
    SuiExchange["BLUEFIN"] = "BLUEFIN";
})(SuiExchange || (exports.SuiExchange = SuiExchange = {}));
const suiExchangeSchema = zod_1.z.nativeEnum(SuiExchange).or(zod_1.z.string());
exports.poolExtraSchema = zod_1.z.union([
    zod_1.z.object({
        AFTERMATH: zod_1.z.object({
            lp_coin_type: zod_1.z.string(),
        }).passthrough(),
    }),
    zod_1.z.object({
        DEEPBOOK: zod_1.z.object({
            pool_type: zod_1.z.string(),
            lot_size: zod_1.z.coerce.bigint(),
            min_size: zod_1.z.coerce.bigint()
        }).passthrough(),
    }),
    zod_1.z.object({
        TURBOS: zod_1.z.object({
            coin_type_a: zod_1.z.string(),
            coin_type_b: zod_1.z.string(),
            fee_type: zod_1.z.string(),
            tick_spacing: zod_1.z.number(),
            tick_current_index: zod_1.z.number(),
        }).passthrough(),
    }),
    zod_1.z.object({
        CETUS: zod_1.z.object({
            coin_type_a: zod_1.z.string(),
            coin_type_b: zod_1.z.string(),
        }).passthrough(),
    }),
    zod_1.z.object({
        FLOWX: zod_1.z.object({
            is_v3: zod_1.z.boolean(),
            fee_rate: zod_1.z.number().nullish(),
        }).passthrough()
    }),
    zod_1.z.object({
        KRIYA: zod_1.z.object({
            is_v3: zod_1.z.boolean()
        }).passthrough()
    }),
    zod_1.z.object({}).passthrough()
]);
const tradePoolSchema = zod_1.z.object({
    object_id: zod_1.z.string(),
    initial_shared_version: zod_1.z.number().nullable(),
    sui_exchange: suiExchangeSchema,
    tokens: zod_1.z.array(zod_1.z.string()).nonempty(),
    is_active: zod_1.z.boolean(),
    extra: exports.poolExtraSchema.nullable(),
}).passthrough();
const tokenAmountSchema = zod_1.z.object({
    token: zod_1.z.string(),
    amount: zod_1.z.coerce.bigint(),
});
const tradeNodeSchema = zod_1.z.object({
    pool: tradePoolSchema,
    weight: zod_1.z.number().nonnegative(),
    amount_in: tokenAmountSchema,
    amount_out: tokenAmountSchema,
});
exports.tradeSchema = zod_1.z.object({
    nodes: zod_1.z.record(tradeNodeSchema),
    edges: zod_1.z.record(zod_1.z.array(zod_1.z.string())),
    amount_in: tokenAmountSchema,
    amount_out: tokenAmountSchema,
}).passthrough();
//# sourceMappingURL=trade.js.map