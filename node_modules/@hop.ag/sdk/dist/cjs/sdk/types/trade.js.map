{"version": 3, "file": "trade.js", "sourceRoot": "", "sources": ["../../../../src/sdk/types/trade.ts"], "names": [], "mappings": ";;;AAAA,6BAAwB;AAExB,IAAY,WAWX;AAXD,WAAY,WAAW;IACrB,8BAAe,CAAA;IACf,8BAAe,CAAA;IACf,gCAAiB,CAAA;IACjB,sCAAuB,CAAA;IACvB,8BAAe,CAAA;IACf,oCAAqB,CAAA;IACrB,oCAAqB,CAAA;IACrB,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;AACrB,CAAC,EAXW,WAAW,2BAAX,WAAW,QAWtB;AAED,MAAM,iBAAiB,GAAG,OAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAEtD,QAAA,eAAe,GAAG,OAAC,CAAC,KAAK,CAAC;IACrC,OAAC,CAAC,MAAM,CAAC;QACP,SAAS,EAAE,OAAC,CAAC,MAAM,CAAC;YAClB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;SACzB,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,OAAC,CAAC,MAAM,CAAC;QACP,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC;YACjB,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;YACrB,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,MAAM,EAAE;YAC3B,QAAQ,EAAE,OAAC,CAAC,MAAM,CAAC,MAAM,EAAE;SAC5B,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,OAAC,CAAC,MAAM,CAAC;QACP,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC;YACf,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;YACvB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;YACvB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE;YACpB,YAAY,EAAE,OAAC,CAAC,MAAM,EAAE;YACxB,kBAAkB,EAAE,OAAC,CAAC,MAAM,EAAE;SAC/B,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,OAAC,CAAC,MAAM,CAAC;QACP,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;YACd,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;YACvB,WAAW,EAAE,OAAC,CAAC,MAAM,EAAE;SACxB,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,OAAC,CAAC,MAAM,CAAC;QACP,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;YACd,KAAK,EAAE,OAAC,CAAC,OAAO,EAAE;YAClB,QAAQ,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE;SAC/B,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,OAAC,CAAC,MAAM,CAAC;QACP,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC;YACd,KAAK,EAAE,OAAC,CAAC,OAAO,EAAE;SACnB,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,OAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE;CAC3B,CAAC,CAAC;AAIH,MAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE,OAAC,CAAC,MAAM,EAAE;IACrB,sBAAsB,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7C,YAAY,EAAE,iBAAiB;IAC/B,MAAM,EAAE,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACtC,SAAS,EAAE,OAAC,CAAC,OAAO,EAAE;IACtB,KAAK,EAAE,uBAAe,CAAC,QAAQ,EAAE;CAClC,CAAC,CAAC,WAAW,EAAE,CAAC;AAIjB,MAAM,iBAAiB,GAAG,OAAC,CAAC,MAAM,CAAC;IACjC,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;IACjB,MAAM,EAAE,OAAC,CAAC,MAAM,CAAC,MAAM,EAAE;CAC1B,CAAC,CAAC;AAEH,MAAM,eAAe,GAAG,OAAC,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE,eAAe;IACrB,MAAM,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IAChC,SAAS,EAAE,iBAAiB;IAC5B,UAAU,EAAE,iBAAiB;CAC9B,CAAC,CAAC;AAIU,QAAA,WAAW,GAAG,OAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,eAAe,CAAC;IAChC,KAAK,EAAE,OAAC,CAAC,MAAM,CAAC,OAAC,CAAC,KAAK,CAAC,OAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACpC,SAAS,EAAE,iBAAiB;IAC5B,UAAU,EAAE,iBAAiB;CAC9B,CAAC,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { z } from \"zod\";\n\nexport enum SuiExchange {\n  CETUS = \"CETUS\",\n  FLOWX = \"FLOWX\",\n  TURBOS = \"TURBOS\",\n  AFTERMATH = \"AFTERMATH\",\n  KRIYA = \"KRIYA\",\n  BLUEMOVE = \"BLUEMOVE\",\n  DEEPBOOK = \"DEEPBOOK\",\n  SUISWAP = \"SUISWAP\",\n  HOPFUN = \"HOPFUN\",\n  BLUEFIN = \"BLUEFIN\"\n}\n\nconst suiExchangeSchema = z.nativeEnum(SuiExchange).or(z.string());\n\nexport const poolExtraSchema = z.union([\n  z.object({\n    AFTERMATH: z.object({\n      lp_coin_type: z.string(),\n    }).passthrough(),\n  }),\n  z.object({\n    DEEPBOOK: z.object({\n      pool_type: z.string(),\n      lot_size: z.coerce.bigint(),\n      min_size: z.coerce.bigint()\n    }).passthrough(),\n  }),\n  z.object({\n    TURBOS: z.object({\n      coin_type_a: z.string(),\n      coin_type_b: z.string(),\n      fee_type: z.string(),\n      tick_spacing: z.number(),\n      tick_current_index: z.number(),\n    }).passthrough(),\n  }),\n  z.object({\n    CETUS: z.object({\n      coin_type_a: z.string(),\n      coin_type_b: z.string(),\n    }).passthrough(),\n  }),\n  z.object({\n    FLOWX: z.object({\n      is_v3: z.boolean(),\n      fee_rate: z.number().nullish(),\n    }).passthrough()\n  }),\n  z.object({\n    KRIYA: z.object({\n      is_v3: z.boolean()\n    }).passthrough()\n  }),\n  z.object({}).passthrough()\n]);\n\nexport type PoolExtra = z.infer<typeof poolExtraSchema>;\n\nconst tradePoolSchema = z.object({\n  object_id: z.string(),\n  initial_shared_version: z.number().nullable(),\n  sui_exchange: suiExchangeSchema,\n  tokens: z.array(z.string()).nonempty(),\n  is_active: z.boolean(),\n  extra: poolExtraSchema.nullable(),\n}).passthrough();\n\nexport type TradePool = z.infer<typeof tradePoolSchema>;\n\nconst tokenAmountSchema = z.object({\n  token: z.string(),\n  amount: z.coerce.bigint(),\n});\n\nconst tradeNodeSchema = z.object({\n  pool: tradePoolSchema,\n  weight: z.number().nonnegative(),\n  amount_in: tokenAmountSchema,\n  amount_out: tokenAmountSchema,\n});\n\nexport type TradeNode = z.infer<typeof tradeNodeSchema>;\n\nexport const tradeSchema = z.object({\n  nodes: z.record(tradeNodeSchema),\n  edges: z.record(z.array(z.string())),\n  amount_in: tokenAmountSchema,\n  amount_out: tokenAmountSchema,\n}).passthrough();\n\nexport type Trade = z.infer<typeof tradeSchema>;\n"]}