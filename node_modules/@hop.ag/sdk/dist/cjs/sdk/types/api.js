"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.priceResponseSchema = exports.tokensResponseSchema = exports.compileResponseSchema = exports.swapAPIResponseSchema = exports.compileRequestSchema = exports.builderRequestSchema = void 0;
const zod_1 = require("zod");
const trade_js_1 = require("./trade.js");
const coinIdSchema = zod_1.z.object({
    object_id: zod_1.z.string(),
    version: zod_1.z.string(),
    digest: zod_1.z.string(),
});
exports.builderRequestSchema = zod_1.z.object({
    sender_address: zod_1.z.string(),
    user_input_coins: zod_1.z.array(zod_1.z.object({
        object_id: coinIdSchema,
        coin_type: zod_1.z.string(),
        amount: zod_1.z.string(),
    })),
    sponsored: zod_1.z.optional(zod_1.z.boolean()),
    gas_coins: zod_1.z.array(coinIdSchema),
    gas_budget: zod_1.z.number(),
    max_slippage_bps: zod_1.z.optional(zod_1.z.number()),
    api_fee_bps: zod_1.z.optional(zod_1.z.number()),
    api_fee_wallet: zod_1.z.optional(zod_1.z.string()),
    charge_fees_in_sui: zod_1.z.optional(zod_1.z.boolean()),
    base_transaction: zod_1.z.optional(zod_1.z.string()),
    input_coin_argument: zod_1.z.optional(zod_1.z.number()),
    input_coin_argument_nested: zod_1.z.optional(zod_1.z.array(zod_1.z.number()).length(2)),
    input_coin_argument_input: zod_1.z.optional(zod_1.z.number()),
    return_output_coin_argument: zod_1.z.optional(zod_1.z.boolean())
}).passthrough();
exports.compileRequestSchema = zod_1.z.object({
    trade: trade_js_1.tradeSchema.passthrough(),
    builder_request: exports.builderRequestSchema.passthrough(),
});
exports.swapAPIResponseSchema = zod_1.z.object({
    total_tests: zod_1.z.number(),
    errors: zod_1.z.number(),
    trade: trade_js_1.tradeSchema.passthrough().nullable(),
}).passthrough();
exports.compileResponseSchema = zod_1.z.object({
    tx: zod_1.z.string(),
    output_coin: zod_1.z.string().nullish(),
});
exports.tokensResponseSchema = zod_1.z.object({
    tokens: zod_1.z.array(zod_1.z.object({
        coin_type: zod_1.z.string(),
        name: zod_1.z.string(),
        ticker: zod_1.z.string(),
        icon_url: zod_1.z.string(),
        decimals: zod_1.z.number(),
        token_order: zod_1.z.nullable(zod_1.z.number())
    }).passthrough())
}).passthrough();
exports.priceResponseSchema = zod_1.z.object({
    coin_type: zod_1.z.string(),
    price_sui: zod_1.z.number(),
    sui_price: zod_1.z.number()
}).passthrough();
//# sourceMappingURL=api.js.map