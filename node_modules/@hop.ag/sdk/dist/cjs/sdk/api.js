"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HopApi = void 0;
const client_1 = require("@mysten/sui/client");
const quote_js_1 = require("./routes/quote.js");
const tx_js_1 = require("./routes/tx.js");
const tokens_js_1 = require("./routes/tokens.js");
const price_js_1 = require("./routes/price.js");
class HopApi {
    constructor(rpc_endpoint, options, use_v2 = true) {
        this.client = new client_1.SuiClient({ url: rpc_endpoint });
        this.options = options;
        this.use_v2 = use_v2;
        this.validate_api_key();
        this.validate_fee();
    }
    validate_api_key() {
        if (!this.options.api_key.startsWith("hopapi")) {
            console.error("Error > Invalid api key:", this.options.api_key, ". Please contact us at hop.ag to request a new key.");
        }
    }
    validate_fee() {
        let fee_bps = this.options.fee_bps;
        if (fee_bps < 0) {
            console.error("> fee_bps must be positive.");
        }
        else if (fee_bps > 500) {
            console.error("> fee_bps must be less than or equal to 5% (500 bps).");
        }
        this.options.fee_bps = Math.max(this.options.fee_bps, 0);
        this.options.fee_bps = Math.min(this.options.fee_bps, 500);
        this.options.fee_bps = Number(this.options.fee_bps.toFixed(0));
    }
    /*
     * Routes
     */
    async fetchQuote(quote) {
        return (0, quote_js_1.fetchQuote)(this, quote);
    }
    async fetchTx(tx) {
        return (0, tx_js_1.fetchTx)(this, tx);
    }
    async fetchTokens() {
        return (0, tokens_js_1.fetchTokens)(this);
    }
    async fetchPrice(price) {
        return (0, price_js_1.fetchPrice)(this, price);
    }
}
exports.HopApi = HopApi;
//# sourceMappingURL=api.js.map