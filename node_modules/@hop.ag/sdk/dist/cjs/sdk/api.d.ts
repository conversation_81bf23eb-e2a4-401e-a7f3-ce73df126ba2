import { SuiClient } from "@mysten/sui/client";
import { GetQuoteParams, GetQuoteResponse } from "./routes/quote.js";
import { GetTxParams, GetTxResponse } from "./routes/tx.js";
import { GetTokensResponse } from "./routes/tokens.js";
import { GetPriceParams, GetPriceResponse } from "./routes/price.js";
export interface HopApiOptions {
    api_key: string;
    fee_bps: number;
    charge_fees_in_sui?: boolean;
    fee_wallet?: string;
    hop_server_url?: string;
}
export declare class HopApi {
    readonly client: SuiClient;
    readonly options: HopApiOptions;
    readonly use_v2: boolean;
    constructor(rpc_endpoint: string, options: HopApiOptions, use_v2?: boolean);
    private validate_api_key;
    private validate_fee;
    fetchQuote(quote: GetQuoteParams): Promise<GetQuoteResponse>;
    fetchTx(tx: GetTxParams): Promise<GetTxResponse>;
    fetchTokens(): Promise<GetTokensResponse>;
    fetchPrice(price: GetPriceParams): Promise<GetPriceResponse>;
}
//# sourceMappingURL=api.d.ts.map