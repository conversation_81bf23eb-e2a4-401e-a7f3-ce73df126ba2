{"version": 3, "file": "trade.js", "sourceRoot": "", "sources": ["../../../../src/sdk/types/trade.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAExB,MAAM,CAAN,IAAY,WAWX;AAXD,WAAY,WAAW;IACrB,8BAAe,CAAA;IACf,8BAAe,CAAA;IACf,gCAAiB,CAAA;IACjB,sCAAuB,CAAA;IACvB,8BAAe,CAAA;IACf,oCAAqB,CAAA;IACrB,oCAAqB,CAAA;IACrB,kCAAmB,CAAA;IACnB,gCAAiB,CAAA;IACjB,kCAAmB,CAAA;AACrB,CAAC,EAXW,WAAW,KAAX,WAAW,QAWtB;AAED,MAAM,iBAAiB,GAAG,CAAC,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;AAEnE,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,CAAC,KAAK,CAAC;IACrC,CAAC,CAAC,MAAM,CAAC;QACP,SAAS,EAAE,CAAC,CAAC,MAAM,CAAC;YAClB,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE;SACzB,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC;YACjB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;YACrB,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE;YAC3B,QAAQ,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE;SAC5B,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC;YACf,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;YACvB,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;YACvB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;YACpB,YAAY,EAAE,CAAC,CAAC,MAAM,EAAE;YACxB,kBAAkB,EAAE,CAAC,CAAC,MAAM,EAAE;SAC/B,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;YACd,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;YACvB,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;SACxB,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;YACd,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE;YAClB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE;SAC/B,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC;QACP,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC;YACd,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE;SACnB,CAAC,CAAC,WAAW,EAAE;KACjB,CAAC;IACF,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,WAAW,EAAE;CAC3B,CAAC,CAAC;AAIH,MAAM,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC;IAC/B,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;IACrB,sBAAsB,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IAC7C,YAAY,EAAE,iBAAiB;IAC/B,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;IACtC,SAAS,EAAE,CAAC,CAAC,OAAO,EAAE;IACtB,KAAK,EAAE,eAAe,CAAC,QAAQ,EAAE;CAClC,CAAC,CAAC,WAAW,EAAE,CAAC;AAIjB,MAAM,iBAAiB,GAAG,CAAC,CAAC,MAAM,CAAC;IACjC,KAAK,EAAE,CAAC,CAAC,MAAM,EAAE;IACjB,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE;CAC1B,CAAC,CAAC;AAEH,MAAM,eAAe,GAAG,CAAC,CAAC,MAAM,CAAC;IAC/B,IAAI,EAAE,eAAe;IACrB,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,WAAW,EAAE;IAChC,SAAS,EAAE,iBAAiB;IAC5B,UAAU,EAAE,iBAAiB;CAC9B,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,WAAW,GAAG,CAAC,CAAC,MAAM,CAAC;IAClC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC;IAChC,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;IACpC,SAAS,EAAE,iBAAiB;IAC5B,UAAU,EAAE,iBAAiB;CAC9B,CAAC,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { z } from \"zod\";\n\nexport enum SuiExchange {\n  CETUS = \"CETUS\",\n  FLOWX = \"FLOWX\",\n  TURBOS = \"TURBOS\",\n  AFTERMATH = \"AFTERMATH\",\n  KRIYA = \"KRIYA\",\n  BLUEMOVE = \"BLUEMOVE\",\n  DEEPBOOK = \"DEEPBOOK\",\n  SUISWAP = \"SUISWAP\",\n  HOPFUN = \"HOPFUN\",\n  BLUEFIN = \"BLUEFIN\"\n}\n\nconst suiExchangeSchema = z.nativeEnum(SuiExchange).or(z.string());\n\nexport const poolExtraSchema = z.union([\n  z.object({\n    AFTERMATH: z.object({\n      lp_coin_type: z.string(),\n    }).passthrough(),\n  }),\n  z.object({\n    DEEPBOOK: z.object({\n      pool_type: z.string(),\n      lot_size: z.coerce.bigint(),\n      min_size: z.coerce.bigint()\n    }).passthrough(),\n  }),\n  z.object({\n    TURBOS: z.object({\n      coin_type_a: z.string(),\n      coin_type_b: z.string(),\n      fee_type: z.string(),\n      tick_spacing: z.number(),\n      tick_current_index: z.number(),\n    }).passthrough(),\n  }),\n  z.object({\n    CETUS: z.object({\n      coin_type_a: z.string(),\n      coin_type_b: z.string(),\n    }).passthrough(),\n  }),\n  z.object({\n    FLOWX: z.object({\n      is_v3: z.boolean(),\n      fee_rate: z.number().nullish(),\n    }).passthrough()\n  }),\n  z.object({\n    KRIYA: z.object({\n      is_v3: z.boolean()\n    }).passthrough()\n  }),\n  z.object({}).passthrough()\n]);\n\nexport type PoolExtra = z.infer<typeof poolExtraSchema>;\n\nconst tradePoolSchema = z.object({\n  object_id: z.string(),\n  initial_shared_version: z.number().nullable(),\n  sui_exchange: suiExchangeSchema,\n  tokens: z.array(z.string()).nonempty(),\n  is_active: z.boolean(),\n  extra: poolExtraSchema.nullable(),\n}).passthrough();\n\nexport type TradePool = z.infer<typeof tradePoolSchema>;\n\nconst tokenAmountSchema = z.object({\n  token: z.string(),\n  amount: z.coerce.bigint(),\n});\n\nconst tradeNodeSchema = z.object({\n  pool: tradePoolSchema,\n  weight: z.number().nonnegative(),\n  amount_in: tokenAmountSchema,\n  amount_out: tokenAmountSchema,\n});\n\nexport type TradeNode = z.infer<typeof tradeNodeSchema>;\n\nexport const tradeSchema = z.object({\n  nodes: z.record(tradeNodeSchema),\n  edges: z.record(z.array(z.string())),\n  amount_in: tokenAmountSchema,\n  amount_out: tokenAmountSchema,\n}).passthrough();\n\nexport type Trade = z.infer<typeof tradeSchema>;\n"]}