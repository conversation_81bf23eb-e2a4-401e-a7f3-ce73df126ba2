{"version": 3, "file": "api.js", "sourceRoot": "", "sources": ["../../../../src/sdk/types/api.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,WAAW,EAAE,MAAM,YAAY,CAAC;AAEzC,MAAM,YAAY,GAAG,CAAC,CAAC,MAAM,CAAC;IAC5B,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;IACrB,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;IACnB,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;CACnB,CAAC,CAAC;AAEH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC3C,cAAc,EAAE,CAAC,CAAC,MAAM,EAAE;IAC1B,gBAAgB,EAAE,CAAC,CAAC,KAAK,CACvB,CAAC,CAAC,MAAM,CAAC;QACP,SAAS,EAAE,YAAY;QACvB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;QACrB,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;KACnB,CAAC,CACH;IAED,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAClC,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC;IAEhC,UAAU,EAAE,CAAC,CAAC,MAAM,EAAE;IAEtB,gBAAgB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAExC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACnC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACtC,kBAAkB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;IAE3C,gBAAgB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IACxC,mBAAmB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAC3C,0BAA0B,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACrE,yBAAyB,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;IAEjD,2BAA2B,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;CACrD,CAAC,CAAC,WAAW,EAAE,CAAC;AAIjB,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC3C,KAAK,EAAE,WAAW,CAAC,WAAW,EAAE;IAChC,eAAe,EAAE,oBAAoB,CAAC,WAAW,EAAE;CACpD,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC5C,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE;IACvB,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;IAClB,KAAK,EAAE,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,EAAE;CAC5C,CAAC,CAAC,WAAW,EAAE,CAAC;AAIjB,MAAM,CAAC,MAAM,qBAAqB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC5C,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE;IACd,WAAW,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE;CAClC,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,oBAAoB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC3C,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC;QACvB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;QACrB,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE;QAChB,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;QAClB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;QACpB,QAAQ,EAAE,CAAC,CAAC,MAAM,EAAE;QACpB,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;KACpC,CAAC,CAAC,WAAW,EAAE,CAAC;CAClB,CAAC,CAAC,WAAW,EAAE,CAAC;AAEjB,MAAM,CAAC,MAAM,mBAAmB,GAAG,CAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;IACrB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;IACrB,SAAS,EAAE,CAAC,CAAC,MAAM,EAAE;CACtB,CAAC,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["import { z } from \"zod\";\nimport { tradeSchema } from \"./trade.js\";\n\nconst coinIdSchema = z.object({\n  object_id: z.string(),\n  version: z.string(),\n  digest: z.string(),\n});\n\nexport const builderRequestSchema = z.object({\n  sender_address: z.string(),\n  user_input_coins: z.array(\n    z.object({\n      object_id: coinIdSchema,\n      coin_type: z.string(),\n      amount: z.string(),\n    }),\n  ),\n  \n  sponsored: z.optional(z.boolean()),\n  gas_coins: z.array(coinIdSchema),\n  \n  gas_budget: z.number(),\n  \n  max_slippage_bps: z.optional(z.number()),\n  \n  api_fee_bps: z.optional(z.number()),\n  api_fee_wallet: z.optional(z.string()),\n  charge_fees_in_sui: z.optional(z.boolean()),\n\n  base_transaction: z.optional(z.string()),\n  input_coin_argument: z.optional(z.number()),\n  input_coin_argument_nested: z.optional(z.array(z.number()).length(2)),\n  input_coin_argument_input: z.optional(z.number()),\n  \n  return_output_coin_argument: z.optional(z.boolean())\n}).passthrough();\n\nexport type BuilderRequest = z.infer<typeof builderRequestSchema>;\n\nexport const compileRequestSchema = z.object({\n  trade: tradeSchema.passthrough(),\n  builder_request: builderRequestSchema.passthrough(),\n});\n\nexport type CompileRequest = z.infer<typeof compileRequestSchema>;\n\nexport const swapAPIResponseSchema = z.object({\n  total_tests: z.number(),\n  errors: z.number(),\n  trade: tradeSchema.passthrough().nullable(),\n}).passthrough();\n\nexport type SwapAPIResponse = z.infer<typeof swapAPIResponseSchema>;\n\nexport const compileResponseSchema = z.object({\n  tx: z.string(),\n  output_coin: z.string().nullish(),\n});\n\nexport type CompileResponse = z.infer<typeof compileResponseSchema>;\n\nexport const tokensResponseSchema = z.object({\n  tokens: z.array(z.object({\n    coin_type: z.string(),\n    name: z.string(),\n    ticker: z.string(),\n    icon_url: z.string(),\n    decimals: z.number(),\n    token_order: z.nullable(z.number())\n  }).passthrough())\n}).passthrough();\n\nexport const priceResponseSchema = z.object({\n  coin_type: z.string(),\n  price_sui: z.number(),\n  sui_price: z.number()\n}).passthrough();\n"]}