import { z } from "zod";
export declare const builderRequestSchema: z.ZodObject<{
    sender_address: z.ZodString;
    user_input_coins: z.<PERSON><PERSON><PERSON><z.ZodObject<{
        object_id: z.ZodObject<{
            object_id: z.ZodString;
            version: z.ZodString;
            digest: z.ZodString;
        }, "strip", z.Z<PERSON>ype<PERSON>ny, {
            object_id: string;
            version: string;
            digest: string;
        }, {
            object_id: string;
            version: string;
            digest: string;
        }>;
        coin_type: z.ZodString;
        amount: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        object_id: {
            object_id: string;
            version: string;
            digest: string;
        };
        amount: string;
        coin_type: string;
    }, {
        object_id: {
            object_id: string;
            version: string;
            digest: string;
        };
        amount: string;
        coin_type: string;
    }>, "many">;
    sponsored: z.ZodOptional<z.ZodBoolean>;
    gas_coins: z.ZodArray<z.ZodObject<{
        object_id: z.ZodString;
        version: z.ZodString;
        digest: z.ZodString;
    }, "strip", z.<PERSON>, {
        object_id: string;
        version: string;
        digest: string;
    }, {
        object_id: string;
        version: string;
        digest: string;
    }>, "many">;
    gas_budget: z.ZodNumber;
    max_slippage_bps: z.ZodOptional<z.ZodNumber>;
    api_fee_bps: z.ZodOptional<z.ZodNumber>;
    api_fee_wallet: z.ZodOptional<z.ZodString>;
    charge_fees_in_sui: z.ZodOptional<z.ZodBoolean>;
    base_transaction: z.ZodOptional<z.ZodString>;
    input_coin_argument: z.ZodOptional<z.ZodNumber>;
    input_coin_argument_nested: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    input_coin_argument_input: z.ZodOptional<z.ZodNumber>;
    return_output_coin_argument: z.ZodOptional<z.ZodBoolean>;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    sender_address: z.ZodString;
    user_input_coins: z.ZodArray<z.ZodObject<{
        object_id: z.ZodObject<{
            object_id: z.ZodString;
            version: z.ZodString;
            digest: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: string;
            version: string;
            digest: string;
        }, {
            object_id: string;
            version: string;
            digest: string;
        }>;
        coin_type: z.ZodString;
        amount: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        object_id: {
            object_id: string;
            version: string;
            digest: string;
        };
        amount: string;
        coin_type: string;
    }, {
        object_id: {
            object_id: string;
            version: string;
            digest: string;
        };
        amount: string;
        coin_type: string;
    }>, "many">;
    sponsored: z.ZodOptional<z.ZodBoolean>;
    gas_coins: z.ZodArray<z.ZodObject<{
        object_id: z.ZodString;
        version: z.ZodString;
        digest: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        object_id: string;
        version: string;
        digest: string;
    }, {
        object_id: string;
        version: string;
        digest: string;
    }>, "many">;
    gas_budget: z.ZodNumber;
    max_slippage_bps: z.ZodOptional<z.ZodNumber>;
    api_fee_bps: z.ZodOptional<z.ZodNumber>;
    api_fee_wallet: z.ZodOptional<z.ZodString>;
    charge_fees_in_sui: z.ZodOptional<z.ZodBoolean>;
    base_transaction: z.ZodOptional<z.ZodString>;
    input_coin_argument: z.ZodOptional<z.ZodNumber>;
    input_coin_argument_nested: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    input_coin_argument_input: z.ZodOptional<z.ZodNumber>;
    return_output_coin_argument: z.ZodOptional<z.ZodBoolean>;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    sender_address: z.ZodString;
    user_input_coins: z.ZodArray<z.ZodObject<{
        object_id: z.ZodObject<{
            object_id: z.ZodString;
            version: z.ZodString;
            digest: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: string;
            version: string;
            digest: string;
        }, {
            object_id: string;
            version: string;
            digest: string;
        }>;
        coin_type: z.ZodString;
        amount: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        object_id: {
            object_id: string;
            version: string;
            digest: string;
        };
        amount: string;
        coin_type: string;
    }, {
        object_id: {
            object_id: string;
            version: string;
            digest: string;
        };
        amount: string;
        coin_type: string;
    }>, "many">;
    sponsored: z.ZodOptional<z.ZodBoolean>;
    gas_coins: z.ZodArray<z.ZodObject<{
        object_id: z.ZodString;
        version: z.ZodString;
        digest: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        object_id: string;
        version: string;
        digest: string;
    }, {
        object_id: string;
        version: string;
        digest: string;
    }>, "many">;
    gas_budget: z.ZodNumber;
    max_slippage_bps: z.ZodOptional<z.ZodNumber>;
    api_fee_bps: z.ZodOptional<z.ZodNumber>;
    api_fee_wallet: z.ZodOptional<z.ZodString>;
    charge_fees_in_sui: z.ZodOptional<z.ZodBoolean>;
    base_transaction: z.ZodOptional<z.ZodString>;
    input_coin_argument: z.ZodOptional<z.ZodNumber>;
    input_coin_argument_nested: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
    input_coin_argument_input: z.ZodOptional<z.ZodNumber>;
    return_output_coin_argument: z.ZodOptional<z.ZodBoolean>;
}, z.ZodTypeAny, "passthrough">>;
export type BuilderRequest = z.infer<typeof builderRequestSchema>;
export declare const compileRequestSchema: z.ZodObject<{
    trade: z.ZodObject<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">>;
    builder_request: z.ZodObject<{
        sender_address: z.ZodString;
        user_input_coins: z.ZodArray<z.ZodObject<{
            object_id: z.ZodObject<{
                object_id: z.ZodString;
                version: z.ZodString;
                digest: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                object_id: string;
                version: string;
                digest: string;
            }, {
                object_id: string;
                version: string;
                digest: string;
            }>;
            coin_type: z.ZodString;
            amount: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }, {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }>, "many">;
        sponsored: z.ZodOptional<z.ZodBoolean>;
        gas_coins: z.ZodArray<z.ZodObject<{
            object_id: z.ZodString;
            version: z.ZodString;
            digest: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: string;
            version: string;
            digest: string;
        }, {
            object_id: string;
            version: string;
            digest: string;
        }>, "many">;
        gas_budget: z.ZodNumber;
        max_slippage_bps: z.ZodOptional<z.ZodNumber>;
        api_fee_bps: z.ZodOptional<z.ZodNumber>;
        api_fee_wallet: z.ZodOptional<z.ZodString>;
        charge_fees_in_sui: z.ZodOptional<z.ZodBoolean>;
        base_transaction: z.ZodOptional<z.ZodString>;
        input_coin_argument: z.ZodOptional<z.ZodNumber>;
        input_coin_argument_nested: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        input_coin_argument_input: z.ZodOptional<z.ZodNumber>;
        return_output_coin_argument: z.ZodOptional<z.ZodBoolean>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        sender_address: z.ZodString;
        user_input_coins: z.ZodArray<z.ZodObject<{
            object_id: z.ZodObject<{
                object_id: z.ZodString;
                version: z.ZodString;
                digest: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                object_id: string;
                version: string;
                digest: string;
            }, {
                object_id: string;
                version: string;
                digest: string;
            }>;
            coin_type: z.ZodString;
            amount: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }, {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }>, "many">;
        sponsored: z.ZodOptional<z.ZodBoolean>;
        gas_coins: z.ZodArray<z.ZodObject<{
            object_id: z.ZodString;
            version: z.ZodString;
            digest: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: string;
            version: string;
            digest: string;
        }, {
            object_id: string;
            version: string;
            digest: string;
        }>, "many">;
        gas_budget: z.ZodNumber;
        max_slippage_bps: z.ZodOptional<z.ZodNumber>;
        api_fee_bps: z.ZodOptional<z.ZodNumber>;
        api_fee_wallet: z.ZodOptional<z.ZodString>;
        charge_fees_in_sui: z.ZodOptional<z.ZodBoolean>;
        base_transaction: z.ZodOptional<z.ZodString>;
        input_coin_argument: z.ZodOptional<z.ZodNumber>;
        input_coin_argument_nested: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        input_coin_argument_input: z.ZodOptional<z.ZodNumber>;
        return_output_coin_argument: z.ZodOptional<z.ZodBoolean>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        sender_address: z.ZodString;
        user_input_coins: z.ZodArray<z.ZodObject<{
            object_id: z.ZodObject<{
                object_id: z.ZodString;
                version: z.ZodString;
                digest: z.ZodString;
            }, "strip", z.ZodTypeAny, {
                object_id: string;
                version: string;
                digest: string;
            }, {
                object_id: string;
                version: string;
                digest: string;
            }>;
            coin_type: z.ZodString;
            amount: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }, {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }>, "many">;
        sponsored: z.ZodOptional<z.ZodBoolean>;
        gas_coins: z.ZodArray<z.ZodObject<{
            object_id: z.ZodString;
            version: z.ZodString;
            digest: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            object_id: string;
            version: string;
            digest: string;
        }, {
            object_id: string;
            version: string;
            digest: string;
        }>, "many">;
        gas_budget: z.ZodNumber;
        max_slippage_bps: z.ZodOptional<z.ZodNumber>;
        api_fee_bps: z.ZodOptional<z.ZodNumber>;
        api_fee_wallet: z.ZodOptional<z.ZodString>;
        charge_fees_in_sui: z.ZodOptional<z.ZodBoolean>;
        base_transaction: z.ZodOptional<z.ZodString>;
        input_coin_argument: z.ZodOptional<z.ZodNumber>;
        input_coin_argument_nested: z.ZodOptional<z.ZodArray<z.ZodNumber, "many">>;
        input_coin_argument_input: z.ZodOptional<z.ZodNumber>;
        return_output_coin_argument: z.ZodOptional<z.ZodBoolean>;
    }, z.ZodTypeAny, "passthrough">>;
}, "strip", z.ZodTypeAny, {
    trade: {
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
        nodes: Record<string, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>;
        edges: Record<string, string[]>;
    } & {
        [k: string]: unknown;
    };
    builder_request: {
        sender_address: string;
        user_input_coins: {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }[];
        gas_coins: {
            object_id: string;
            version: string;
            digest: string;
        }[];
        gas_budget: number;
        sponsored?: boolean | undefined;
        max_slippage_bps?: number | undefined;
        api_fee_bps?: number | undefined;
        api_fee_wallet?: string | undefined;
        charge_fees_in_sui?: boolean | undefined;
        base_transaction?: string | undefined;
        input_coin_argument?: number | undefined;
        input_coin_argument_nested?: number[] | undefined;
        input_coin_argument_input?: number | undefined;
        return_output_coin_argument?: boolean | undefined;
    } & {
        [k: string]: unknown;
    };
}, {
    trade: {
        amount_in: {
            token: string;
            amount: bigint;
        };
        amount_out: {
            token: string;
            amount: bigint;
        };
        nodes: Record<string, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>;
        edges: Record<string, string[]>;
    } & {
        [k: string]: unknown;
    };
    builder_request: {
        sender_address: string;
        user_input_coins: {
            object_id: {
                object_id: string;
                version: string;
                digest: string;
            };
            amount: string;
            coin_type: string;
        }[];
        gas_coins: {
            object_id: string;
            version: string;
            digest: string;
        }[];
        gas_budget: number;
        sponsored?: boolean | undefined;
        max_slippage_bps?: number | undefined;
        api_fee_bps?: number | undefined;
        api_fee_wallet?: string | undefined;
        charge_fees_in_sui?: boolean | undefined;
        base_transaction?: string | undefined;
        input_coin_argument?: number | undefined;
        input_coin_argument_nested?: number[] | undefined;
        input_coin_argument_input?: number | undefined;
        return_output_coin_argument?: boolean | undefined;
    } & {
        [k: string]: unknown;
    };
}>;
export type CompileRequest = z.infer<typeof compileRequestSchema>;
export declare const swapAPIResponseSchema: z.ZodObject<{
    total_tests: z.ZodNumber;
    errors: z.ZodNumber;
    trade: z.ZodNullable<z.ZodObject<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">>>;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    total_tests: z.ZodNumber;
    errors: z.ZodNumber;
    trade: z.ZodNullable<z.ZodObject<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">>>;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    total_tests: z.ZodNumber;
    errors: z.ZodNumber;
    trade: z.ZodNullable<z.ZodObject<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        nodes: z.ZodRecord<z.ZodString, z.ZodObject<{
            pool: z.ZodObject<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                object_id: z.ZodString;
                initial_shared_version: z.ZodNullable<z.ZodNumber>;
                sui_exchange: z.ZodUnion<[z.ZodNativeEnum<typeof import("./trade.js").SuiExchange>, z.ZodString]>;
                tokens: z.ZodArray<z.ZodString, "atleastone">;
                is_active: z.ZodBoolean;
                extra: z.ZodNullable<z.ZodUnion<[z.ZodObject<{
                    AFTERMATH: z.ZodObject<{
                        lp_coin_type: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        lp_coin_type: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    DEEPBOOK: z.ZodObject<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        pool_type: z.ZodString;
                        lot_size: z.ZodBigInt;
                        min_size: z.ZodBigInt;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    TURBOS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                        fee_type: z.ZodString;
                        tick_spacing: z.ZodNumber;
                        tick_current_index: z.ZodNumber;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    CETUS: z.ZodObject<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        coin_type_a: z.ZodString;
                        coin_type_b: z.ZodString;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    FLOWX: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                        fee_rate: z.ZodOptional<z.ZodNullable<z.ZodNumber>>;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{
                    KRIYA: z.ZodObject<{
                        is_v3: z.ZodBoolean;
                    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
                        is_v3: z.ZodBoolean;
                    }, z.ZodTypeAny, "passthrough">>;
                }, "strip", z.ZodTypeAny, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }, {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                }>, z.ZodObject<{}, "passthrough", z.ZodTypeAny, z.objectOutputType<{}, z.ZodTypeAny, "passthrough">, z.objectInputType<{}, z.ZodTypeAny, "passthrough">>]>>;
            }, z.ZodTypeAny, "passthrough">>;
            weight: z.ZodNumber;
            amount_in: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
            amount_out: z.ZodObject<{
                token: z.ZodString;
                amount: z.ZodBigInt;
            }, "strip", z.ZodTypeAny, {
                token: string;
                amount: bigint;
            }, {
                token: string;
                amount: bigint;
            }>;
        }, "strip", z.ZodTypeAny, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectOutputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }, {
            pool: {
                object_id: string;
                initial_shared_version: number | null;
                sui_exchange: string;
                tokens: [string, ...string[]];
                is_active: boolean;
                extra: {
                    AFTERMATH: {
                        lp_coin_type: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    DEEPBOOK: {
                        pool_type: string;
                        lot_size: bigint;
                        min_size: bigint;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    TURBOS: {
                        coin_type_a: string;
                        coin_type_b: string;
                        fee_type: string;
                        tick_spacing: number;
                        tick_current_index: number;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    CETUS: {
                        coin_type_a: string;
                        coin_type_b: string;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    FLOWX: {
                        is_v3: boolean;
                        fee_rate?: number | null | undefined;
                    } & {
                        [k: string]: unknown;
                    };
                } | {
                    KRIYA: {
                        is_v3: boolean;
                    } & {
                        [k: string]: unknown;
                    };
                } | z.objectInputType<{}, z.ZodTypeAny, "passthrough"> | null;
            } & {
                [k: string]: unknown;
            };
            weight: number;
            amount_in: {
                token: string;
                amount: bigint;
            };
            amount_out: {
                token: string;
                amount: bigint;
            };
        }>>;
        edges: z.ZodRecord<z.ZodString, z.ZodArray<z.ZodString, "many">>;
        amount_in: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
        amount_out: z.ZodObject<{
            token: z.ZodString;
            amount: z.ZodBigInt;
        }, "strip", z.ZodTypeAny, {
            token: string;
            amount: bigint;
        }, {
            token: string;
            amount: bigint;
        }>;
    }, z.ZodTypeAny, "passthrough">>>;
}, z.ZodTypeAny, "passthrough">>;
export type SwapAPIResponse = z.infer<typeof swapAPIResponseSchema>;
export declare const compileResponseSchema: z.ZodObject<{
    tx: z.ZodString;
    output_coin: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    tx: string;
    output_coin?: string | null | undefined;
}, {
    tx: string;
    output_coin?: string | null | undefined;
}>;
export type CompileResponse = z.infer<typeof compileResponseSchema>;
export declare const tokensResponseSchema: z.ZodObject<{
    tokens: z.ZodArray<z.ZodObject<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, z.ZodTypeAny, "passthrough">>, "many">;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    tokens: z.ZodArray<z.ZodObject<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, z.ZodTypeAny, "passthrough">>, "many">;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    tokens: z.ZodArray<z.ZodObject<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, "passthrough", z.ZodTypeAny, z.objectOutputType<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, z.ZodTypeAny, "passthrough">, z.objectInputType<{
        coin_type: z.ZodString;
        name: z.ZodString;
        ticker: z.ZodString;
        icon_url: z.ZodString;
        decimals: z.ZodNumber;
        token_order: z.ZodNullable<z.ZodNumber>;
    }, z.ZodTypeAny, "passthrough">>, "many">;
}, z.ZodTypeAny, "passthrough">>;
export declare const priceResponseSchema: z.ZodObject<{
    coin_type: z.ZodString;
    price_sui: z.ZodNumber;
    sui_price: z.ZodNumber;
}, "passthrough", z.ZodTypeAny, z.objectOutputType<{
    coin_type: z.ZodString;
    price_sui: z.ZodNumber;
    sui_price: z.ZodNumber;
}, z.ZodTypeAny, "passthrough">, z.objectInputType<{
    coin_type: z.ZodString;
    price_sui: z.ZodNumber;
    sui_price: z.ZodNumber;
}, z.ZodTypeAny, "passthrough">>;
//# sourceMappingURL=api.d.ts.map