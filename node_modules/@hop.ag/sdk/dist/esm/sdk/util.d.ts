import { z } from "zod";
export interface RequestParams {
    hop_server_url?: string;
    api_key: string;
    data: object;
    method: "get" | "post";
}
export declare function makeAPIRequest<O>({ route, options, responseSchema, }: {
    route: string;
    options: RequestParams;
    responseSchema: z.ZodSchema<O>;
}): Promise<O>;
export declare function getAmountOutWithCommission(amount_out: bigint, fee_bps: number): bigint;
export declare function isSuiType(coin_type: string): boolean;
//# sourceMappingURL=util.d.ts.map