import { tokensResponseSchema } from "../types/api.js";
import { makeAPIRequest } from "../util.js";
export async function fetchTokens(client) {
    const response = await makeAPIRequest({
        route: "tokens",
        options: {
            api_key: client.options.api_key,
            hop_server_url: client.options.hop_server_url,
            data: {},
            method: "post",
        },
        responseSchema: tokensResponseSchema,
    });
    if (response?.tokens) {
        return {
            tokens: response.tokens,
        };
    }
    throw new Error("Unable to get tokens");
}
//# sourceMappingURL=tokens.js.map