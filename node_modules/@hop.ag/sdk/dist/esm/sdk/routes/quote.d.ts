import { <PERSON><PERSON>pi } from "../api.js";
import { Trade } from "../types/trade.js";
export interface GetQuoteParams {
    token_in: string;
    token_out: string;
    amount_in: bigint;
}
export interface GetQuoteResponse {
    amount_out_with_fee: bigint;
    trade: Trade;
}
export declare function fetchQuote(client: HopApi, params: GetQuoteParams): Promise<GetQuoteResponse>;
//# sourceMappingURL=quote.d.ts.map