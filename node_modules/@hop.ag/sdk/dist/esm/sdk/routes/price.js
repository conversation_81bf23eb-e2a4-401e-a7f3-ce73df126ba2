import { makeAPIRequest } from "../util.js";
import { priceResponseSchema } from "../types/api.js";
export async function fetchPrice(client, params) {
    const response = await makeAPIRequest({
        route: `price`,
        options: {
            api_key: client.options.api_key,
            hop_server_url: client.options.hop_server_url,
            data: {
                coin_type: params.coin_type,
            },
            method: "post",
        },
        responseSchema: priceResponseSchema,
    });
    if (response?.coin_type) {
        let price_usd = response.price_sui * response.sui_price;
        return {
            coin_type: response?.coin_type,
            price_sui: response?.price_sui,
            price_usd,
            sui_price: response?.sui_price
        };
    }
    throw new Error("Unable to get price");
}
//# sourceMappingURL=price.js.map