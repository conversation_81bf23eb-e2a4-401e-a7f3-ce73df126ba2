{"version": 3, "file": "quote.js", "sourceRoot": "", "sources": ["../../../../src/sdk/routes/quote.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,qBAAqB,EAAE,MAAM,iBAAiB,CAAC;AAExD,OAAO,EAAE,0BAA0B,EAAE,SAAS,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAanF,MAAM,CAAC,KAAK,UAAU,UAAU,CAC9B,MAAc,EACd,MAAsB;IAEtB,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC;QACpC,KAAK,EAAE,OAAO;QACd,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;YAC/B,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc;YAC7C,IAAI,EAAE;gBACJ,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,SAAS,EAAE,MAAM,CAAC,SAAS;gBAC3B,SAAS,EAAE,MAAM,CAAC,SAAS,CAAC,QAAQ,EAAE;gBACtC,gBAAgB,EAAE,MAAM,CAAC,MAAM;gBAE/B,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;gBACnC,kBAAkB,EAAE,MAAM,CAAC,OAAO,CAAC,kBAAkB;aACtD;YACD,MAAM,EAAE,MAAM;SACf;QACD,cAAc,EAAE,qBAAqB;KACtC,CAAC,CAAC;IAEH,IAAI,QAAQ,EAAE,KAAK,EAAE,CAAC;QACpB,IAAI,mBAAmB,CAAC;QAExB,IAAG,MAAM,CAAC,OAAO,CAAC,kBAAkB,IAAI,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnE,sBAAsB;YACtB,mBAAmB,GAAG,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC;QACzD,CAAC;aAAM,CAAC;YACN,mBAAmB,GAAG,0BAA0B,CAC9C,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,MAAM,EAChC,MAAM,CAAC,OAAO,CAAC,OAAO,CACvB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mBAAmB;YACnB,KAAK,EAAE,QAAQ,CAAC,KAAK;SACtB,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;AACzC,CAAC", "sourcesContent": ["import { Hop<PERSON><PERSON> } from \"../api.js\";\nimport { swapAPIResponseSchema } from \"../types/api.js\";\nimport { Trade } from \"../types/trade.js\";\nimport { getAmountOutWithCommission, isSuiType, makeAPIRequest } from \"../util.js\";\n\nexport interface GetQuoteParams {\n  token_in: string;\n  token_out: string;\n  amount_in: bigint;\n}\n\nexport interface GetQuoteResponse {\n  amount_out_with_fee: bigint;\n  trade: Trade;\n}\n\nexport async function fetchQuote(\n  client: HopApi,\n  params: GetQuoteParams,\n): Promise<GetQuoteResponse> {\n  const response = await makeAPIRequest({\n    route: \"quote\",\n    options: {\n      api_key: client.options.api_key,\n      hop_server_url: client.options.hop_server_url,\n      data: {\n        token_in: params.token_in,\n        token_out: params.token_out,\n        amount_in: params.amount_in.toString(),\n        use_alpha_router: client.use_v2,\n\n        api_fee_bps: client.options.fee_bps,\n        charge_fees_in_sui: client.options.charge_fees_in_sui,\n      },\n      method: \"post\",\n    },\n    responseSchema: swapAPIResponseSchema,\n  });\n\n  if (response?.trade) {\n    let amount_out_with_fee;\n\n    if(client.options.charge_fees_in_sui && isSuiType(params.token_in)) {\n      // fee already charged\n      amount_out_with_fee = response.trade.amount_out.amount;\n    } else {\n      amount_out_with_fee = getAmountOutWithCommission(\n        response.trade.amount_out.amount,\n        client.options.fee_bps\n      );\n    }\n\n    return {\n      amount_out_with_fee,\n      trade: response.trade,\n    };\n  }\n\n  throw new Error(\"Unable to get quote\");\n}\n"]}