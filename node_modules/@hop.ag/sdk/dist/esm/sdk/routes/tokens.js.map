{"version": 3, "file": "tokens.js", "sourceRoot": "", "sources": ["../../../../src/sdk/routes/tokens.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,oBAAoB,EAAE,MAAM,iBAAiB,CAAC;AACvD,OAAO,EAAE,cAAc,EAAE,MAAM,YAAY,CAAC;AAe5C,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,MAAc;IAC9C,MAAM,QAAQ,GAAG,MAAM,cAAc,CAAC;QACpC,KAAK,EAAE,QAAQ;QACf,OAAO,EAAE;YACP,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,OAAO;YAC/B,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc;YAC7C,IAAI,EAAE,EAAE;YACR,MAAM,EAAE,MAAM;SACf;QACD,cAAc,EAAE,oBAAoB;KACrC,CAAC,CAAC;IAEH,IAAI,QAAQ,EAAE,MAAM,EAAE,CAAC;QACrB,OAAO;YACL,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;IACJ,CAAC;IAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;AAC1C,CAAC", "sourcesContent": ["import { HopApi } from \"../api.js\";\nimport { tokensResponseSchema } from \"../types/api.js\";\nimport { makeAPIRequest } from \"../util.js\";\n\nexport interface VerifiedToken {\n  coin_type: string;\n  name: string;\n  ticker: string;\n  icon_url: string;\n  decimals: number;\n  token_order?: number | null; // used for internal reasons\n}\n\nexport interface GetTokensResponse {\n  tokens: VerifiedToken[];\n}\n\nexport async function fetchTokens(client: HopApi): Promise<GetTokensResponse> {\n  const response = await makeAPIRequest({\n    route: \"tokens\",\n    options: {\n      api_key: client.options.api_key,\n      hop_server_url: client.options.hop_server_url,\n      data: {},\n      method: \"post\",\n    },\n    responseSchema: tokensResponseSchema,\n  });\n\n  if (response?.tokens) {\n    return {\n      tokens: response.tokens,\n    };\n  }\n\n  throw new Error(\"Unable to get tokens\");\n}\n"]}