import { SuiClient } from "@mysten/sui/client";
import { fetchQuote, } from "./routes/quote.js";
import { fetchTx } from "./routes/tx.js";
import { fetchTokens } from "./routes/tokens.js";
import { fetchPrice } from "./routes/price.js";
export class HopApi {
    client;
    options;
    use_v2;
    constructor(rpc_endpoint, options, use_v2 = true) {
        this.client = new SuiClient({ url: rpc_endpoint });
        this.options = options;
        this.use_v2 = use_v2;
        this.validate_api_key();
        this.validate_fee();
    }
    validate_api_key() {
        if (!this.options.api_key.startsWith("hopapi")) {
            console.error("Error > Invalid api key:", this.options.api_key, ". Please contact us at hop.ag to request a new key.");
        }
    }
    validate_fee() {
        let fee_bps = this.options.fee_bps;
        if (fee_bps < 0) {
            console.error("> fee_bps must be positive.");
        }
        else if (fee_bps > 500) {
            console.error("> fee_bps must be less than or equal to 5% (500 bps).");
        }
        this.options.fee_bps = Math.max(this.options.fee_bps, 0);
        this.options.fee_bps = Math.min(this.options.fee_bps, 500);
        this.options.fee_bps = Number(this.options.fee_bps.toFixed(0));
    }
    /*
     * Routes
     */
    async fetchQuote(quote) {
        return fetchQuote(this, quote);
    }
    async fetchTx(tx) {
        return fetchTx(this, tx);
    }
    async fetchTokens() {
        return fetchTokens(this);
    }
    async fetchPrice(price) {
        return fetchPrice(this, price);
    }
}
//# sourceMappingURL=api.js.map