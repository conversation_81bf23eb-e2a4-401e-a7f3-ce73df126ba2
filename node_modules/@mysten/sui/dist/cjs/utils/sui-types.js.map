{"version": 3, "sources": ["../../../src/utils/sui-types.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase58, splitGenericParameters } from '@mysten/bcs';\n\nconst TX_DIGEST_LENGTH = 32;\n\n/** Returns whether the tx digest is valid based on the serialization format */\nexport function isValidTransactionDigest(value: string): value is string {\n\ttry {\n\t\tconst buffer = fromBase58(value);\n\t\treturn buffer.length === TX_DIGEST_LENGTH;\n\t} catch (e) {\n\t\treturn false;\n\t}\n}\n\n// TODO - can we automatically sync this with rust length definition?\n// Source of truth is\n// https://github.com/MystenLabs/sui/blob/acb2b97ae21f47600e05b0d28127d88d0725561d/crates/sui-types/src/base_types.rs#L67\n// which uses the Move account address length\n// https://github.com/move-language/move/blob/67ec40dc50c66c34fd73512fcc412f3b68d67235/language/move-core/types/src/account_address.rs#L23 .\n\nexport const SUI_ADDRESS_LENGTH = 32;\nexport function isValidSuiAddress(value: string): value is string {\n\treturn isHex(value) && getHexByteLength(value) === SUI_ADDRESS_LENGTH;\n}\n\nexport function isValidSuiObjectId(value: string): boolean {\n\treturn isValidSuiAddress(value);\n}\n\ntype StructTag = {\n\taddress: string;\n\tmodule: string;\n\tname: string;\n\ttypeParams: (string | StructTag)[];\n};\n\nfunction parseTypeTag(type: string): string | StructTag {\n\tif (!type.includes('::')) return type;\n\n\treturn parseStructTag(type);\n}\n\nexport function parseStructTag(type: string): StructTag {\n\tconst [address, module] = type.split('::');\n\n\tconst rest = type.slice(address.length + module.length + 4);\n\tconst name = rest.includes('<') ? rest.slice(0, rest.indexOf('<')) : rest;\n\tconst typeParams = rest.includes('<')\n\t\t? splitGenericParameters(rest.slice(rest.indexOf('<') + 1, rest.lastIndexOf('>'))).map(\n\t\t\t\t(typeParam) => parseTypeTag(typeParam.trim()),\n\t\t\t)\n\t\t: [];\n\n\treturn {\n\t\taddress: normalizeSuiAddress(address),\n\t\tmodule,\n\t\tname,\n\t\ttypeParams,\n\t};\n}\n\nexport function normalizeStructTag(type: string | StructTag): string {\n\tconst { address, module, name, typeParams } =\n\t\ttypeof type === 'string' ? parseStructTag(type) : type;\n\n\tconst formattedTypeParams =\n\t\ttypeParams?.length > 0\n\t\t\t? `<${typeParams\n\t\t\t\t\t.map((typeParam) =>\n\t\t\t\t\t\ttypeof typeParam === 'string' ? typeParam : normalizeStructTag(typeParam),\n\t\t\t\t\t)\n\t\t\t\t\t.join(',')}>`\n\t\t\t: '';\n\n\treturn `${address}::${module}::${name}${formattedTypeParams}`;\n}\n\n/**\n * Perform the following operations:\n * 1. Make the address lower case\n * 2. Prepend `0x` if the string does not start with `0x`.\n * 3. Add more zeros if the length of the address(excluding `0x`) is less than `SUI_ADDRESS_LENGTH`\n *\n * WARNING: if the address value itself starts with `0x`, e.g., `0x0x`, the default behavior\n * is to treat the first `0x` not as part of the address. The default behavior can be overridden by\n * setting `forceAdd0x` to true\n *\n */\nexport function normalizeSuiAddress(value: string, forceAdd0x: boolean = false): string {\n\tlet address = value.toLowerCase();\n\tif (!forceAdd0x && address.startsWith('0x')) {\n\t\taddress = address.slice(2);\n\t}\n\treturn `0x${address.padStart(SUI_ADDRESS_LENGTH * 2, '0')}`;\n}\n\nexport function normalizeSuiObjectId(value: string, forceAdd0x: boolean = false): string {\n\treturn normalizeSuiAddress(value, forceAdd0x);\n}\n\nfunction isHex(value: string): boolean {\n\treturn /^(0x|0X)?[a-fA-F0-9]+$/.test(value) && value.length % 2 === 0;\n}\n\nfunction getHexByteLength(value: string): number {\n\treturn /^(0x|0X)/.test(value) ? (value.length - 2) / 2 : value.length / 2;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAmD;AAEnD,MAAM,mBAAmB;AAGlB,SAAS,yBAAyB,OAAgC;AACxE,MAAI;AACH,UAAM,aAAS,uBAAW,KAAK;AAC/B,WAAO,OAAO,WAAW;AAAA,EAC1B,SAAS,GAAG;AACX,WAAO;AAAA,EACR;AACD;AAQO,MAAM,qBAAqB;AAC3B,SAAS,kBAAkB,OAAgC;AACjE,SAAO,MAAM,KAAK,KAAK,iBAAiB,KAAK,MAAM;AACpD;AAEO,SAAS,mBAAmB,OAAwB;AAC1D,SAAO,kBAAkB,KAAK;AAC/B;AASA,SAAS,aAAa,MAAkC;AACvD,MAAI,CAAC,KAAK,SAAS,IAAI,EAAG,QAAO;AAEjC,SAAO,eAAe,IAAI;AAC3B;AAEO,SAAS,eAAe,MAAyB;AACvD,QAAM,CAAC,SAASA,OAAM,IAAI,KAAK,MAAM,IAAI;AAEzC,QAAM,OAAO,KAAK,MAAM,QAAQ,SAASA,QAAO,SAAS,CAAC;AAC1D,QAAM,OAAO,KAAK,SAAS,GAAG,IAAI,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC,IAAI;AACrE,QAAM,aAAa,KAAK,SAAS,GAAG,QACjC,mCAAuB,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,GAAG,KAAK,YAAY,GAAG,CAAC,CAAC,EAAE;AAAA,IACjF,CAAC,cAAc,aAAa,UAAU,KAAK,CAAC;AAAA,EAC7C,IACC,CAAC;AAEJ,SAAO;AAAA,IACN,SAAS,oBAAoB,OAAO;AAAA,IACpC,QAAAA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;AAEO,SAAS,mBAAmB,MAAkC;AACpE,QAAM,EAAE,SAAS,QAAAA,SAAQ,MAAM,WAAW,IACzC,OAAO,SAAS,WAAW,eAAe,IAAI,IAAI;AAEnD,QAAM,sBACL,YAAY,SAAS,IAClB,IAAI,WACH;AAAA,IAAI,CAAC,cACL,OAAO,cAAc,WAAW,YAAY,mBAAmB,SAAS;AAAA,EACzE,EACC,KAAK,GAAG,CAAC,MACV;AAEJ,SAAO,GAAG,OAAO,KAAKA,OAAM,KAAK,IAAI,GAAG,mBAAmB;AAC5D;AAaO,SAAS,oBAAoB,OAAe,aAAsB,OAAe;AACvF,MAAI,UAAU,MAAM,YAAY;AAChC,MAAI,CAAC,cAAc,QAAQ,WAAW,IAAI,GAAG;AAC5C,cAAU,QAAQ,MAAM,CAAC;AAAA,EAC1B;AACA,SAAO,KAAK,QAAQ,SAAS,qBAAqB,GAAG,GAAG,CAAC;AAC1D;AAEO,SAAS,qBAAqB,OAAe,aAAsB,OAAe;AACxF,SAAO,oBAAoB,OAAO,UAAU;AAC7C;AAEA,SAAS,MAAM,OAAwB;AACtC,SAAO,yBAAyB,KAAK,KAAK,KAAK,MAAM,SAAS,MAAM;AACrE;AAEA,SAAS,iBAAiB,OAAuB;AAChD,SAAO,WAAW,KAAK,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,SAAS;AACzE;", "names": ["module"]}