{"version": 3, "sources": ["../../../src/faucet/faucet.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport class FaucetRateLimitError extends Error {}\n\ntype FaucetCoinInfo = {\n\tamount: number;\n\tid: string;\n\ttransferTxDigest: string;\n};\n\ntype FaucetResponse = {\n\ttransferredGasObjects: FaucetCoinInfo[];\n\terror?: string | null;\n};\n\ntype BatchFaucetResponse = {\n\ttask?: string | null;\n\terror?: string | null;\n};\n\ntype BatchSendStatusType = {\n\tstatus: 'INPROGRESS' | 'SUCCEEDED' | 'DISCARDED';\n\ttransferred_gas_objects: { sent: FaucetCoinInfo[] };\n};\n\ntype BatchStatusFaucetResponse = {\n\tstatus: BatchSendStatusType;\n\terror?: string | null;\n};\n\ntype FaucetRequest = {\n\thost: string;\n\tpath: string;\n\tbody?: Record<string, any>;\n\theaders?: HeadersInit;\n\tmethod: 'GET' | 'POST';\n};\n\nasync function faucetRequest({ host, path, body, headers, method }: FaucetRequest) {\n\tconst endpoint = new URL(path, host).toString();\n\tconst res = await fetch(endpoint, {\n\t\tmethod,\n\t\tbody: body ? JSON.stringify(body) : undefined,\n\t\theaders: {\n\t\t\t'Content-Type': 'application/json',\n\t\t\t...(headers || {}),\n\t\t},\n\t});\n\n\tif (res.status === 429) {\n\t\tthrow new FaucetRateLimitError(\n\t\t\t`Too many requests from this client have been sent to the faucet. Please retry later`,\n\t\t);\n\t}\n\n\ttry {\n\t\tconst parsed = await res.json();\n\t\tif (parsed.error) {\n\t\t\tthrow new Error(`Faucet returns error: ${parsed.error}`);\n\t\t}\n\t\treturn parsed;\n\t} catch (e) {\n\t\tthrow new Error(\n\t\t\t`Encountered error when parsing response from faucet, error: ${e}, status ${res.status}, response ${res}`,\n\t\t);\n\t}\n}\n\nexport async function requestSuiFromFaucetV0(input: {\n\thost: string;\n\trecipient: string;\n\theaders?: HeadersInit;\n}): Promise<FaucetResponse> {\n\treturn faucetRequest({\n\t\thost: input.host,\n\t\tpath: '/gas',\n\t\tbody: {\n\t\t\tFixedAmountRequest: {\n\t\t\t\trecipient: input.recipient,\n\t\t\t},\n\t\t},\n\t\theaders: input.headers,\n\t\tmethod: 'POST',\n\t});\n}\n\nexport async function requestSuiFromFaucetV1(input: {\n\thost: string;\n\trecipient: string;\n\theaders?: HeadersInit;\n}): Promise<BatchFaucetResponse> {\n\treturn faucetRequest({\n\t\thost: input.host,\n\t\tpath: '/v1/gas',\n\t\tbody: {\n\t\t\tFixedAmountRequest: {\n\t\t\t\trecipient: input.recipient,\n\t\t\t},\n\t\t},\n\t\theaders: input.headers,\n\t\tmethod: 'POST',\n\t});\n}\n\nexport async function getFaucetRequestStatus(input: {\n\thost: string;\n\ttaskId: string;\n\theaders?: HeadersInit;\n}): Promise<BatchStatusFaucetResponse> {\n\treturn faucetRequest({\n\t\thost: input.host,\n\t\tpath: `/v1/status/${input.taskId}`,\n\t\theaders: input.headers,\n\t\tmethod: 'GET',\n\t});\n}\n\nexport function getFaucetHost(network: 'testnet' | 'devnet' | 'localnet') {\n\tswitch (network) {\n\t\tcase 'testnet':\n\t\t\treturn 'https://faucet.testnet.sui.io';\n\t\tcase 'devnet':\n\t\t\treturn 'https://faucet.devnet.sui.io';\n\t\tcase 'localnet':\n\t\t\treturn 'http://127.0.0.1:9123';\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown network: ${network}`);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,MAAM,6BAA6B,MAAM;AAAC;AAoCjD,eAAe,cAAc,EAAE,MAAM,MAAM,MAAM,SAAS,OAAO,GAAkB;AAClF,QAAM,WAAW,IAAI,IAAI,MAAM,IAAI,EAAE,SAAS;AAC9C,QAAM,MAAM,MAAM,MAAM,UAAU;AAAA,IACjC;AAAA,IACA,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;AAAA,IACpC,SAAS;AAAA,MACR,gBAAgB;AAAA,MAChB,GAAI,WAAW,CAAC;AAAA,IACjB;AAAA,EACD,CAAC;AAED,MAAI,IAAI,WAAW,KAAK;AACvB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,MAAI;AACH,UAAM,SAAS,MAAM,IAAI,KAAK;AAC9B,QAAI,OAAO,OAAO;AACjB,YAAM,IAAI,MAAM,yBAAyB,OAAO,KAAK,EAAE;AAAA,IACxD;AACA,WAAO;AAAA,EACR,SAAS,GAAG;AACX,UAAM,IAAI;AAAA,MACT,+DAA+D,CAAC,YAAY,IAAI,MAAM,cAAc,GAAG;AAAA,IACxG;AAAA,EACD;AACD;AAEA,eAAsB,uBAAuB,OAIjB;AAC3B,SAAO,cAAc;AAAA,IACpB,MAAM,MAAM;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,MACL,oBAAoB;AAAA,QACnB,WAAW,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AACF;AAEA,eAAsB,uBAAuB,OAIZ;AAChC,SAAO,cAAc;AAAA,IACpB,MAAM,MAAM;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,MACL,oBAAoB;AAAA,QACnB,WAAW,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AACF;AAEA,eAAsB,uBAAuB,OAIN;AACtC,SAAO,cAAc;AAAA,IACpB,MAAM,MAAM;AAAA,IACZ,MAAM,cAAc,MAAM,MAAM;AAAA,IAChC,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AACF;AAEO,SAAS,cAAc,SAA4C;AACzE,UAAQ,SAAS;AAAA,IAChB,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR;AACC,YAAM,IAAI,MAAM,oBAAoB,OAAO,EAAE;AAAA,EAC/C;AACD;", "names": []}