{"version": 3, "sources": ["../../../../../src/graphql/schemas/latest/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { initGraphQLTada } from 'gql.tada';\n\nimport type { introspection } from '../../generated/latest/tada-env.js';\nimport type { CustomScalars } from '../../types.js';\n\nexport * from '../../types.js';\n\nexport type { FragmentOf, ResultOf, VariablesOf, TadaDocumentNode } from 'gql.tada';\nexport { readFragment, maskFragments } from 'gql.tada';\n\nexport const graphql = initGraphQLTada<{\n\tintrospection: introspection;\n\tscalars: CustomScalars;\n}>();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAgC;AAKhC,2BAAc,2BARd;AAWA,IAAAA,cAA4C;AAErC,MAAM,cAAU,4BAGpB;", "names": ["import_gql"]}