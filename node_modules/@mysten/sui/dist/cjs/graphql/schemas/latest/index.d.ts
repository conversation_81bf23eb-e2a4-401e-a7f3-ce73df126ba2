export * from '../../types.js';
export type { FragmentOf, ResultOf, VariablesOf, TadaDocumentNode } from 'gql.tada';
export { readFragment, maskFragments } from 'gql.tada';
export declare const graphql: import("gql.tada").GraphQLTadaAPI<{
    name: never;
    query: "Query";
    mutation: "Mutation";
    subscription: never;
    types: {
        Base64: {
            name: "Base64";
            type: string;
        };
        SuiAddress: {
            name: "SuiAddress";
            type: string;
        };
        ID: {
            name: "ID";
            type: string;
        };
        Boolean: {
            name: "Boolean";
            type: boolean;
        };
        String: {
            name: "String";
            type: string;
        };
        Float: {
            name: "Float";
            type: number;
        };
        Int: {
            name: "Int";
            type: number;
        };
        BigInt: {
            name: "BigInt";
            type: string;
        };
        DateTime: {
            name: "DateTime";
            type: string;
        };
        JSON: {
            name: "<PERSON>SO<PERSON>";
            type: unknown;
        };
        MoveData: {
            name: "MoveData";
            type: import("../../types.js").MoveData;
        };
        MoveTypeLayout: {
            name: "MoveTypeLayout";
            type: import("../../types.js").MoveTypeLayout;
        };
        MoveTypeSignature: {
            name: "MoveTypeSignature";
            type: import("../../types.js").MoveTypeSignature;
        };
        OpenMoveTypeSignature: {
            name: "OpenMoveTypeSignature";
            type: import("../../types.js").OpenMoveTypeSignature;
        };
    } & {
        ActiveJwk: {
            kind: "OBJECT";
            name: "ActiveJwk";
            fields: {
                "alg": {
                    name: "alg";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "e": {
                    name: "e";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "iss": {
                    name: "iss";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "kid": {
                    name: "kid";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "kty": {
                    name: "kty";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "n": {
                    name: "n";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ActiveJwkConnection: {
            kind: "OBJECT";
            name: "ActiveJwkConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ActiveJwkEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ActiveJwk";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ActiveJwkEdge: {
            kind: "OBJECT";
            name: "ActiveJwkEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ActiveJwk";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Address: {
            kind: "OBJECT";
            name: "Address";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "transactionBlocks": {
                    name: "transactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        AddressConnection: {
            kind: "OBJECT";
            name: "AddressConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "AddressEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Address";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        AddressEdge: {
            kind: "OBJECT";
            name: "AddressEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Address";
                            ofType: null;
                        };
                    };
                };
            };
        };
        AddressOwner: {
            kind: "OBJECT";
            name: "AddressOwner";
            fields: {
                "owner": {
                    name: "owner";
                    type: {
                        kind: "OBJECT";
                        name: "Owner";
                        ofType: null;
                    };
                };
            };
        };
        AddressTransactionBlockRelationship: {
            name: "AddressTransactionBlockRelationship";
            enumValues: "SENT" | "AFFECTED";
        };
        Authenticator: {
            kind: "UNION";
            name: "Authenticator";
            fields: {};
            possibleTypes: "Address";
        };
        AuthenticatorStateCreateTransaction: {
            kind: "OBJECT";
            name: "AuthenticatorStateCreateTransaction";
            fields: {
                "_": {
                    name: "_";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
            };
        };
        AuthenticatorStateExpireTransaction: {
            kind: "OBJECT";
            name: "AuthenticatorStateExpireTransaction";
            fields: {
                "authenticatorObjInitialSharedVersion": {
                    name: "authenticatorObjInitialSharedVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "minEpoch": {
                    name: "minEpoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
            };
        };
        AuthenticatorStateUpdateTransaction: {
            kind: "OBJECT";
            name: "AuthenticatorStateUpdateTransaction";
            fields: {
                "authenticatorObjInitialSharedVersion": {
                    name: "authenticatorObjInitialSharedVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "newActiveJwks": {
                    name: "newActiveJwks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ActiveJwkConnection";
                            ofType: null;
                        };
                    };
                };
                "round": {
                    name: "round";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        AvailableRange: {
            kind: "OBJECT";
            name: "AvailableRange";
            fields: {
                "first": {
                    name: "first";
                    type: {
                        kind: "OBJECT";
                        name: "Checkpoint";
                        ofType: null;
                    };
                };
                "last": {
                    name: "last";
                    type: {
                        kind: "OBJECT";
                        name: "Checkpoint";
                        ofType: null;
                    };
                };
            };
        };
        Balance: {
            kind: "OBJECT";
            name: "Balance";
            fields: {
                "coinObjectCount": {
                    name: "coinObjectCount";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "coinType": {
                    name: "coinType";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveType";
                            ofType: null;
                        };
                    };
                };
                "totalBalance": {
                    name: "totalBalance";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
            };
        };
        BalanceChange: {
            kind: "OBJECT";
            name: "BalanceChange";
            fields: {
                "amount": {
                    name: "amount";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "coinType": {
                    name: "coinType";
                    type: {
                        kind: "OBJECT";
                        name: "MoveType";
                        ofType: null;
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "OBJECT";
                        name: "Owner";
                        ofType: null;
                    };
                };
            };
        };
        BalanceChangeConnection: {
            kind: "OBJECT";
            name: "BalanceChangeConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "BalanceChangeEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "BalanceChange";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        BalanceChangeEdge: {
            kind: "OBJECT";
            name: "BalanceChangeEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceChange";
                            ofType: null;
                        };
                    };
                };
            };
        };
        BalanceConnection: {
            kind: "OBJECT";
            name: "BalanceConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "BalanceEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Balance";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        BalanceEdge: {
            kind: "OBJECT";
            name: "BalanceEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Balance";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Base64: unknown;
        BigInt: unknown;
        Boolean: unknown;
        BridgeCommitteeInitTransaction: {
            kind: "OBJECT";
            name: "BridgeCommitteeInitTransaction";
            fields: {
                "bridgeObjInitialSharedVersion": {
                    name: "bridgeObjInitialSharedVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        BridgeStateCreateTransaction: {
            kind: "OBJECT";
            name: "BridgeStateCreateTransaction";
            fields: {
                "chainId": {
                    name: "chainId";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ChangeEpochTransaction: {
            kind: "OBJECT";
            name: "ChangeEpochTransaction";
            fields: {
                "computationCharge": {
                    name: "computationCharge";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "BigInt";
                            ofType: null;
                        };
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "nonRefundableStorageFee": {
                    name: "nonRefundableStorageFee";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "BigInt";
                            ofType: null;
                        };
                    };
                };
                "protocolVersion": {
                    name: "protocolVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "startTimestamp": {
                    name: "startTimestamp";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "DateTime";
                            ofType: null;
                        };
                    };
                };
                "storageCharge": {
                    name: "storageCharge";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "BigInt";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "BigInt";
                            ofType: null;
                        };
                    };
                };
                "systemPackages": {
                    name: "systemPackages";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackageConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Checkpoint: {
            kind: "OBJECT";
            name: "Checkpoint";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "networkTotalTransactions": {
                    name: "networkTotalTransactions";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "previousCheckpointDigest": {
                    name: "previousCheckpointDigest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "rollingGasSummary": {
                    name: "rollingGasSummary";
                    type: {
                        kind: "OBJECT";
                        name: "GasCostSummary";
                        ofType: null;
                    };
                };
                "sequenceNumber": {
                    name: "sequenceNumber";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "timestamp": {
                    name: "timestamp";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "DateTime";
                            ofType: null;
                        };
                    };
                };
                "transactionBlocks": {
                    name: "transactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "validatorSignatures": {
                    name: "validatorSignatures";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
            };
        };
        CheckpointConnection: {
            kind: "OBJECT";
            name: "CheckpointConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "CheckpointEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Checkpoint";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        CheckpointEdge: {
            kind: "OBJECT";
            name: "CheckpointEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Checkpoint";
                            ofType: null;
                        };
                    };
                };
            };
        };
        CheckpointId: {
            kind: "INPUT_OBJECT";
            name: "CheckpointId";
            isOneOf: false;
            inputFields: [{
                name: "digest";
                type: {
                    kind: "SCALAR";
                    name: "String";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "sequenceNumber";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }];
        };
        Coin: {
            kind: "OBJECT";
            name: "Coin";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coinBalance": {
                    name: "coinBalance";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "contents": {
                    name: "contents";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "hasPublicTransfer": {
                    name: "hasPublicTransfer";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        CoinConnection: {
            kind: "OBJECT";
            name: "CoinConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "CoinEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Coin";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        CoinDenyListStateCreateTransaction: {
            kind: "OBJECT";
            name: "CoinDenyListStateCreateTransaction";
            fields: {
                "_": {
                    name: "_";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
            };
        };
        CoinEdge: {
            kind: "OBJECT";
            name: "CoinEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Coin";
                            ofType: null;
                        };
                    };
                };
            };
        };
        CoinMetadata: {
            kind: "OBJECT";
            name: "CoinMetadata";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "contents": {
                    name: "contents";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "decimals": {
                    name: "decimals";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "description": {
                    name: "description";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "hasPublicTransfer": {
                    name: "hasPublicTransfer";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "iconUrl": {
                    name: "iconUrl";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "supply": {
                    name: "supply";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "symbol": {
                    name: "symbol";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ConsensusCommitPrologueTransaction: {
            kind: "OBJECT";
            name: "ConsensusCommitPrologueTransaction";
            fields: {
                "commitTimestamp": {
                    name: "commitTimestamp";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "DateTime";
                            ofType: null;
                        };
                    };
                };
                "consensusCommitDigest": {
                    name: "consensusCommitDigest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "round": {
                    name: "round";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ConsensusV2: {
            kind: "OBJECT";
            name: "ConsensusV2";
            fields: {
                "authenticator": {
                    name: "authenticator";
                    type: {
                        kind: "UNION";
                        name: "Authenticator";
                        ofType: null;
                    };
                };
                "startVersion": {
                    name: "startVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        DateTime: unknown;
        DependencyConnection: {
            kind: "OBJECT";
            name: "DependencyConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "DependencyEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "TransactionBlock";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        DependencyEdge: {
            kind: "OBJECT";
            name: "DependencyEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
            };
        };
        DisplayEntry: {
            kind: "OBJECT";
            name: "DisplayEntry";
            fields: {
                "error": {
                    name: "error";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "key": {
                    name: "key";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "value": {
                    name: "value";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
            };
        };
        DomainFormat: {
            name: "DomainFormat";
            enumValues: "AT" | "DOT";
        };
        DryRunEffect: {
            kind: "OBJECT";
            name: "DryRunEffect";
            fields: {
                "mutatedReferences": {
                    name: "mutatedReferences";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DryRunMutation";
                                ofType: null;
                            };
                        };
                    };
                };
                "returnValues": {
                    name: "returnValues";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DryRunReturn";
                                ofType: null;
                            };
                        };
                    };
                };
            };
        };
        DryRunMutation: {
            kind: "OBJECT";
            name: "DryRunMutation";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
                "input": {
                    name: "input";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "TransactionArgument";
                            ofType: null;
                        };
                    };
                };
                "type": {
                    name: "type";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveType";
                            ofType: null;
                        };
                    };
                };
            };
        };
        DryRunResult: {
            kind: "OBJECT";
            name: "DryRunResult";
            fields: {
                "error": {
                    name: "error";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "results": {
                    name: "results";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DryRunEffect";
                                ofType: null;
                            };
                        };
                    };
                };
                "transaction": {
                    name: "transaction";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
            };
        };
        DryRunReturn: {
            kind: "OBJECT";
            name: "DryRunReturn";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
                "type": {
                    name: "type";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveType";
                            ofType: null;
                        };
                    };
                };
            };
        };
        DynamicField: {
            kind: "OBJECT";
            name: "DynamicField";
            fields: {
                "name": {
                    name: "name";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "value": {
                    name: "value";
                    type: {
                        kind: "UNION";
                        name: "DynamicFieldValue";
                        ofType: null;
                    };
                };
            };
        };
        DynamicFieldConnection: {
            kind: "OBJECT";
            name: "DynamicFieldConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "DynamicFieldEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "DynamicField";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        DynamicFieldEdge: {
            kind: "OBJECT";
            name: "DynamicFieldEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicField";
                            ofType: null;
                        };
                    };
                };
            };
        };
        DynamicFieldName: {
            kind: "INPUT_OBJECT";
            name: "DynamicFieldName";
            isOneOf: false;
            inputFields: [{
                name: "type";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }, {
                name: "bcs";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }];
        };
        DynamicFieldValue: {
            kind: "UNION";
            name: "DynamicFieldValue";
            fields: {};
            possibleTypes: "MoveObject" | "MoveValue";
        };
        EndOfEpochTransaction: {
            kind: "OBJECT";
            name: "EndOfEpochTransaction";
            fields: {
                "transactions": {
                    name: "transactions";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "EndOfEpochTransactionKindConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        EndOfEpochTransactionKind: {
            kind: "UNION";
            name: "EndOfEpochTransactionKind";
            fields: {};
            possibleTypes: "AuthenticatorStateCreateTransaction" | "AuthenticatorStateExpireTransaction" | "BridgeCommitteeInitTransaction" | "BridgeStateCreateTransaction" | "ChangeEpochTransaction" | "CoinDenyListStateCreateTransaction" | "RandomnessStateCreateTransaction";
        };
        EndOfEpochTransactionKindConnection: {
            kind: "OBJECT";
            name: "EndOfEpochTransactionKindConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "EndOfEpochTransactionKindEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "EndOfEpochTransactionKind";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        EndOfEpochTransactionKindEdge: {
            kind: "OBJECT";
            name: "EndOfEpochTransactionKindEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "EndOfEpochTransactionKind";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Epoch: {
            kind: "OBJECT";
            name: "Epoch";
            fields: {
                "checkpoints": {
                    name: "checkpoints";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CheckpointConnection";
                            ofType: null;
                        };
                    };
                };
                "endTimestamp": {
                    name: "endTimestamp";
                    type: {
                        kind: "SCALAR";
                        name: "DateTime";
                        ofType: null;
                    };
                };
                "epochId": {
                    name: "epochId";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "fundInflow": {
                    name: "fundInflow";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "fundOutflow": {
                    name: "fundOutflow";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "fundSize": {
                    name: "fundSize";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "liveObjectSetDigest": {
                    name: "liveObjectSetDigest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "netInflow": {
                    name: "netInflow";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "protocolConfigs": {
                    name: "protocolConfigs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ProtocolConfigs";
                            ofType: null;
                        };
                    };
                };
                "referenceGasPrice": {
                    name: "referenceGasPrice";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "safeMode": {
                    name: "safeMode";
                    type: {
                        kind: "OBJECT";
                        name: "SafeMode";
                        ofType: null;
                    };
                };
                "startTimestamp": {
                    name: "startTimestamp";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "DateTime";
                            ofType: null;
                        };
                    };
                };
                "storageFund": {
                    name: "storageFund";
                    type: {
                        kind: "OBJECT";
                        name: "StorageFund";
                        ofType: null;
                    };
                };
                "systemParameters": {
                    name: "systemParameters";
                    type: {
                        kind: "OBJECT";
                        name: "SystemParameters";
                        ofType: null;
                    };
                };
                "systemStakeSubsidy": {
                    name: "systemStakeSubsidy";
                    type: {
                        kind: "OBJECT";
                        name: "StakeSubsidy";
                        ofType: null;
                    };
                };
                "systemStateVersion": {
                    name: "systemStateVersion";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "totalCheckpoints": {
                    name: "totalCheckpoints";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "totalGasFees": {
                    name: "totalGasFees";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "totalStakeRewards": {
                    name: "totalStakeRewards";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "totalStakeSubsidies": {
                    name: "totalStakeSubsidies";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "totalTransactions": {
                    name: "totalTransactions";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "transactionBlocks": {
                    name: "transactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "validatorSet": {
                    name: "validatorSet";
                    type: {
                        kind: "OBJECT";
                        name: "ValidatorSet";
                        ofType: null;
                    };
                };
            };
        };
        EpochConnection: {
            kind: "OBJECT";
            name: "EpochConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "EpochEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Epoch";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        EpochEdge: {
            kind: "OBJECT";
            name: "EpochEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Epoch";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Event: {
            kind: "OBJECT";
            name: "Event";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
                "contents": {
                    name: "contents";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveValue";
                            ofType: null;
                        };
                    };
                };
                "sender": {
                    name: "sender";
                    type: {
                        kind: "OBJECT";
                        name: "Address";
                        ofType: null;
                    };
                };
                "sendingModule": {
                    name: "sendingModule";
                    type: {
                        kind: "OBJECT";
                        name: "MoveModule";
                        ofType: null;
                    };
                };
                "timestamp": {
                    name: "timestamp";
                    type: {
                        kind: "SCALAR";
                        name: "DateTime";
                        ofType: null;
                    };
                };
                "transactionBlock": {
                    name: "transactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
            };
        };
        EventConnection: {
            kind: "OBJECT";
            name: "EventConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "EventEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Event";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        EventEdge: {
            kind: "OBJECT";
            name: "EventEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Event";
                            ofType: null;
                        };
                    };
                };
            };
        };
        EventFilter: {
            kind: "INPUT_OBJECT";
            name: "EventFilter";
            isOneOf: false;
            inputFields: [{
                name: "sender";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "transactionDigest";
                type: {
                    kind: "SCALAR";
                    name: "String";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "emittingModule";
                type: {
                    kind: "SCALAR";
                    name: "String";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "eventType";
                type: {
                    kind: "SCALAR";
                    name: "String";
                    ofType: null;
                };
                defaultValue: null;
            }];
        };
        ExecutionResult: {
            kind: "OBJECT";
            name: "ExecutionResult";
            fields: {
                "effects": {
                    name: "effects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockEffects";
                            ofType: null;
                        };
                    };
                };
                "errors": {
                    name: "errors";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "SCALAR";
                                name: "String";
                                ofType: null;
                            };
                        };
                    };
                };
            };
        };
        ExecutionStatus: {
            name: "ExecutionStatus";
            enumValues: "SUCCESS" | "FAILURE";
        };
        Feature: {
            name: "Feature";
            enumValues: "ANALYTICS" | "COINS" | "DYNAMIC_FIELDS" | "NAME_SERVICE" | "SUBSCRIPTIONS" | "SYSTEM_STATE" | "MOVE_REGISTRY";
        };
        GasCoin: {
            kind: "OBJECT";
            name: "GasCoin";
            fields: {
                "_": {
                    name: "_";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
            };
        };
        GasCostSummary: {
            kind: "OBJECT";
            name: "GasCostSummary";
            fields: {
                "computationCost": {
                    name: "computationCost";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "nonRefundableStorageFee": {
                    name: "nonRefundableStorageFee";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "storageCost": {
                    name: "storageCost";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
            };
        };
        GasEffects: {
            kind: "OBJECT";
            name: "GasEffects";
            fields: {
                "gasObject": {
                    name: "gasObject";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "gasSummary": {
                    name: "gasSummary";
                    type: {
                        kind: "OBJECT";
                        name: "GasCostSummary";
                        ofType: null;
                    };
                };
            };
        };
        GasInput: {
            kind: "OBJECT";
            name: "GasInput";
            fields: {
                "gasBudget": {
                    name: "gasBudget";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "gasPayment": {
                    name: "gasPayment";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "gasPrice": {
                    name: "gasPrice";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "gasSponsor": {
                    name: "gasSponsor";
                    type: {
                        kind: "OBJECT";
                        name: "Address";
                        ofType: null;
                    };
                };
            };
        };
        GenesisTransaction: {
            kind: "OBJECT";
            name: "GenesisTransaction";
            fields: {
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ObjectConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        IMoveDatatype: {
            kind: "INTERFACE";
            name: "IMoveDatatype";
            fields: {
                "abilities": {
                    name: "abilities";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "ENUM";
                                name: "MoveAbility";
                                ofType: null;
                            };
                        };
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModule";
                            ofType: null;
                        };
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "typeParameters": {
                    name: "typeParameters";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveStructTypeParameter";
                                ofType: null;
                            };
                        };
                    };
                };
            };
            possibleTypes: "MoveDatatype" | "MoveEnum" | "MoveStruct";
        };
        IMoveObject: {
            kind: "INTERFACE";
            name: "IMoveObject";
            fields: {
                "contents": {
                    name: "contents";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "hasPublicTransfer": {
                    name: "hasPublicTransfer";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
            };
            possibleTypes: "Coin" | "CoinMetadata" | "MoveObject" | "StakedSui" | "SuinsRegistration";
        };
        IObject: {
            kind: "INTERFACE";
            name: "IObject";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
            possibleTypes: "Coin" | "CoinMetadata" | "MoveObject" | "MovePackage" | "Object" | "StakedSui" | "SuinsRegistration";
        };
        IOwner: {
            kind: "INTERFACE";
            name: "IOwner";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
            };
            possibleTypes: "Address" | "Coin" | "CoinMetadata" | "MoveObject" | "MovePackage" | "Object" | "Owner" | "StakedSui" | "SuinsRegistration";
        };
        Immutable: {
            kind: "OBJECT";
            name: "Immutable";
            fields: {
                "_": {
                    name: "_";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
            };
        };
        Input: {
            kind: "OBJECT";
            name: "Input";
            fields: {
                "ix": {
                    name: "ix";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Int: unknown;
        JSON: unknown;
        Linkage: {
            kind: "OBJECT";
            name: "Linkage";
            fields: {
                "originalId": {
                    name: "originalId";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "upgradedId": {
                    name: "upgradedId";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MakeMoveVecTransaction: {
            kind: "OBJECT";
            name: "MakeMoveVecTransaction";
            fields: {
                "elements": {
                    name: "elements";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "TransactionArgument";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "type": {
                    name: "type";
                    type: {
                        kind: "OBJECT";
                        name: "MoveType";
                        ofType: null;
                    };
                };
            };
        };
        MergeCoinsTransaction: {
            kind: "OBJECT";
            name: "MergeCoinsTransaction";
            fields: {
                "coin": {
                    name: "coin";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "TransactionArgument";
                            ofType: null;
                        };
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "TransactionArgument";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
            };
        };
        MoveAbility: {
            name: "MoveAbility";
            enumValues: "COPY" | "DROP" | "KEY" | "STORE";
        };
        MoveCallTransaction: {
            kind: "OBJECT";
            name: "MoveCallTransaction";
            fields: {
                "arguments": {
                    name: "arguments";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "TransactionArgument";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "function": {
                    name: "function";
                    type: {
                        kind: "OBJECT";
                        name: "MoveFunction";
                        ofType: null;
                    };
                };
                "functionName": {
                    name: "functionName";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "package": {
                    name: "package";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "typeArguments": {
                    name: "typeArguments";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveType";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
            };
        };
        MoveData: unknown;
        MoveDatatype: {
            kind: "OBJECT";
            name: "MoveDatatype";
            fields: {
                "abilities": {
                    name: "abilities";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "ENUM";
                                name: "MoveAbility";
                                ofType: null;
                            };
                        };
                    };
                };
                "asMoveEnum": {
                    name: "asMoveEnum";
                    type: {
                        kind: "OBJECT";
                        name: "MoveEnum";
                        ofType: null;
                    };
                };
                "asMoveStruct": {
                    name: "asMoveStruct";
                    type: {
                        kind: "OBJECT";
                        name: "MoveStruct";
                        ofType: null;
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModule";
                            ofType: null;
                        };
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "typeParameters": {
                    name: "typeParameters";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveStructTypeParameter";
                                ofType: null;
                            };
                        };
                    };
                };
            };
        };
        MoveDatatypeConnection: {
            kind: "OBJECT";
            name: "MoveDatatypeConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveDatatypeEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveDatatype";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveDatatypeEdge: {
            kind: "OBJECT";
            name: "MoveDatatypeEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveDatatype";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveEnum: {
            kind: "OBJECT";
            name: "MoveEnum";
            fields: {
                "abilities": {
                    name: "abilities";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "ENUM";
                                name: "MoveAbility";
                                ofType: null;
                            };
                        };
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModule";
                            ofType: null;
                        };
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "typeParameters": {
                    name: "typeParameters";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveStructTypeParameter";
                                ofType: null;
                            };
                        };
                    };
                };
                "variants": {
                    name: "variants";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveEnumVariant";
                                ofType: null;
                            };
                        };
                    };
                };
            };
        };
        MoveEnumConnection: {
            kind: "OBJECT";
            name: "MoveEnumConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveEnumEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveEnum";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveEnumEdge: {
            kind: "OBJECT";
            name: "MoveEnumEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveEnum";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveEnumVariant: {
            kind: "OBJECT";
            name: "MoveEnumVariant";
            fields: {
                "fields": {
                    name: "fields";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveField";
                                ofType: null;
                            };
                        };
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveField: {
            kind: "OBJECT";
            name: "MoveField";
            fields: {
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "type": {
                    name: "type";
                    type: {
                        kind: "OBJECT";
                        name: "OpenMoveType";
                        ofType: null;
                    };
                };
            };
        };
        MoveFunction: {
            kind: "OBJECT";
            name: "MoveFunction";
            fields: {
                "isEntry": {
                    name: "isEntry";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModule";
                            ofType: null;
                        };
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "parameters": {
                    name: "parameters";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "OpenMoveType";
                                ofType: null;
                            };
                        };
                    };
                };
                "return": {
                    name: "return";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "OpenMoveType";
                                ofType: null;
                            };
                        };
                    };
                };
                "typeParameters": {
                    name: "typeParameters";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveFunctionTypeParameter";
                                ofType: null;
                            };
                        };
                    };
                };
                "visibility": {
                    name: "visibility";
                    type: {
                        kind: "ENUM";
                        name: "MoveVisibility";
                        ofType: null;
                    };
                };
            };
        };
        MoveFunctionConnection: {
            kind: "OBJECT";
            name: "MoveFunctionConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveFunctionEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveFunction";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveFunctionEdge: {
            kind: "OBJECT";
            name: "MoveFunctionEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveFunction";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveFunctionTypeParameter: {
            kind: "OBJECT";
            name: "MoveFunctionTypeParameter";
            fields: {
                "constraints": {
                    name: "constraints";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "ENUM";
                                    name: "MoveAbility";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
            };
        };
        MoveModule: {
            kind: "OBJECT";
            name: "MoveModule";
            fields: {
                "bytes": {
                    name: "bytes";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "datatype": {
                    name: "datatype";
                    type: {
                        kind: "OBJECT";
                        name: "MoveDatatype";
                        ofType: null;
                    };
                };
                "datatypes": {
                    name: "datatypes";
                    type: {
                        kind: "OBJECT";
                        name: "MoveDatatypeConnection";
                        ofType: null;
                    };
                };
                "disassembly": {
                    name: "disassembly";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "enum": {
                    name: "enum";
                    type: {
                        kind: "OBJECT";
                        name: "MoveEnum";
                        ofType: null;
                    };
                };
                "enums": {
                    name: "enums";
                    type: {
                        kind: "OBJECT";
                        name: "MoveEnumConnection";
                        ofType: null;
                    };
                };
                "fileFormatVersion": {
                    name: "fileFormatVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "friends": {
                    name: "friends";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModuleConnection";
                            ofType: null;
                        };
                    };
                };
                "function": {
                    name: "function";
                    type: {
                        kind: "OBJECT";
                        name: "MoveFunction";
                        ofType: null;
                    };
                };
                "functions": {
                    name: "functions";
                    type: {
                        kind: "OBJECT";
                        name: "MoveFunctionConnection";
                        ofType: null;
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "package": {
                    name: "package";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackage";
                            ofType: null;
                        };
                    };
                };
                "struct": {
                    name: "struct";
                    type: {
                        kind: "OBJECT";
                        name: "MoveStruct";
                        ofType: null;
                    };
                };
                "structs": {
                    name: "structs";
                    type: {
                        kind: "OBJECT";
                        name: "MoveStructConnection";
                        ofType: null;
                    };
                };
            };
        };
        MoveModuleConnection: {
            kind: "OBJECT";
            name: "MoveModuleConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveModuleEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveModule";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveModuleEdge: {
            kind: "OBJECT";
            name: "MoveModuleEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModule";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveObject: {
            kind: "OBJECT";
            name: "MoveObject";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "asCoin": {
                    name: "asCoin";
                    type: {
                        kind: "OBJECT";
                        name: "Coin";
                        ofType: null;
                    };
                };
                "asCoinMetadata": {
                    name: "asCoinMetadata";
                    type: {
                        kind: "OBJECT";
                        name: "CoinMetadata";
                        ofType: null;
                    };
                };
                "asStakedSui": {
                    name: "asStakedSui";
                    type: {
                        kind: "OBJECT";
                        name: "StakedSui";
                        ofType: null;
                    };
                };
                "asSuinsRegistration": {
                    name: "asSuinsRegistration";
                    type: {
                        kind: "OBJECT";
                        name: "SuinsRegistration";
                        ofType: null;
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "contents": {
                    name: "contents";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "hasPublicTransfer": {
                    name: "hasPublicTransfer";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveObjectConnection: {
            kind: "OBJECT";
            name: "MoveObjectConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveObjectEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveObject";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveObjectEdge: {
            kind: "OBJECT";
            name: "MoveObjectEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObject";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MovePackage: {
            kind: "OBJECT";
            name: "MovePackage";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "latestPackage": {
                    name: "latestPackage";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackage";
                            ofType: null;
                        };
                    };
                };
                "linkage": {
                    name: "linkage";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "Linkage";
                                ofType: null;
                            };
                        };
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "OBJECT";
                        name: "MoveModule";
                        ofType: null;
                    };
                };
                "moduleBcs": {
                    name: "moduleBcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "modules": {
                    name: "modules";
                    type: {
                        kind: "OBJECT";
                        name: "MoveModuleConnection";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "packageAtVersion": {
                    name: "packageAtVersion";
                    type: {
                        kind: "OBJECT";
                        name: "MovePackage";
                        ofType: null;
                    };
                };
                "packageBcs": {
                    name: "packageBcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "packageVersions": {
                    name: "packageVersions";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackageConnection";
                            ofType: null;
                        };
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "typeOrigins": {
                    name: "typeOrigins";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "TypeOrigin";
                                ofType: null;
                            };
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MovePackageCheckpointFilter: {
            kind: "INPUT_OBJECT";
            name: "MovePackageCheckpointFilter";
            isOneOf: false;
            inputFields: [{
                name: "afterCheckpoint";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "beforeCheckpoint";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }];
        };
        MovePackageConnection: {
            kind: "OBJECT";
            name: "MovePackageConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MovePackageEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MovePackage";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MovePackageEdge: {
            kind: "OBJECT";
            name: "MovePackageEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackage";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MovePackageVersionFilter: {
            kind: "INPUT_OBJECT";
            name: "MovePackageVersionFilter";
            isOneOf: false;
            inputFields: [{
                name: "afterVersion";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "beforeVersion";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }];
        };
        MoveStruct: {
            kind: "OBJECT";
            name: "MoveStruct";
            fields: {
                "abilities": {
                    name: "abilities";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "ENUM";
                                name: "MoveAbility";
                                ofType: null;
                            };
                        };
                    };
                };
                "fields": {
                    name: "fields";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveField";
                                ofType: null;
                            };
                        };
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveModule";
                            ofType: null;
                        };
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "typeParameters": {
                    name: "typeParameters";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "MoveStructTypeParameter";
                                ofType: null;
                            };
                        };
                    };
                };
            };
        };
        MoveStructConnection: {
            kind: "OBJECT";
            name: "MoveStructConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveStructEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "MoveStruct";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveStructEdge: {
            kind: "OBJECT";
            name: "MoveStructEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveStruct";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveStructTypeParameter: {
            kind: "OBJECT";
            name: "MoveStructTypeParameter";
            fields: {
                "constraints": {
                    name: "constraints";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "ENUM";
                                    name: "MoveAbility";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "isPhantom": {
                    name: "isPhantom";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveType: {
            kind: "OBJECT";
            name: "MoveType";
            fields: {
                "abilities": {
                    name: "abilities";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "ENUM";
                                name: "MoveAbility";
                                ofType: null;
                            };
                        };
                    };
                };
                "layout": {
                    name: "layout";
                    type: {
                        kind: "SCALAR";
                        name: "MoveTypeLayout";
                        ofType: null;
                    };
                };
                "repr": {
                    name: "repr";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "signature": {
                    name: "signature";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "MoveTypeSignature";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveTypeLayout: unknown;
        MoveTypeSignature: unknown;
        MoveValue: {
            kind: "OBJECT";
            name: "MoveValue";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
                "data": {
                    name: "data";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "MoveData";
                            ofType: null;
                        };
                    };
                };
                "json": {
                    name: "json";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "JSON";
                            ofType: null;
                        };
                    };
                };
                "type": {
                    name: "type";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveType";
                            ofType: null;
                        };
                    };
                };
            };
        };
        MoveVisibility: {
            name: "MoveVisibility";
            enumValues: "PUBLIC" | "PRIVATE" | "FRIEND";
        };
        Mutation: {
            kind: "OBJECT";
            name: "Mutation";
            fields: {
                "executeTransactionBlock": {
                    name: "executeTransactionBlock";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ExecutionResult";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Object: {
            kind: "OBJECT";
            name: "Object";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "asMoveObject": {
                    name: "asMoveObject";
                    type: {
                        kind: "OBJECT";
                        name: "MoveObject";
                        ofType: null;
                    };
                };
                "asMovePackage": {
                    name: "asMovePackage";
                    type: {
                        kind: "OBJECT";
                        name: "MovePackage";
                        ofType: null;
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ObjectChange: {
            kind: "OBJECT";
            name: "ObjectChange";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "idCreated": {
                    name: "idCreated";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
                "idDeleted": {
                    name: "idDeleted";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
                "inputState": {
                    name: "inputState";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "outputState": {
                    name: "outputState";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
            };
        };
        ObjectChangeConnection: {
            kind: "OBJECT";
            name: "ObjectChangeConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ObjectChangeEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ObjectChange";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ObjectChangeEdge: {
            kind: "OBJECT";
            name: "ObjectChangeEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ObjectChange";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ObjectConnection: {
            kind: "OBJECT";
            name: "ObjectConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ObjectEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Object";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ObjectEdge: {
            kind: "OBJECT";
            name: "ObjectEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Object";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ObjectFilter: {
            kind: "INPUT_OBJECT";
            name: "ObjectFilter";
            isOneOf: false;
            inputFields: [{
                name: "type";
                type: {
                    kind: "SCALAR";
                    name: "String";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "owner";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "objectIds";
                type: {
                    kind: "LIST";
                    name: never;
                    ofType: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                defaultValue: null;
            }, {
                name: "objectKeys";
                type: {
                    kind: "LIST";
                    name: never;
                    ofType: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "INPUT_OBJECT";
                            name: "ObjectKey";
                            ofType: null;
                        };
                    };
                };
                defaultValue: null;
            }];
        };
        ObjectKey: {
            kind: "INPUT_OBJECT";
            name: "ObjectKey";
            isOneOf: false;
            inputFields: [{
                name: "objectId";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }, {
                name: "version";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }];
        };
        ObjectKind: {
            name: "ObjectKind";
            enumValues: "NOT_INDEXED" | "INDEXED";
        };
        ObjectOwner: {
            kind: "UNION";
            name: "ObjectOwner";
            fields: {};
            possibleTypes: "AddressOwner" | "ConsensusV2" | "Immutable" | "Parent" | "Shared";
        };
        ObjectRef: {
            kind: "INPUT_OBJECT";
            name: "ObjectRef";
            isOneOf: false;
            inputFields: [{
                name: "address";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }, {
                name: "version";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }, {
                name: "digest";
                type: {
                    kind: "NON_NULL";
                    name: never;
                    ofType: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                defaultValue: null;
            }];
        };
        OpenMoveType: {
            kind: "OBJECT";
            name: "OpenMoveType";
            fields: {
                "repr": {
                    name: "repr";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "signature": {
                    name: "signature";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "OpenMoveTypeSignature";
                            ofType: null;
                        };
                    };
                };
            };
        };
        OpenMoveTypeSignature: unknown;
        OwnedOrImmutable: {
            kind: "OBJECT";
            name: "OwnedOrImmutable";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "object": {
                    name: "object";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Owner: {
            kind: "OBJECT";
            name: "Owner";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "asAddress": {
                    name: "asAddress";
                    type: {
                        kind: "OBJECT";
                        name: "Address";
                        ofType: null;
                    };
                };
                "asObject": {
                    name: "asObject";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        PageInfo: {
            kind: "OBJECT";
            name: "PageInfo";
            fields: {
                "endCursor": {
                    name: "endCursor";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "hasNextPage": {
                    name: "hasNextPage";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "hasPreviousPage": {
                    name: "hasPreviousPage";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "startCursor": {
                    name: "startCursor";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
            };
        };
        Parent: {
            kind: "OBJECT";
            name: "Parent";
            fields: {
                "parent": {
                    name: "parent";
                    type: {
                        kind: "OBJECT";
                        name: "Owner";
                        ofType: null;
                    };
                };
            };
        };
        ProgrammableTransaction: {
            kind: "UNION";
            name: "ProgrammableTransaction";
            fields: {};
            possibleTypes: "MakeMoveVecTransaction" | "MergeCoinsTransaction" | "MoveCallTransaction" | "PublishTransaction" | "SplitCoinsTransaction" | "TransferObjectsTransaction" | "UpgradeTransaction";
        };
        ProgrammableTransactionBlock: {
            kind: "OBJECT";
            name: "ProgrammableTransactionBlock";
            fields: {
                "inputs": {
                    name: "inputs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionInputConnection";
                            ofType: null;
                        };
                    };
                };
                "transactions": {
                    name: "transactions";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ProgrammableTransactionConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ProgrammableTransactionConnection: {
            kind: "OBJECT";
            name: "ProgrammableTransactionConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ProgrammableTransactionEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "ProgrammableTransaction";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ProgrammableTransactionEdge: {
            kind: "OBJECT";
            name: "ProgrammableTransactionEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "ProgrammableTransaction";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ProtocolConfigAttr: {
            kind: "OBJECT";
            name: "ProtocolConfigAttr";
            fields: {
                "key": {
                    name: "key";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "value": {
                    name: "value";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
            };
        };
        ProtocolConfigFeatureFlag: {
            kind: "OBJECT";
            name: "ProtocolConfigFeatureFlag";
            fields: {
                "key": {
                    name: "key";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "value": {
                    name: "value";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ProtocolConfigs: {
            kind: "OBJECT";
            name: "ProtocolConfigs";
            fields: {
                "config": {
                    name: "config";
                    type: {
                        kind: "OBJECT";
                        name: "ProtocolConfigAttr";
                        ofType: null;
                    };
                };
                "configs": {
                    name: "configs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ProtocolConfigAttr";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "featureFlag": {
                    name: "featureFlag";
                    type: {
                        kind: "OBJECT";
                        name: "ProtocolConfigFeatureFlag";
                        ofType: null;
                    };
                };
                "featureFlags": {
                    name: "featureFlags";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ProtocolConfigFeatureFlag";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "protocolVersion": {
                    name: "protocolVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        PublishTransaction: {
            kind: "OBJECT";
            name: "PublishTransaction";
            fields: {
                "dependencies": {
                    name: "dependencies";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "SCALAR";
                                    name: "SuiAddress";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "modules": {
                    name: "modules";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "SCALAR";
                                    name: "Base64";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
            };
        };
        Pure: {
            kind: "OBJECT";
            name: "Pure";
            fields: {
                "bytes": {
                    name: "bytes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Query: {
            kind: "OBJECT";
            name: "Query";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "OBJECT";
                        name: "Address";
                        ofType: null;
                    };
                };
                "availableRange": {
                    name: "availableRange";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "AvailableRange";
                            ofType: null;
                        };
                    };
                };
                "chainIdentifier": {
                    name: "chainIdentifier";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "checkpoint": {
                    name: "checkpoint";
                    type: {
                        kind: "OBJECT";
                        name: "Checkpoint";
                        ofType: null;
                    };
                };
                "checkpoints": {
                    name: "checkpoints";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CheckpointConnection";
                            ofType: null;
                        };
                    };
                };
                "coinMetadata": {
                    name: "coinMetadata";
                    type: {
                        kind: "OBJECT";
                        name: "CoinMetadata";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "dryRunTransactionBlock": {
                    name: "dryRunTransactionBlock";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DryRunResult";
                            ofType: null;
                        };
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "epochs": {
                    name: "epochs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "EpochConnection";
                            ofType: null;
                        };
                    };
                };
                "events": {
                    name: "events";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "EventConnection";
                            ofType: null;
                        };
                    };
                };
                "latestPackage": {
                    name: "latestPackage";
                    type: {
                        kind: "OBJECT";
                        name: "MovePackage";
                        ofType: null;
                    };
                };
                "object": {
                    name: "object";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "OBJECT";
                        name: "Owner";
                        ofType: null;
                    };
                };
                "package": {
                    name: "package";
                    type: {
                        kind: "OBJECT";
                        name: "MovePackage";
                        ofType: null;
                    };
                };
                "packageByName": {
                    name: "packageByName";
                    type: {
                        kind: "OBJECT";
                        name: "MovePackage";
                        ofType: null;
                    };
                };
                "packageVersions": {
                    name: "packageVersions";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackageConnection";
                            ofType: null;
                        };
                    };
                };
                "packages": {
                    name: "packages";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MovePackageConnection";
                            ofType: null;
                        };
                    };
                };
                "protocolConfig": {
                    name: "protocolConfig";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ProtocolConfigs";
                            ofType: null;
                        };
                    };
                };
                "resolveSuinsAddress": {
                    name: "resolveSuinsAddress";
                    type: {
                        kind: "OBJECT";
                        name: "Address";
                        ofType: null;
                    };
                };
                "serviceConfig": {
                    name: "serviceConfig";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ServiceConfig";
                            ofType: null;
                        };
                    };
                };
                "transactionBlock": {
                    name: "transactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "transactionBlocks": {
                    name: "transactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "type": {
                    name: "type";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveType";
                            ofType: null;
                        };
                    };
                };
                "typeByName": {
                    name: "typeByName";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveType";
                            ofType: null;
                        };
                    };
                };
                "verifyZkloginSignature": {
                    name: "verifyZkloginSignature";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ZkLoginVerifyResult";
                            ofType: null;
                        };
                    };
                };
            };
        };
        RandomnessStateCreateTransaction: {
            kind: "OBJECT";
            name: "RandomnessStateCreateTransaction";
            fields: {
                "_": {
                    name: "_";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
            };
        };
        RandomnessStateUpdateTransaction: {
            kind: "OBJECT";
            name: "RandomnessStateUpdateTransaction";
            fields: {
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "randomBytes": {
                    name: "randomBytes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
                "randomnessObjInitialSharedVersion": {
                    name: "randomnessObjInitialSharedVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "randomnessRound": {
                    name: "randomnessRound";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Receiving: {
            kind: "OBJECT";
            name: "Receiving";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "object": {
                    name: "object";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Result: {
            kind: "OBJECT";
            name: "Result";
            fields: {
                "cmd": {
                    name: "cmd";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "ix": {
                    name: "ix";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
            };
        };
        SafeMode: {
            kind: "OBJECT";
            name: "SafeMode";
            fields: {
                "enabled": {
                    name: "enabled";
                    type: {
                        kind: "SCALAR";
                        name: "Boolean";
                        ofType: null;
                    };
                };
                "gasSummary": {
                    name: "gasSummary";
                    type: {
                        kind: "OBJECT";
                        name: "GasCostSummary";
                        ofType: null;
                    };
                };
            };
        };
        ServiceConfig: {
            kind: "OBJECT";
            name: "ServiceConfig";
            fields: {
                "defaultPageSize": {
                    name: "defaultPageSize";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "enabledFeatures": {
                    name: "enabledFeatures";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "ENUM";
                                    name: "Feature";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "isEnabled": {
                    name: "isEnabled";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "maxDbQueryCost": {
                    name: "maxDbQueryCost";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxMoveValueDepth": {
                    name: "maxMoveValueDepth";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxOutputNodes": {
                    name: "maxOutputNodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxPageSize": {
                    name: "maxPageSize";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxQueryDepth": {
                    name: "maxQueryDepth";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxQueryNodes": {
                    name: "maxQueryNodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxQueryPayloadSize": {
                    name: "maxQueryPayloadSize";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxScanLimit": {
                    name: "maxScanLimit";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxTransactionIds": {
                    name: "maxTransactionIds";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxTransactionPayloadSize": {
                    name: "maxTransactionPayloadSize";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxTypeArgumentDepth": {
                    name: "maxTypeArgumentDepth";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxTypeArgumentWidth": {
                    name: "maxTypeArgumentWidth";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "maxTypeNodes": {
                    name: "maxTypeNodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "mutationTimeoutMs": {
                    name: "mutationTimeoutMs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
                "requestTimeoutMs": {
                    name: "requestTimeoutMs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Int";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Shared: {
            kind: "OBJECT";
            name: "Shared";
            fields: {
                "initialSharedVersion": {
                    name: "initialSharedVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SharedInput: {
            kind: "OBJECT";
            name: "SharedInput";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "initialSharedVersion": {
                    name: "initialSharedVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "mutable": {
                    name: "mutable";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SharedObjectCancelled: {
            kind: "OBJECT";
            name: "SharedObjectCancelled";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SharedObjectDelete: {
            kind: "OBJECT";
            name: "SharedObjectDelete";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "mutable": {
                    name: "mutable";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SharedObjectRead: {
            kind: "OBJECT";
            name: "SharedObjectRead";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "object": {
                    name: "object";
                    type: {
                        kind: "OBJECT";
                        name: "Object";
                        ofType: null;
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SplitCoinsTransaction: {
            kind: "OBJECT";
            name: "SplitCoinsTransaction";
            fields: {
                "amounts": {
                    name: "amounts";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "TransactionArgument";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "coin": {
                    name: "coin";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "TransactionArgument";
                            ofType: null;
                        };
                    };
                };
            };
        };
        StakeStatus: {
            name: "StakeStatus";
            enumValues: "ACTIVE" | "PENDING" | "UNSTAKED";
        };
        StakeSubsidy: {
            kind: "OBJECT";
            name: "StakeSubsidy";
            fields: {
                "balance": {
                    name: "balance";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "currentDistributionAmount": {
                    name: "currentDistributionAmount";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "decreaseRate": {
                    name: "decreaseRate";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "distributionCounter": {
                    name: "distributionCounter";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "periodLength": {
                    name: "periodLength";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
            };
        };
        StakedSui: {
            kind: "OBJECT";
            name: "StakedSui";
            fields: {
                "activatedEpoch": {
                    name: "activatedEpoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "contents": {
                    name: "contents";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "estimatedReward": {
                    name: "estimatedReward";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "hasPublicTransfer": {
                    name: "hasPublicTransfer";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "poolId": {
                    name: "poolId";
                    type: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "principal": {
                    name: "principal";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "requestedEpoch": {
                    name: "requestedEpoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "stakeStatus": {
                    name: "stakeStatus";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "StakeStatus";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        StakedSuiConnection: {
            kind: "OBJECT";
            name: "StakedSuiConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "StakedSuiEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "StakedSui";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        StakedSuiEdge: {
            kind: "OBJECT";
            name: "StakedSuiEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSui";
                            ofType: null;
                        };
                    };
                };
            };
        };
        StorageFund: {
            kind: "OBJECT";
            name: "StorageFund";
            fields: {
                "nonRefundableBalance": {
                    name: "nonRefundableBalance";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "totalObjectStorageRebates": {
                    name: "totalObjectStorageRebates";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
            };
        };
        String: unknown;
        SuiAddress: unknown;
        SuinsRegistration: {
            kind: "OBJECT";
            name: "SuinsRegistration";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "balance": {
                    name: "balance";
                    type: {
                        kind: "OBJECT";
                        name: "Balance";
                        ofType: null;
                    };
                };
                "balances": {
                    name: "balances";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "coins": {
                    name: "coins";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "CoinConnection";
                            ofType: null;
                        };
                    };
                };
                "contents": {
                    name: "contents";
                    type: {
                        kind: "OBJECT";
                        name: "MoveValue";
                        ofType: null;
                    };
                };
                "defaultSuinsName": {
                    name: "defaultSuinsName";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "display": {
                    name: "display";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "OBJECT";
                                name: "DisplayEntry";
                                ofType: null;
                            };
                        };
                    };
                };
                "domain": {
                    name: "domain";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "dynamicField": {
                    name: "dynamicField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "dynamicFields": {
                    name: "dynamicFields";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DynamicFieldConnection";
                            ofType: null;
                        };
                    };
                };
                "dynamicObjectField": {
                    name: "dynamicObjectField";
                    type: {
                        kind: "OBJECT";
                        name: "DynamicField";
                        ofType: null;
                    };
                };
                "hasPublicTransfer": {
                    name: "hasPublicTransfer";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
                "objects": {
                    name: "objects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "MoveObjectConnection";
                            ofType: null;
                        };
                    };
                };
                "owner": {
                    name: "owner";
                    type: {
                        kind: "UNION";
                        name: "ObjectOwner";
                        ofType: null;
                    };
                };
                "previousTransactionBlock": {
                    name: "previousTransactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "receivedTransactionBlocks": {
                    name: "receivedTransactionBlocks";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlockConnection";
                            ofType: null;
                        };
                    };
                };
                "stakedSuis": {
                    name: "stakedSuis";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "StakedSuiConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "ENUM";
                            name: "ObjectKind";
                            ofType: null;
                        };
                    };
                };
                "storageRebate": {
                    name: "storageRebate";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "suinsRegistrations": {
                    name: "suinsRegistrations";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistrationConnection";
                            ofType: null;
                        };
                    };
                };
                "version": {
                    name: "version";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SuinsRegistrationConnection: {
            kind: "OBJECT";
            name: "SuinsRegistrationConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "SuinsRegistrationEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "SuinsRegistration";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SuinsRegistrationEdge: {
            kind: "OBJECT";
            name: "SuinsRegistrationEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "SuinsRegistration";
                            ofType: null;
                        };
                    };
                };
            };
        };
        SystemParameters: {
            kind: "OBJECT";
            name: "SystemParameters";
            fields: {
                "durationMs": {
                    name: "durationMs";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "maxValidatorCount": {
                    name: "maxValidatorCount";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "minValidatorCount": {
                    name: "minValidatorCount";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "minValidatorJoiningStake": {
                    name: "minValidatorJoiningStake";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "stakeSubsidyStartEpoch": {
                    name: "stakeSubsidyStartEpoch";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "validatorLowStakeGracePeriod": {
                    name: "validatorLowStakeGracePeriod";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "validatorLowStakeThreshold": {
                    name: "validatorLowStakeThreshold";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "validatorVeryLowStakeThreshold": {
                    name: "validatorVeryLowStakeThreshold";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
            };
        };
        TransactionArgument: {
            kind: "UNION";
            name: "TransactionArgument";
            fields: {};
            possibleTypes: "GasCoin" | "Input" | "Result";
        };
        TransactionBlock: {
            kind: "OBJECT";
            name: "TransactionBlock";
            fields: {
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "digest": {
                    name: "digest";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "effects": {
                    name: "effects";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlockEffects";
                        ofType: null;
                    };
                };
                "expiration": {
                    name: "expiration";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "gasInput": {
                    name: "gasInput";
                    type: {
                        kind: "OBJECT";
                        name: "GasInput";
                        ofType: null;
                    };
                };
                "kind": {
                    name: "kind";
                    type: {
                        kind: "UNION";
                        name: "TransactionBlockKind";
                        ofType: null;
                    };
                };
                "sender": {
                    name: "sender";
                    type: {
                        kind: "OBJECT";
                        name: "Address";
                        ofType: null;
                    };
                };
                "signatures": {
                    name: "signatures";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "SCALAR";
                                name: "Base64";
                                ofType: null;
                            };
                        };
                    };
                };
            };
        };
        TransactionBlockConnection: {
            kind: "OBJECT";
            name: "TransactionBlockConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "TransactionBlockEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "TransactionBlock";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        TransactionBlockEdge: {
            kind: "OBJECT";
            name: "TransactionBlockEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "TransactionBlock";
                            ofType: null;
                        };
                    };
                };
            };
        };
        TransactionBlockEffects: {
            kind: "OBJECT";
            name: "TransactionBlockEffects";
            fields: {
                "balanceChanges": {
                    name: "balanceChanges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "BalanceChangeConnection";
                            ofType: null;
                        };
                    };
                };
                "bcs": {
                    name: "bcs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Base64";
                            ofType: null;
                        };
                    };
                };
                "checkpoint": {
                    name: "checkpoint";
                    type: {
                        kind: "OBJECT";
                        name: "Checkpoint";
                        ofType: null;
                    };
                };
                "dependencies": {
                    name: "dependencies";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "DependencyConnection";
                            ofType: null;
                        };
                    };
                };
                "epoch": {
                    name: "epoch";
                    type: {
                        kind: "OBJECT";
                        name: "Epoch";
                        ofType: null;
                    };
                };
                "errors": {
                    name: "errors";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "events": {
                    name: "events";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "EventConnection";
                            ofType: null;
                        };
                    };
                };
                "gasEffects": {
                    name: "gasEffects";
                    type: {
                        kind: "OBJECT";
                        name: "GasEffects";
                        ofType: null;
                    };
                };
                "lamportVersion": {
                    name: "lamportVersion";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "UInt53";
                            ofType: null;
                        };
                    };
                };
                "objectChanges": {
                    name: "objectChanges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ObjectChangeConnection";
                            ofType: null;
                        };
                    };
                };
                "status": {
                    name: "status";
                    type: {
                        kind: "ENUM";
                        name: "ExecutionStatus";
                        ofType: null;
                    };
                };
                "timestamp": {
                    name: "timestamp";
                    type: {
                        kind: "SCALAR";
                        name: "DateTime";
                        ofType: null;
                    };
                };
                "transactionBlock": {
                    name: "transactionBlock";
                    type: {
                        kind: "OBJECT";
                        name: "TransactionBlock";
                        ofType: null;
                    };
                };
                "unchangedSharedObjects": {
                    name: "unchangedSharedObjects";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "UnchangedSharedObjectConnection";
                            ofType: null;
                        };
                    };
                };
            };
        };
        TransactionBlockFilter: {
            kind: "INPUT_OBJECT";
            name: "TransactionBlockFilter";
            isOneOf: false;
            inputFields: [{
                name: "function";
                type: {
                    kind: "SCALAR";
                    name: "String";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "kind";
                type: {
                    kind: "ENUM";
                    name: "TransactionBlockKindInput";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "afterCheckpoint";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "atCheckpoint";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "beforeCheckpoint";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "affectedAddress";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "sentAddress";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "inputObject";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "changedObject";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "transactionIds";
                type: {
                    kind: "LIST";
                    name: never;
                    ofType: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                defaultValue: null;
            }];
        };
        TransactionBlockKind: {
            kind: "UNION";
            name: "TransactionBlockKind";
            fields: {};
            possibleTypes: "AuthenticatorStateUpdateTransaction" | "ChangeEpochTransaction" | "ConsensusCommitPrologueTransaction" | "EndOfEpochTransaction" | "GenesisTransaction" | "ProgrammableTransactionBlock" | "RandomnessStateUpdateTransaction";
        };
        TransactionBlockKindInput: {
            name: "TransactionBlockKindInput";
            enumValues: "SYSTEM_TX" | "PROGRAMMABLE_TX";
        };
        TransactionInput: {
            kind: "UNION";
            name: "TransactionInput";
            fields: {};
            possibleTypes: "OwnedOrImmutable" | "Pure" | "Receiving" | "SharedInput";
        };
        TransactionInputConnection: {
            kind: "OBJECT";
            name: "TransactionInputConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "TransactionInputEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "TransactionInput";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        TransactionInputEdge: {
            kind: "OBJECT";
            name: "TransactionInputEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "TransactionInput";
                            ofType: null;
                        };
                    };
                };
            };
        };
        TransactionMetadata: {
            kind: "INPUT_OBJECT";
            name: "TransactionMetadata";
            isOneOf: false;
            inputFields: [{
                name: "sender";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "gasPrice";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "gasObjects";
                type: {
                    kind: "LIST";
                    name: never;
                    ofType: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "INPUT_OBJECT";
                            name: "ObjectRef";
                            ofType: null;
                        };
                    };
                };
                defaultValue: null;
            }, {
                name: "gasBudget";
                type: {
                    kind: "SCALAR";
                    name: "UInt53";
                    ofType: null;
                };
                defaultValue: null;
            }, {
                name: "gasSponsor";
                type: {
                    kind: "SCALAR";
                    name: "SuiAddress";
                    ofType: null;
                };
                defaultValue: null;
            }];
        };
        TransferObjectsTransaction: {
            kind: "OBJECT";
            name: "TransferObjectsTransaction";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "TransactionArgument";
                            ofType: null;
                        };
                    };
                };
                "inputs": {
                    name: "inputs";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "TransactionArgument";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
            };
        };
        TypeOrigin: {
            kind: "OBJECT";
            name: "TypeOrigin";
            fields: {
                "definingId": {
                    name: "definingId";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "module": {
                    name: "module";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "struct": {
                    name: "struct";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
            };
        };
        UInt53: unknown;
        UnchangedSharedObject: {
            kind: "UNION";
            name: "UnchangedSharedObject";
            fields: {};
            possibleTypes: "SharedObjectCancelled" | "SharedObjectDelete" | "SharedObjectRead";
        };
        UnchangedSharedObjectConnection: {
            kind: "OBJECT";
            name: "UnchangedSharedObjectConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "UnchangedSharedObjectEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "UNION";
                                    name: "UnchangedSharedObject";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        UnchangedSharedObjectEdge: {
            kind: "OBJECT";
            name: "UnchangedSharedObjectEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "UnchangedSharedObject";
                            ofType: null;
                        };
                    };
                };
            };
        };
        UpgradeTransaction: {
            kind: "OBJECT";
            name: "UpgradeTransaction";
            fields: {
                "currentPackage": {
                    name: "currentPackage";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "dependencies": {
                    name: "dependencies";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "SCALAR";
                                    name: "SuiAddress";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "modules": {
                    name: "modules";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "SCALAR";
                                    name: "Base64";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "upgradeTicket": {
                    name: "upgradeTicket";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "UNION";
                            name: "TransactionArgument";
                            ofType: null;
                        };
                    };
                };
            };
        };
        Validator: {
            kind: "OBJECT";
            name: "Validator";
            fields: {
                "address": {
                    name: "address";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Address";
                            ofType: null;
                        };
                    };
                };
                "apy": {
                    name: "apy";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "atRisk": {
                    name: "atRisk";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "commissionRate": {
                    name: "commissionRate";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "credentials": {
                    name: "credentials";
                    type: {
                        kind: "OBJECT";
                        name: "ValidatorCredentials";
                        ofType: null;
                    };
                };
                "description": {
                    name: "description";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "exchangeRates": {
                    name: "exchangeRates";
                    type: {
                        kind: "OBJECT";
                        name: "MoveObject";
                        ofType: null;
                    };
                };
                "exchangeRatesSize": {
                    name: "exchangeRatesSize";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "exchangeRatesTable": {
                    name: "exchangeRatesTable";
                    type: {
                        kind: "OBJECT";
                        name: "Owner";
                        ofType: null;
                    };
                };
                "gasPrice": {
                    name: "gasPrice";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "imageUrl": {
                    name: "imageUrl";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "name": {
                    name: "name";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "nextEpochCommissionRate": {
                    name: "nextEpochCommissionRate";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "nextEpochCredentials": {
                    name: "nextEpochCredentials";
                    type: {
                        kind: "OBJECT";
                        name: "ValidatorCredentials";
                        ofType: null;
                    };
                };
                "nextEpochGasPrice": {
                    name: "nextEpochGasPrice";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "nextEpochStake": {
                    name: "nextEpochStake";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "operationCap": {
                    name: "operationCap";
                    type: {
                        kind: "OBJECT";
                        name: "MoveObject";
                        ofType: null;
                    };
                };
                "pendingPoolTokenWithdraw": {
                    name: "pendingPoolTokenWithdraw";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "pendingStake": {
                    name: "pendingStake";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "pendingTotalSuiWithdraw": {
                    name: "pendingTotalSuiWithdraw";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "poolTokenBalance": {
                    name: "poolTokenBalance";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "projectUrl": {
                    name: "projectUrl";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "reportRecords": {
                    name: "reportRecords";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "AddressConnection";
                            ofType: null;
                        };
                    };
                };
                "rewardsPool": {
                    name: "rewardsPool";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "stakingPool": {
                    name: "stakingPool";
                    type: {
                        kind: "OBJECT";
                        name: "MoveObject";
                        ofType: null;
                    };
                };
                "stakingPoolActivationEpoch": {
                    name: "stakingPoolActivationEpoch";
                    type: {
                        kind: "SCALAR";
                        name: "UInt53";
                        ofType: null;
                    };
                };
                "stakingPoolId": {
                    name: "stakingPoolId";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "SuiAddress";
                            ofType: null;
                        };
                    };
                };
                "stakingPoolSuiBalance": {
                    name: "stakingPoolSuiBalance";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "votingPower": {
                    name: "votingPower";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
            };
        };
        ValidatorConnection: {
            kind: "OBJECT";
            name: "ValidatorConnection";
            fields: {
                "edges": {
                    name: "edges";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "ValidatorEdge";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "nodes": {
                    name: "nodes";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "OBJECT";
                                    name: "Validator";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "pageInfo": {
                    name: "pageInfo";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "PageInfo";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ValidatorCredentials: {
            kind: "OBJECT";
            name: "ValidatorCredentials";
            fields: {
                "netAddress": {
                    name: "netAddress";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "networkPubKey": {
                    name: "networkPubKey";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "p2PAddress": {
                    name: "p2PAddress";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "primaryAddress": {
                    name: "primaryAddress";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "proofOfPossession": {
                    name: "proofOfPossession";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "protocolPubKey": {
                    name: "protocolPubKey";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
                "workerAddress": {
                    name: "workerAddress";
                    type: {
                        kind: "SCALAR";
                        name: "String";
                        ofType: null;
                    };
                };
                "workerPubKey": {
                    name: "workerPubKey";
                    type: {
                        kind: "SCALAR";
                        name: "Base64";
                        ofType: null;
                    };
                };
            };
        };
        ValidatorEdge: {
            kind: "OBJECT";
            name: "ValidatorEdge";
            fields: {
                "cursor": {
                    name: "cursor";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "String";
                            ofType: null;
                        };
                    };
                };
                "node": {
                    name: "node";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "Validator";
                            ofType: null;
                        };
                    };
                };
            };
        };
        ValidatorSet: {
            kind: "OBJECT";
            name: "ValidatorSet";
            fields: {
                "activeValidators": {
                    name: "activeValidators";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "OBJECT";
                            name: "ValidatorConnection";
                            ofType: null;
                        };
                    };
                };
                "inactivePoolsId": {
                    name: "inactivePoolsId";
                    type: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                "inactivePoolsSize": {
                    name: "inactivePoolsSize";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "pendingActiveValidatorsId": {
                    name: "pendingActiveValidatorsId";
                    type: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                "pendingActiveValidatorsSize": {
                    name: "pendingActiveValidatorsSize";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "pendingRemovals": {
                    name: "pendingRemovals";
                    type: {
                        kind: "LIST";
                        name: never;
                        ofType: {
                            kind: "NON_NULL";
                            name: never;
                            ofType: {
                                kind: "SCALAR";
                                name: "Int";
                                ofType: null;
                            };
                        };
                    };
                };
                "stakingPoolMappingsId": {
                    name: "stakingPoolMappingsId";
                    type: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                "stakingPoolMappingsSize": {
                    name: "stakingPoolMappingsSize";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
                "totalStake": {
                    name: "totalStake";
                    type: {
                        kind: "SCALAR";
                        name: "BigInt";
                        ofType: null;
                    };
                };
                "validatorCandidatesId": {
                    name: "validatorCandidatesId";
                    type: {
                        kind: "SCALAR";
                        name: "SuiAddress";
                        ofType: null;
                    };
                };
                "validatorCandidatesSize": {
                    name: "validatorCandidatesSize";
                    type: {
                        kind: "SCALAR";
                        name: "Int";
                        ofType: null;
                    };
                };
            };
        };
        ZkLoginIntentScope: {
            name: "ZkLoginIntentScope";
            enumValues: "TRANSACTION_DATA" | "PERSONAL_MESSAGE";
        };
        ZkLoginVerifyResult: {
            kind: "OBJECT";
            name: "ZkLoginVerifyResult";
            fields: {
                "errors": {
                    name: "errors";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "LIST";
                            name: never;
                            ofType: {
                                kind: "NON_NULL";
                                name: never;
                                ofType: {
                                    kind: "SCALAR";
                                    name: "String";
                                    ofType: null;
                                };
                            };
                        };
                    };
                };
                "success": {
                    name: "success";
                    type: {
                        kind: "NON_NULL";
                        name: never;
                        ofType: {
                            kind: "SCALAR";
                            name: "Boolean";
                            ofType: null;
                        };
                    };
                };
            };
        };
    };
}, {
    isMaskingDisabled: false;
}>;
