{"version": 3, "sources": ["../../../src/cryptography/signature.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64 } from '@mysten/bcs';\n\nimport { bcs } from '../bcs/index.js';\nimport { parseSerializedPasskeySignature } from '../keypairs/passkey/publickey.js';\nimport type { MultiSigStruct } from '../multisig/publickey.js';\nimport { parseSerializedZkLoginSignature } from '../zklogin/publickey.js';\nimport type { PublicKey } from './publickey.js';\nimport type { SignatureScheme } from './signature-scheme.js';\nimport {\n\tSIGNATURE_FLAG_TO_SCHEME,\n\tSIGNATURE_SCHEME_TO_FLAG,\n\tSIGNATURE_SCHEME_TO_SIZE,\n} from './signature-scheme.js';\n\n/**\n * Pair of signature and corresponding public key\n */\nexport type SerializeSignatureInput = {\n\tsignatureScheme: SignatureScheme;\n\t/** Base64-encoded signature */\n\tsignature: Uint8Array;\n\t/** Base64-encoded public key */\n\tpublicKey?: PublicKey;\n};\n\n/**\n * Takes in a signature, its associated signing scheme and a public key, then serializes this data\n */\nexport function toSerializedSignature({\n\tsignature,\n\tsignatureScheme,\n\tpublicKey,\n}: SerializeSignatureInput): string {\n\tif (!publicKey) {\n\t\tthrow new Error('`publicKey` is required');\n\t}\n\n\tconst pubKeyBytes = publicKey.toRawBytes();\n\tconst serializedSignature = new Uint8Array(1 + signature.length + pubKeyBytes.length);\n\tserializedSignature.set([SIGNATURE_SCHEME_TO_FLAG[signatureScheme]]);\n\tserializedSignature.set(signature, 1);\n\tserializedSignature.set(pubKeyBytes, 1 + signature.length);\n\treturn toBase64(serializedSignature);\n}\n\n/**\n * Decodes a serialized signature into its constituent components: the signature scheme, the actual signature, and the public key\n */\nexport function parseSerializedSignature(serializedSignature: string) {\n\tconst bytes = fromBase64(serializedSignature);\n\n\tconst signatureScheme =\n\t\tSIGNATURE_FLAG_TO_SCHEME[bytes[0] as keyof typeof SIGNATURE_FLAG_TO_SCHEME];\n\n\tswitch (signatureScheme) {\n\t\tcase 'Passkey':\n\t\t\treturn parseSerializedPasskeySignature(serializedSignature);\n\t\tcase 'MultiSig':\n\t\t\tconst multisig: MultiSigStruct = bcs.MultiSig.parse(bytes.slice(1));\n\t\t\treturn {\n\t\t\t\tserializedSignature,\n\t\t\t\tsignatureScheme,\n\t\t\t\tmultisig,\n\t\t\t\tbytes,\n\t\t\t};\n\t\tcase 'ZkLogin':\n\t\t\treturn parseSerializedZkLoginSignature(serializedSignature);\n\t\tcase 'ED25519':\n\t\tcase 'Secp256k1':\n\t\tcase 'Secp256r1':\n\t\t\tconst size =\n\t\t\t\tSIGNATURE_SCHEME_TO_SIZE[signatureScheme as keyof typeof SIGNATURE_SCHEME_TO_SIZE];\n\t\t\tconst signature = bytes.slice(1, bytes.length - size);\n\t\t\tconst publicKey = bytes.slice(1 + signature.length);\n\n\t\t\treturn {\n\t\t\t\tserializedSignature,\n\t\t\t\tsignatureScheme,\n\t\t\t\tsignature,\n\t\t\t\tpublicKey,\n\t\t\t\tbytes,\n\t\t\t};\n\t\tdefault:\n\t\t\tthrow new Error('Unsupported signature scheme');\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAqC;AAErC,IAAAA,cAAoB;AACpB,uBAAgD;AAEhD,IAAAC,oBAAgD;AAGhD,8BAIO;AAgBA,SAAS,sBAAsB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AACD,GAAoC;AACnC,MAAI,CAAC,WAAW;AACf,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAEA,QAAM,cAAc,UAAU,WAAW;AACzC,QAAM,sBAAsB,IAAI,WAAW,IAAI,UAAU,SAAS,YAAY,MAAM;AACpF,sBAAoB,IAAI,CAAC,iDAAyB,eAAe,CAAC,CAAC;AACnE,sBAAoB,IAAI,WAAW,CAAC;AACpC,sBAAoB,IAAI,aAAa,IAAI,UAAU,MAAM;AACzD,aAAO,qBAAS,mBAAmB;AACpC;AAKO,SAAS,yBAAyB,qBAA6B;AACrE,QAAM,YAAQ,uBAAW,mBAAmB;AAE5C,QAAM,kBACL,iDAAyB,MAAM,CAAC,CAA0C;AAE3E,UAAQ,iBAAiB;AAAA,IACxB,KAAK;AACJ,iBAAO,kDAAgC,mBAAmB;AAAA,IAC3D,KAAK;AACJ,YAAM,WAA2B,gBAAI,SAAS,MAAM,MAAM,MAAM,CAAC,CAAC;AAClE,aAAO;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD,KAAK;AACJ,iBAAO,mDAAgC,mBAAmB;AAAA,IAC3D,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACJ,YAAM,OACL,iDAAyB,eAAwD;AAClF,YAAM,YAAY,MAAM,MAAM,GAAG,MAAM,SAAS,IAAI;AACpD,YAAM,YAAY,MAAM,MAAM,IAAI,UAAU,MAAM;AAElD,aAAO;AAAA,QACN;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACD;AAAA,IACD;AACC,YAAM,IAAI,MAAM,8BAA8B;AAAA,EAChD;AACD;", "names": ["import_bcs", "import_publickey"]}