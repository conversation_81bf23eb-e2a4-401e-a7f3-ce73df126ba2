{"version": 3, "sources": ["../../../src/transactions/pure.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { isSerializedBcs } from '@mysten/bcs';\nimport type { BcsType, SerializedBcs } from '@mysten/bcs';\n\nimport { bcs } from '../bcs/index.js';\nimport type { Argument } from './data/internal.js';\n\nexport function createPure<T>(makePure: (value: SerializedBcs<any, any> | Uint8Array) => T) {\n\tfunction pure<T extends PureTypeName>(\n\t\ttype: T extends PureTypeName ? ValidPureTypeName<T> : T,\n\t\tvalue: ShapeFromPureTypeName<T>,\n\t): T;\n\n\tfunction pure(\n\t\t/**\n\t\t * The pure value, serialized to BCS. If this is a Uint8Array, then the value\n\t\t * is assumed to be raw bytes, and will be used directly.\n\t\t */\n\t\tvalue: SerializedBcs<any, any> | Uint8Array,\n\t): T;\n\n\tfunction pure(\n\t\ttypeOrSerializedValue?: PureTypeName | SerializedBcs<any, any> | Uint8Array,\n\t\tvalue?: unknown,\n\t): T {\n\t\tif (typeof typeOrSerializedValue === 'string') {\n\t\t\treturn makePure(schemaFromName(typeOrSerializedValue).serialize(value as never));\n\t\t}\n\n\t\tif (typeOrSerializedValue instanceof Uint8Array || isSerializedBcs(typeOrSerializedValue)) {\n\t\t\treturn makePure(typeOrSerializedValue);\n\t\t}\n\n\t\tthrow new Error('tx.pure must be called either a bcs type name, or a serialized bcs value');\n\t}\n\n\tpure.u8 = (value: number) => makePure(bcs.U8.serialize(value));\n\tpure.u16 = (value: number) => makePure(bcs.U16.serialize(value));\n\tpure.u32 = (value: number) => makePure(bcs.U32.serialize(value));\n\tpure.u64 = (value: bigint | number | string) => makePure(bcs.U64.serialize(value));\n\tpure.u128 = (value: bigint | number | string) => makePure(bcs.U128.serialize(value));\n\tpure.u256 = (value: bigint | number | string) => makePure(bcs.U256.serialize(value));\n\tpure.bool = (value: boolean) => makePure(bcs.Bool.serialize(value));\n\tpure.string = (value: string) => makePure(bcs.String.serialize(value));\n\tpure.address = (value: string) => makePure(bcs.Address.serialize(value));\n\tpure.id = pure.address;\n\tpure.vector = <Type extends PureTypeName>(\n\t\ttype: T extends PureTypeName ? ValidPureTypeName<Type> : Type,\n\t\tvalue: Iterable<ShapeFromPureTypeName<Type>> & { length: number },\n\t) => {\n\t\treturn makePure(bcs.vector(schemaFromName(type as BasePureType)).serialize(value as never));\n\t};\n\tpure.option = <Type extends PureTypeName>(\n\t\ttype: T extends PureTypeName ? ValidPureTypeName<Type> : Type,\n\t\tvalue: ShapeFromPureTypeName<Type> | null | undefined,\n\t) => {\n\t\treturn makePure(bcs.option(schemaFromName(type)).serialize(value as never));\n\t};\n\n\treturn pure;\n}\n\nexport type BasePureType =\n\t| 'u8'\n\t| 'u16'\n\t| 'u32'\n\t| 'u64'\n\t| 'u128'\n\t| 'u256'\n\t| 'bool'\n\t| 'id'\n\t| 'string'\n\t| 'address';\n\nexport type PureTypeName = BasePureType | `vector<${string}>` | `option<${string}>`;\nexport type ValidPureTypeName<T extends string> = T extends BasePureType\n\t? PureTypeName\n\t: T extends `vector<${infer U}>`\n\t\t? ValidPureTypeName<U>\n\t\t: T extends `option<${infer U}>`\n\t\t\t? ValidPureTypeName<U>\n\t\t\t: PureTypeValidationError<T>;\n\ntype ShapeFromPureTypeName<T extends PureTypeName> = T extends BasePureType\n\t? Parameters<ReturnType<typeof createPure<Argument>>[T]>[0]\n\t: T extends `vector<${infer U extends PureTypeName}>`\n\t\t? ShapeFromPureTypeName<U>[]\n\t\t: T extends `option<${infer U extends PureTypeName}>`\n\t\t\t? ShapeFromPureTypeName<U> | null\n\t\t\t: never;\n\ntype PureTypeValidationError<T extends string> = T & {\n\terror: `Invalid Pure type name: ${T}`;\n};\n\nfunction schemaFromName<T extends PureTypeName>(\n\tname: T extends PureTypeName ? ValidPureTypeName<T> : T,\n): BcsType<ShapeFromPureTypeName<T>> {\n\tswitch (name) {\n\t\tcase 'u8':\n\t\t\treturn bcs.u8() as never;\n\t\tcase 'u16':\n\t\t\treturn bcs.u16() as never;\n\t\tcase 'u32':\n\t\t\treturn bcs.u32() as never;\n\t\tcase 'u64':\n\t\t\treturn bcs.u64() as never;\n\t\tcase 'u128':\n\t\t\treturn bcs.u128() as never;\n\t\tcase 'u256':\n\t\t\treturn bcs.u256() as never;\n\t\tcase 'bool':\n\t\t\treturn bcs.bool() as never;\n\t\tcase 'string':\n\t\t\treturn bcs.string() as never;\n\t\tcase 'id':\n\t\tcase 'address':\n\t\t\treturn bcs.Address as never;\n\t}\n\n\tconst generic = name.match(/^(vector|option)<(.+)>$/);\n\tif (generic) {\n\t\tconst [kind, inner] = generic.slice(1);\n\t\tif (kind === 'vector') {\n\t\t\treturn bcs.vector(schemaFromName(inner as PureTypeName)) as never;\n\t\t} else {\n\t\t\treturn bcs.option(schemaFromName(inner as PureTypeName)) as never;\n\t\t}\n\t}\n\n\tthrow new Error(`Invalid Pure type name: ${name}`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAgC;AAGhC,IAAAA,cAAoB;AAGb,SAAS,WAAc,UAA8D;AAc3F,WAAS,KACR,uBACA,OACI;AACJ,QAAI,OAAO,0BAA0B,UAAU;AAC9C,aAAO,SAAS,eAAe,qBAAqB,EAAE,UAAU,KAAc,CAAC;AAAA,IAChF;AAEA,QAAI,iCAAiC,kBAAc,4BAAgB,qBAAqB,GAAG;AAC1F,aAAO,SAAS,qBAAqB;AAAA,IACtC;AAEA,UAAM,IAAI,MAAM,0EAA0E;AAAA,EAC3F;AAEA,OAAK,KAAK,CAAC,UAAkB,SAAS,gBAAI,GAAG,UAAU,KAAK,CAAC;AAC7D,OAAK,MAAM,CAAC,UAAkB,SAAS,gBAAI,IAAI,UAAU,KAAK,CAAC;AAC/D,OAAK,MAAM,CAAC,UAAkB,SAAS,gBAAI,IAAI,UAAU,KAAK,CAAC;AAC/D,OAAK,MAAM,CAAC,UAAoC,SAAS,gBAAI,IAAI,UAAU,KAAK,CAAC;AACjF,OAAK,OAAO,CAAC,UAAoC,SAAS,gBAAI,KAAK,UAAU,KAAK,CAAC;AACnF,OAAK,OAAO,CAAC,UAAoC,SAAS,gBAAI,KAAK,UAAU,KAAK,CAAC;AACnF,OAAK,OAAO,CAAC,UAAmB,SAAS,gBAAI,KAAK,UAAU,KAAK,CAAC;AAClE,OAAK,SAAS,CAAC,UAAkB,SAAS,gBAAI,OAAO,UAAU,KAAK,CAAC;AACrE,OAAK,UAAU,CAAC,UAAkB,SAAS,gBAAI,QAAQ,UAAU,KAAK,CAAC;AACvE,OAAK,KAAK,KAAK;AACf,OAAK,SAAS,CACb,MACA,UACI;AACJ,WAAO,SAAS,gBAAI,OAAO,eAAe,IAAoB,CAAC,EAAE,UAAU,KAAc,CAAC;AAAA,EAC3F;AACA,OAAK,SAAS,CACb,MACA,UACI;AACJ,WAAO,SAAS,gBAAI,OAAO,eAAe,IAAI,CAAC,EAAE,UAAU,KAAc,CAAC;AAAA,EAC3E;AAEA,SAAO;AACR;AAmCA,SAAS,eACR,MACoC;AACpC,UAAQ,MAAM;AAAA,IACb,KAAK;AACJ,aAAO,gBAAI,GAAG;AAAA,IACf,KAAK;AACJ,aAAO,gBAAI,IAAI;AAAA,IAChB,KAAK;AACJ,aAAO,gBAAI,IAAI;AAAA,IAChB,KAAK;AACJ,aAAO,gBAAI,IAAI;AAAA,IAChB,KAAK;AACJ,aAAO,gBAAI,KAAK;AAAA,IACjB,KAAK;AACJ,aAAO,gBAAI,KAAK;AAAA,IACjB,KAAK;AACJ,aAAO,gBAAI,KAAK;AAAA,IACjB,KAAK;AACJ,aAAO,gBAAI,OAAO;AAAA,IACnB,KAAK;AAAA,IACL,KAAK;AACJ,aAAO,gBAAI;AAAA,EACb;AAEA,QAAM,UAAU,KAAK,MAAM,yBAAyB;AACpD,MAAI,SAAS;AACZ,UAAM,CAAC,MAAM,KAAK,IAAI,QAAQ,MAAM,CAAC;AACrC,QAAI,SAAS,UAAU;AACtB,aAAO,gBAAI,OAAO,eAAe,KAAqB,CAAC;AAAA,IACxD,OAAO;AACN,aAAO,gBAAI,OAAO,eAAe,KAAqB,CAAC;AAAA,IACxD;AAAA,EACD;AAEA,QAAM,IAAI,MAAM,2BAA2B,IAAI,EAAE;AAClD;", "names": ["import_bcs"]}