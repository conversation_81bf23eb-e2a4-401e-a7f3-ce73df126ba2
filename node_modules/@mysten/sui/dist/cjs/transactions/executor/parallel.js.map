{"version": 3, "sources": ["../../../../src/transactions/executor/parallel.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/bcs';\n\nimport { bcs } from '../../bcs/index.js';\nimport type { SuiObjectRef } from '../../bcs/types.js';\nimport type {\n\tSuiClient,\n\tSuiTransactionBlockResponse,\n\tSuiTransactionBlockResponseOptions,\n} from '../../client/index.js';\nimport type { Signer } from '../../cryptography/index.js';\nimport type { ObjectCacheOptions } from '../ObjectCache.js';\nimport { Transaction } from '../Transaction.js';\nimport { TransactionDataBuilder } from '../TransactionData.js';\nimport { CachingTransactionExecutor } from './caching.js';\nimport { ParallelQueue, SerialQueue } from './queue.js';\nimport { getGasCoinFromEffects } from './serial.js';\n\nconst PARALLEL_EXECUTOR_DEFAULTS = {\n\tcoinBatchSize: 20,\n\tinitialCoinBalance: 200_000_000n,\n\tminimumCoinBalance: 50_000_000n,\n\tmaxPoolSize: 50,\n\tepochBoundaryWindow: 1_000,\n} satisfies Omit<ParallelTransactionExecutorOptions, 'signer' | 'client'>;\nexport interface ParallelTransactionExecutorOptions extends Omit<ObjectCacheOptions, 'address'> {\n\tclient: SuiClient;\n\tsigner: Signer;\n\t/** The number of coins to create in a batch when refilling the gas pool */\n\tcoinBatchSize?: number;\n\t/** The initial balance of each coin created for the gas pool */\n\tinitialCoinBalance?: bigint;\n\t/** The minimum balance of a coin that can be reused for future transactions.  If the gasCoin is below this value, it will be used when refilling the gasPool */\n\tminimumCoinBalance?: bigint;\n\t/** The gasBudget to use if the transaction has not defined it's own gasBudget, defaults to `minimumCoinBalance` */\n\tdefaultGasBudget?: bigint;\n\t/**\n\t * Time to wait before/after the expected epoch boundary before re-fetching the gas pool (in milliseconds).\n\t * Building transactions will be paused for up to 2x this duration around each epoch boundary to ensure the\n\t * gas price is up-to-date for the next epoch.\n\t * */\n\tepochBoundaryWindow?: number;\n\t/** The maximum number of transactions that can be execute in parallel, this also determines the maximum number of gas coins that will be created */\n\tmaxPoolSize?: number;\n\t/** An initial list of coins used to fund the gas pool, uses all owned SUI coins by default */\n\tsourceCoins?: string[];\n}\n\ninterface CoinWithBalance {\n\tid: string;\n\tversion: string;\n\tdigest: string;\n\tbalance: bigint;\n}\nexport class ParallelTransactionExecutor {\n\t#signer: Signer;\n\t#client: SuiClient;\n\t#coinBatchSize: number;\n\t#initialCoinBalance: bigint;\n\t#minimumCoinBalance: bigint;\n\t#epochBoundaryWindow: number;\n\t#defaultGasBudget: bigint;\n\t#maxPoolSize: number;\n\t#sourceCoins: Map<string, SuiObjectRef | null> | null;\n\t#coinPool: CoinWithBalance[] = [];\n\t#cache: CachingTransactionExecutor;\n\t#objectIdQueues = new Map<string, (() => void)[]>();\n\t#buildQueue = new SerialQueue();\n\t#executeQueue: ParallelQueue;\n\t#lastDigest: string | null = null;\n\t#cacheLock: Promise<void> | null = null;\n\t#pendingTransactions = 0;\n\t#gasPrice: null | {\n\t\tprice: bigint;\n\t\texpiration: number;\n\t} = null;\n\n\tconstructor(options: ParallelTransactionExecutorOptions) {\n\t\tthis.#signer = options.signer;\n\t\tthis.#client = options.client;\n\t\tthis.#coinBatchSize = options.coinBatchSize ?? PARALLEL_EXECUTOR_DEFAULTS.coinBatchSize;\n\t\tthis.#initialCoinBalance =\n\t\t\toptions.initialCoinBalance ?? PARALLEL_EXECUTOR_DEFAULTS.initialCoinBalance;\n\t\tthis.#minimumCoinBalance =\n\t\t\toptions.minimumCoinBalance ?? PARALLEL_EXECUTOR_DEFAULTS.minimumCoinBalance;\n\t\tthis.#defaultGasBudget = options.defaultGasBudget ?? this.#minimumCoinBalance;\n\t\tthis.#epochBoundaryWindow =\n\t\t\toptions.epochBoundaryWindow ?? PARALLEL_EXECUTOR_DEFAULTS.epochBoundaryWindow;\n\t\tthis.#maxPoolSize = options.maxPoolSize ?? PARALLEL_EXECUTOR_DEFAULTS.maxPoolSize;\n\t\tthis.#cache = new CachingTransactionExecutor({\n\t\t\tclient: options.client,\n\t\t\tcache: options.cache,\n\t\t});\n\t\tthis.#executeQueue = new ParallelQueue(this.#maxPoolSize);\n\t\tthis.#sourceCoins = options.sourceCoins\n\t\t\t? new Map(options.sourceCoins.map((id) => [id, null]))\n\t\t\t: null;\n\t}\n\n\tresetCache() {\n\t\tthis.#gasPrice = null;\n\t\treturn this.#updateCache(() => this.#cache.reset());\n\t}\n\n\tasync waitForLastTransaction() {\n\t\tawait this.#updateCache(() => this.#waitForLastDigest());\n\t}\n\n\tasync executeTransaction(transaction: Transaction, options?: SuiTransactionBlockResponseOptions) {\n\t\tconst { promise, resolve, reject } = promiseWithResolvers<{\n\t\t\tdigest: string;\n\t\t\teffects: string;\n\t\t\tdata: SuiTransactionBlockResponse;\n\t\t}>();\n\t\tconst usedObjects = await this.#getUsedObjects(transaction);\n\n\t\tconst execute = () => {\n\t\t\tthis.#executeQueue.runTask(() => {\n\t\t\t\tconst promise = this.#execute(transaction, usedObjects, options);\n\n\t\t\t\treturn promise.then(resolve, reject);\n\t\t\t});\n\t\t};\n\n\t\tconst conflicts = new Set<string>();\n\n\t\tusedObjects.forEach((objectId) => {\n\t\t\tconst queue = this.#objectIdQueues.get(objectId);\n\t\t\tif (queue) {\n\t\t\t\tconflicts.add(objectId);\n\t\t\t\tthis.#objectIdQueues.get(objectId)!.push(() => {\n\t\t\t\t\tconflicts.delete(objectId);\n\t\t\t\t\tif (conflicts.size === 0) {\n\t\t\t\t\t\texecute();\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\tthis.#objectIdQueues.set(objectId, []);\n\t\t\t}\n\t\t});\n\n\t\tif (conflicts.size === 0) {\n\t\t\texecute();\n\t\t}\n\n\t\treturn promise;\n\t}\n\n\tasync #getUsedObjects(transaction: Transaction) {\n\t\tconst usedObjects = new Set<string>();\n\t\tlet serialized = false;\n\n\t\ttransaction.addSerializationPlugin(async (blockData, _options, next) => {\n\t\t\tawait next();\n\n\t\t\tif (serialized) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tserialized = true;\n\n\t\t\tblockData.inputs.forEach((input) => {\n\t\t\t\tif (input.Object?.ImmOrOwnedObject?.objectId) {\n\t\t\t\t\tusedObjects.add(input.Object.ImmOrOwnedObject.objectId);\n\t\t\t\t} else if (input.Object?.Receiving?.objectId) {\n\t\t\t\t\tusedObjects.add(input.Object.Receiving.objectId);\n\t\t\t\t} else if (\n\t\t\t\t\tinput.UnresolvedObject?.objectId &&\n\t\t\t\t\t!input.UnresolvedObject.initialSharedVersion\n\t\t\t\t) {\n\t\t\t\t\tusedObjects.add(input.UnresolvedObject.objectId);\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\tawait transaction.prepareForSerialization({ client: this.#client });\n\n\t\treturn usedObjects;\n\t}\n\n\tasync #execute(\n\t\ttransaction: Transaction,\n\t\tusedObjects: Set<string>,\n\t\toptions?: SuiTransactionBlockResponseOptions,\n\t) {\n\t\tlet gasCoin!: CoinWithBalance;\n\t\ttry {\n\t\t\ttransaction.setSenderIfNotSet(this.#signer.toSuiAddress());\n\n\t\t\tawait this.#buildQueue.runTask(async () => {\n\t\t\t\tconst data = transaction.getData();\n\n\t\t\t\tif (!data.gasData.price) {\n\t\t\t\t\ttransaction.setGasPrice(await this.#getGasPrice());\n\t\t\t\t}\n\n\t\t\t\ttransaction.setGasBudgetIfNotSet(this.#defaultGasBudget);\n\n\t\t\t\tawait this.#updateCache();\n\t\t\t\tgasCoin = await this.#getGasCoin();\n\t\t\t\tthis.#pendingTransactions++;\n\t\t\t\ttransaction.setGasPayment([\n\t\t\t\t\t{\n\t\t\t\t\t\tobjectId: gasCoin.id,\n\t\t\t\t\t\tversion: gasCoin.version,\n\t\t\t\t\t\tdigest: gasCoin.digest,\n\t\t\t\t\t},\n\t\t\t\t]);\n\n\t\t\t\t// Resolve cached references\n\t\t\t\tawait this.#cache.buildTransaction({ transaction, onlyTransactionKind: true });\n\t\t\t});\n\n\t\t\tconst bytes = await transaction.build({ client: this.#client });\n\n\t\t\tconst { signature } = await this.#signer.signTransaction(bytes);\n\n\t\t\tconst results = await this.#cache.executeTransaction({\n\t\t\t\ttransaction: bytes,\n\t\t\t\tsignature,\n\t\t\t\toptions: {\n\t\t\t\t\t...options,\n\t\t\t\t\tshowEffects: true,\n\t\t\t\t},\n\t\t\t});\n\n\t\t\tconst effectsBytes = Uint8Array.from(results.rawEffects!);\n\t\t\tconst effects = bcs.TransactionEffects.parse(effectsBytes);\n\n\t\t\tconst gasResult = getGasCoinFromEffects(effects);\n\t\t\tconst gasUsed = effects.V2?.gasUsed;\n\n\t\t\tif (gasCoin && gasUsed && gasResult.owner === this.#signer.toSuiAddress()) {\n\t\t\t\tconst totalUsed =\n\t\t\t\t\tBigInt(gasUsed.computationCost) +\n\t\t\t\t\tBigInt(gasUsed.storageCost) +\n\t\t\t\t\tBigInt(gasUsed.storageCost) -\n\t\t\t\t\tBigInt(gasUsed.storageRebate);\n\n\t\t\t\tlet usesGasCoin = false;\n\t\t\t\tnew TransactionDataBuilder(transaction.getData()).mapArguments((arg) => {\n\t\t\t\t\tif (arg.$kind === 'GasCoin') {\n\t\t\t\t\t\tusesGasCoin = true;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn arg;\n\t\t\t\t});\n\n\t\t\t\tif (!usesGasCoin && gasCoin.balance >= this.#minimumCoinBalance) {\n\t\t\t\t\tthis.#coinPool.push({\n\t\t\t\t\t\tid: gasResult.ref.objectId,\n\t\t\t\t\t\tversion: gasResult.ref.version,\n\t\t\t\t\t\tdigest: gasResult.ref.digest,\n\t\t\t\t\t\tbalance: gasCoin.balance - totalUsed,\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tif (!this.#sourceCoins) {\n\t\t\t\t\t\tthis.#sourceCoins = new Map();\n\t\t\t\t\t}\n\t\t\t\t\tthis.#sourceCoins.set(gasResult.ref.objectId, gasResult.ref);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis.#lastDigest = results.digest;\n\n\t\t\treturn {\n\t\t\t\tdigest: results.digest,\n\t\t\t\teffects: toBase64(effectsBytes),\n\t\t\t\tdata: results,\n\t\t\t};\n\t\t} catch (error) {\n\t\t\tif (gasCoin) {\n\t\t\t\tif (!this.#sourceCoins) {\n\t\t\t\t\tthis.#sourceCoins = new Map();\n\t\t\t\t}\n\n\t\t\t\tthis.#sourceCoins.set(gasCoin.id, null);\n\t\t\t}\n\n\t\t\tawait this.#updateCache(async () => {\n\t\t\t\tawait Promise.all([\n\t\t\t\t\tthis.#cache.cache.deleteObjects([...usedObjects]),\n\t\t\t\t\tthis.#waitForLastDigest(),\n\t\t\t\t]);\n\t\t\t});\n\n\t\t\tthrow error;\n\t\t} finally {\n\t\t\tusedObjects.forEach((objectId) => {\n\t\t\t\tconst queue = this.#objectIdQueues.get(objectId);\n\t\t\t\tif (queue && queue.length > 0) {\n\t\t\t\t\tqueue.shift()!();\n\t\t\t\t} else if (queue) {\n\t\t\t\t\tthis.#objectIdQueues.delete(objectId);\n\t\t\t\t}\n\t\t\t});\n\t\t\tthis.#pendingTransactions--;\n\t\t}\n\t}\n\n\t/** Helper for synchronizing cache updates, by ensuring only one update happens at a time.  This can also be used to wait for any pending cache updates  */\n\tasync #updateCache(fn?: () => Promise<void>) {\n\t\tif (this.#cacheLock) {\n\t\t\tawait this.#cacheLock;\n\t\t}\n\n\t\tthis.#cacheLock =\n\t\t\tfn?.().then(\n\t\t\t\t() => {\n\t\t\t\t\tthis.#cacheLock = null;\n\t\t\t\t},\n\t\t\t\t() => {},\n\t\t\t) ?? null;\n\t}\n\n\tasync #waitForLastDigest() {\n\t\tconst digest = this.#lastDigest;\n\t\tif (digest) {\n\t\t\tthis.#lastDigest = null;\n\t\t\tawait this.#client.waitForTransaction({ digest });\n\t\t}\n\t}\n\n\tasync #getGasCoin() {\n\t\tif (this.#coinPool.length === 0 && this.#pendingTransactions <= this.#maxPoolSize) {\n\t\t\tawait this.#refillCoinPool();\n\t\t}\n\n\t\tif (this.#coinPool.length === 0) {\n\t\t\tthrow new Error('No coins available');\n\t\t}\n\n\t\tconst coin = this.#coinPool.shift()!;\n\t\treturn coin;\n\t}\n\n\tasync #getGasPrice(): Promise<bigint> {\n\t\tconst remaining = this.#gasPrice\n\t\t\t? this.#gasPrice.expiration - this.#epochBoundaryWindow - Date.now()\n\t\t\t: 0;\n\n\t\tif (remaining > 0) {\n\t\t\treturn this.#gasPrice!.price;\n\t\t}\n\n\t\tif (this.#gasPrice) {\n\t\t\tconst timeToNextEpoch = Math.max(\n\t\t\t\tthis.#gasPrice.expiration + this.#epochBoundaryWindow - Date.now(),\n\t\t\t\t1_000,\n\t\t\t);\n\n\t\t\tawait new Promise((resolve) => setTimeout(resolve, timeToNextEpoch));\n\t\t}\n\n\t\tconst state = await this.#client.getLatestSuiSystemState();\n\n\t\tthis.#gasPrice = {\n\t\t\tprice: BigInt(state.referenceGasPrice),\n\t\t\texpiration:\n\t\t\t\tNumber.parseInt(state.epochStartTimestampMs, 10) +\n\t\t\t\tNumber.parseInt(state.epochDurationMs, 10),\n\t\t};\n\n\t\treturn this.#getGasPrice();\n\t}\n\n\tasync #refillCoinPool() {\n\t\tconst batchSize = Math.min(\n\t\t\tthis.#coinBatchSize,\n\t\t\tthis.#maxPoolSize - (this.#coinPool.length + this.#pendingTransactions) + 1,\n\t\t);\n\n\t\tif (batchSize === 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst txb = new Transaction();\n\t\tconst address = this.#signer.toSuiAddress();\n\t\ttxb.setSender(address);\n\n\t\tif (this.#sourceCoins) {\n\t\t\tconst refs = [];\n\t\t\tconst ids = [];\n\t\t\tfor (const [id, ref] of this.#sourceCoins) {\n\t\t\t\tif (ref) {\n\t\t\t\t\trefs.push(ref);\n\t\t\t\t} else {\n\t\t\t\t\tids.push(id);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (ids.length > 0) {\n\t\t\t\tconst coins = await this.#client.multiGetObjects({\n\t\t\t\t\tids,\n\t\t\t\t});\n\t\t\t\trefs.push(\n\t\t\t\t\t...coins\n\t\t\t\t\t\t.filter((coin): coin is typeof coin & { data: object } => coin.data !== null)\n\t\t\t\t\t\t.map(({ data }) => ({\n\t\t\t\t\t\t\tobjectId: data.objectId,\n\t\t\t\t\t\t\tversion: data.version,\n\t\t\t\t\t\t\tdigest: data.digest,\n\t\t\t\t\t\t})),\n\t\t\t\t);\n\t\t\t}\n\n\t\t\ttxb.setGasPayment(refs);\n\t\t\tthis.#sourceCoins = new Map();\n\t\t}\n\n\t\tconst amounts = new Array(batchSize).fill(this.#initialCoinBalance);\n\t\tconst results = txb.splitCoins(txb.gas, amounts);\n\t\tconst coinResults = [];\n\t\tfor (let i = 0; i < amounts.length; i++) {\n\t\t\tcoinResults.push(results[i]);\n\t\t}\n\t\ttxb.transferObjects(coinResults, address);\n\n\t\tawait this.waitForLastTransaction();\n\n\t\tconst result = await this.#client.signAndExecuteTransaction({\n\t\t\ttransaction: txb,\n\t\t\tsigner: this.#signer,\n\t\t\toptions: {\n\t\t\t\tshowRawEffects: true,\n\t\t\t},\n\t\t});\n\n\t\tconst effects = bcs.TransactionEffects.parse(Uint8Array.from(result.rawEffects!));\n\t\teffects.V2?.changedObjects.forEach(([id, { outputState }], i) => {\n\t\t\tif (i === effects.V2?.gasObjectIndex || !outputState.ObjectWrite) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.#coinPool.push({\n\t\t\t\tid,\n\t\t\t\tversion: effects.V2!.lamportVersion,\n\t\t\t\tdigest: outputState.ObjectWrite[0],\n\t\t\t\tbalance: BigInt(this.#initialCoinBalance),\n\t\t\t});\n\t\t});\n\n\t\tif (!this.#sourceCoins) {\n\t\t\tthis.#sourceCoins = new Map();\n\t\t}\n\n\t\tconst gasObject = getGasCoinFromEffects(effects).ref;\n\t\tthis.#sourceCoins!.set(gasObject.objectId, gasObject);\n\n\t\tawait this.#client.waitForTransaction({ digest: result.digest });\n\t}\n}\n\nfunction promiseWithResolvers<T>() {\n\tlet resolve: (value: T) => void;\n\tlet reject: (reason: any) => void;\n\n\tconst promise = new Promise<T>((_resolve, _reject) => {\n\t\tresolve = _resolve;\n\t\treject = _reject;\n\t});\n\n\treturn { promise, resolve: resolve!, reject: reject! };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAyB;AAEzB,IAAAA,cAAoB;AASpB,yBAA4B;AAC5B,6BAAuC;AACvC,qBAA2C;AAC3C,mBAA2C;AAC3C,oBAAsC;AAlBtC;AAoBA,MAAM,6BAA6B;AAAA,EAClC,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,aAAa;AAAA,EACb,qBAAqB;AACtB;AA8BO,MAAM,4BAA4B;AAAA,EAuBxC,YAAY,SAA6C;AAvBnD;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,kCAA+B,CAAC;AAChC;AACA,wCAAkB,oBAAI,IAA4B;AAClD,oCAAc,IAAI,yBAAY;AAC9B;AACA,oCAA6B;AAC7B,mCAAmC;AACnC,6CAAuB;AACvB,kCAGI;AAGH,uBAAK,SAAU,QAAQ;AACvB,uBAAK,SAAU,QAAQ;AACvB,uBAAK,gBAAiB,QAAQ,iBAAiB,2BAA2B;AAC1E,uBAAK,qBACJ,QAAQ,sBAAsB,2BAA2B;AAC1D,uBAAK,qBACJ,QAAQ,sBAAsB,2BAA2B;AAC1D,uBAAK,mBAAoB,QAAQ,oBAAoB,mBAAK;AAC1D,uBAAK,sBACJ,QAAQ,uBAAuB,2BAA2B;AAC3D,uBAAK,cAAe,QAAQ,eAAe,2BAA2B;AACtE,uBAAK,QAAS,IAAI,0CAA2B;AAAA,MAC5C,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ;AAAA,IAChB,CAAC;AACD,uBAAK,eAAgB,IAAI,2BAAc,mBAAK,aAAY;AACxD,uBAAK,cAAe,QAAQ,cACzB,IAAI,IAAI,QAAQ,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC,CAAC,IACnD;AAAA,EACJ;AAAA,EAEA,aAAa;AACZ,uBAAK,WAAY;AACjB,WAAO,sBAAK,wDAAL,WAAkB,MAAM,mBAAK,QAAO,MAAM;AAAA,EAClD;AAAA,EAEA,MAAM,yBAAyB;AAC9B,UAAM,sBAAK,wDAAL,WAAkB,MAAM,sBAAK,8DAAL;AAAA,EAC/B;AAAA,EAEA,MAAM,mBAAmB,aAA0B,SAA8C;AAChG,UAAM,EAAE,SAAS,SAAS,OAAO,IAAI,qBAIlC;AACH,UAAM,cAAc,MAAM,sBAAK,2DAAL,WAAqB;AAE/C,UAAM,UAAU,MAAM;AACrB,yBAAK,eAAc,QAAQ,MAAM;AAChC,cAAMC,WAAU,sBAAK,oDAAL,WAAc,aAAa,aAAa;AAExD,eAAOA,SAAQ,KAAK,SAAS,MAAM;AAAA,MACpC,CAAC;AAAA,IACF;AAEA,UAAM,YAAY,oBAAI,IAAY;AAElC,gBAAY,QAAQ,CAAC,aAAa;AACjC,YAAM,QAAQ,mBAAK,iBAAgB,IAAI,QAAQ;AAC/C,UAAI,OAAO;AACV,kBAAU,IAAI,QAAQ;AACtB,2BAAK,iBAAgB,IAAI,QAAQ,EAAG,KAAK,MAAM;AAC9C,oBAAU,OAAO,QAAQ;AACzB,cAAI,UAAU,SAAS,GAAG;AACzB,oBAAQ;AAAA,UACT;AAAA,QACD,CAAC;AAAA,MACF,OAAO;AACN,2BAAK,iBAAgB,IAAI,UAAU,CAAC,CAAC;AAAA,MACtC;AAAA,IACD,CAAC;AAED,QAAI,UAAU,SAAS,GAAG;AACzB,cAAQ;AAAA,IACT;AAEA,WAAO;AAAA,EACR;AAgTD;AA3YC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBM;AA8FA,oBAAe,eAAC,aAA0B;AAC/C,QAAM,cAAc,oBAAI,IAAY;AACpC,MAAI,aAAa;AAEjB,cAAY,uBAAuB,OAAO,WAAW,UAAU,SAAS;AACvE,UAAM,KAAK;AAEX,QAAI,YAAY;AACf;AAAA,IACD;AACA,iBAAa;AAEb,cAAU,OAAO,QAAQ,CAAC,UAAU;AACnC,UAAI,MAAM,QAAQ,kBAAkB,UAAU;AAC7C,oBAAY,IAAI,MAAM,OAAO,iBAAiB,QAAQ;AAAA,MACvD,WAAW,MAAM,QAAQ,WAAW,UAAU;AAC7C,oBAAY,IAAI,MAAM,OAAO,UAAU,QAAQ;AAAA,MAChD,WACC,MAAM,kBAAkB,YACxB,CAAC,MAAM,iBAAiB,sBACvB;AACD,oBAAY,IAAI,MAAM,iBAAiB,QAAQ;AAAA,MAChD;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AAED,QAAM,YAAY,wBAAwB,EAAE,QAAQ,mBAAK,SAAQ,CAAC;AAElE,SAAO;AACR;AAEM,aAAQ,eACb,aACA,aACA,SACC;AACD,MAAI;AACJ,MAAI;AACH,gBAAY,kBAAkB,mBAAK,SAAQ,aAAa,CAAC;AAEzD,UAAM,mBAAK,aAAY,QAAQ,YAAY;AAC1C,YAAM,OAAO,YAAY,QAAQ;AAEjC,UAAI,CAAC,KAAK,QAAQ,OAAO;AACxB,oBAAY,YAAY,MAAM,sBAAK,wDAAL,UAAmB;AAAA,MAClD;AAEA,kBAAY,qBAAqB,mBAAK,kBAAiB;AAEvD,YAAM,sBAAK,wDAAL;AACN,gBAAU,MAAM,sBAAK,uDAAL;AAChB,6BAAK,sBAAL;AACA,kBAAY,cAAc;AAAA,QACzB;AAAA,UACC,UAAU,QAAQ;AAAA,UAClB,SAAS,QAAQ;AAAA,UACjB,QAAQ,QAAQ;AAAA,QACjB;AAAA,MACD,CAAC;AAGD,YAAM,mBAAK,QAAO,iBAAiB,EAAE,aAAa,qBAAqB,KAAK,CAAC;AAAA,IAC9E,CAAC;AAED,UAAM,QAAQ,MAAM,YAAY,MAAM,EAAE,QAAQ,mBAAK,SAAQ,CAAC;AAE9D,UAAM,EAAE,UAAU,IAAI,MAAM,mBAAK,SAAQ,gBAAgB,KAAK;AAE9D,UAAM,UAAU,MAAM,mBAAK,QAAO,mBAAmB;AAAA,MACpD,aAAa;AAAA,MACb;AAAA,MACA,SAAS;AAAA,QACR,GAAG;AAAA,QACH,aAAa;AAAA,MACd;AAAA,IACD,CAAC;AAED,UAAM,eAAe,WAAW,KAAK,QAAQ,UAAW;AACxD,UAAM,UAAU,gBAAI,mBAAmB,MAAM,YAAY;AAEzD,UAAM,gBAAY,qCAAsB,OAAO;AAC/C,UAAM,UAAU,QAAQ,IAAI;AAE5B,QAAI,WAAW,WAAW,UAAU,UAAU,mBAAK,SAAQ,aAAa,GAAG;AAC1E,YAAM,YACL,OAAO,QAAQ,eAAe,IAC9B,OAAO,QAAQ,WAAW,IAC1B,OAAO,QAAQ,WAAW,IAC1B,OAAO,QAAQ,aAAa;AAE7B,UAAI,cAAc;AAClB,UAAI,8CAAuB,YAAY,QAAQ,CAAC,EAAE,aAAa,CAAC,QAAQ;AACvE,YAAI,IAAI,UAAU,WAAW;AAC5B,wBAAc;AAAA,QACf;AAEA,eAAO;AAAA,MACR,CAAC;AAED,UAAI,CAAC,eAAe,QAAQ,WAAW,mBAAK,sBAAqB;AAChE,2BAAK,WAAU,KAAK;AAAA,UACnB,IAAI,UAAU,IAAI;AAAA,UAClB,SAAS,UAAU,IAAI;AAAA,UACvB,QAAQ,UAAU,IAAI;AAAA,UACtB,SAAS,QAAQ,UAAU;AAAA,QAC5B,CAAC;AAAA,MACF,OAAO;AACN,YAAI,CAAC,mBAAK,eAAc;AACvB,6BAAK,cAAe,oBAAI,IAAI;AAAA,QAC7B;AACA,2BAAK,cAAa,IAAI,UAAU,IAAI,UAAU,UAAU,GAAG;AAAA,MAC5D;AAAA,IACD;AAEA,uBAAK,aAAc,QAAQ;AAE3B,WAAO;AAAA,MACN,QAAQ,QAAQ;AAAA,MAChB,aAAS,qBAAS,YAAY;AAAA,MAC9B,MAAM;AAAA,IACP;AAAA,EACD,SAAS,OAAO;AACf,QAAI,SAAS;AACZ,UAAI,CAAC,mBAAK,eAAc;AACvB,2BAAK,cAAe,oBAAI,IAAI;AAAA,MAC7B;AAEA,yBAAK,cAAa,IAAI,QAAQ,IAAI,IAAI;AAAA,IACvC;AAEA,UAAM,sBAAK,wDAAL,WAAkB,YAAY;AACnC,YAAM,QAAQ,IAAI;AAAA,QACjB,mBAAK,QAAO,MAAM,cAAc,CAAC,GAAG,WAAW,CAAC;AAAA,QAChD,sBAAK,8DAAL;AAAA,MACD,CAAC;AAAA,IACF;AAEA,UAAM;AAAA,EACP,UAAE;AACD,gBAAY,QAAQ,CAAC,aAAa;AACjC,YAAM,QAAQ,mBAAK,iBAAgB,IAAI,QAAQ;AAC/C,UAAI,SAAS,MAAM,SAAS,GAAG;AAC9B,cAAM,MAAM,EAAG;AAAA,MAChB,WAAW,OAAO;AACjB,2BAAK,iBAAgB,OAAO,QAAQ;AAAA,MACrC;AAAA,IACD,CAAC;AACD,2BAAK,sBAAL;AAAA,EACD;AACD;AAGM,iBAAY,eAAC,IAA0B;AAC5C,MAAI,mBAAK,aAAY;AACpB,UAAM,mBAAK;AAAA,EACZ;AAEA,qBAAK,YACJ,KAAK,EAAE;AAAA,IACN,MAAM;AACL,yBAAK,YAAa;AAAA,IACnB;AAAA,IACA,MAAM;AAAA,IAAC;AAAA,EACR,KAAK;AACP;AAEM,uBAAkB,iBAAG;AAC1B,QAAM,SAAS,mBAAK;AACpB,MAAI,QAAQ;AACX,uBAAK,aAAc;AACnB,UAAM,mBAAK,SAAQ,mBAAmB,EAAE,OAAO,CAAC;AAAA,EACjD;AACD;AAEM,gBAAW,iBAAG;AACnB,MAAI,mBAAK,WAAU,WAAW,KAAK,mBAAK,yBAAwB,mBAAK,eAAc;AAClF,UAAM,sBAAK,2DAAL;AAAA,EACP;AAEA,MAAI,mBAAK,WAAU,WAAW,GAAG;AAChC,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACrC;AAEA,QAAM,OAAO,mBAAK,WAAU,MAAM;AAClC,SAAO;AACR;AAEM,iBAAY,iBAAoB;AACrC,QAAM,YAAY,mBAAK,aACpB,mBAAK,WAAU,aAAa,mBAAK,wBAAuB,KAAK,IAAI,IACjE;AAEH,MAAI,YAAY,GAAG;AAClB,WAAO,mBAAK,WAAW;AAAA,EACxB;AAEA,MAAI,mBAAK,YAAW;AACnB,UAAM,kBAAkB,KAAK;AAAA,MAC5B,mBAAK,WAAU,aAAa,mBAAK,wBAAuB,KAAK,IAAI;AAAA,MACjE;AAAA,IACD;AAEA,UAAM,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,eAAe,CAAC;AAAA,EACpE;AAEA,QAAM,QAAQ,MAAM,mBAAK,SAAQ,wBAAwB;AAEzD,qBAAK,WAAY;AAAA,IAChB,OAAO,OAAO,MAAM,iBAAiB;AAAA,IACrC,YACC,OAAO,SAAS,MAAM,uBAAuB,EAAE,IAC/C,OAAO,SAAS,MAAM,iBAAiB,EAAE;AAAA,EAC3C;AAEA,SAAO,sBAAK,wDAAL;AACR;AAEM,oBAAe,iBAAG;AACvB,QAAM,YAAY,KAAK;AAAA,IACtB,mBAAK;AAAA,IACL,mBAAK,iBAAgB,mBAAK,WAAU,SAAS,mBAAK,yBAAwB;AAAA,EAC3E;AAEA,MAAI,cAAc,GAAG;AACpB;AAAA,EACD;AAEA,QAAM,MAAM,IAAI,+BAAY;AAC5B,QAAM,UAAU,mBAAK,SAAQ,aAAa;AAC1C,MAAI,UAAU,OAAO;AAErB,MAAI,mBAAK,eAAc;AACtB,UAAM,OAAO,CAAC;AACd,UAAM,MAAM,CAAC;AACb,eAAW,CAAC,IAAI,GAAG,KAAK,mBAAK,eAAc;AAC1C,UAAI,KAAK;AACR,aAAK,KAAK,GAAG;AAAA,MACd,OAAO;AACN,YAAI,KAAK,EAAE;AAAA,MACZ;AAAA,IACD;AAEA,QAAI,IAAI,SAAS,GAAG;AACnB,YAAM,QAAQ,MAAM,mBAAK,SAAQ,gBAAgB;AAAA,QAChD;AAAA,MACD,CAAC;AACD,WAAK;AAAA,QACJ,GAAG,MACD,OAAO,CAAC,SAAiD,KAAK,SAAS,IAAI,EAC3E,IAAI,CAAC,EAAE,KAAK,OAAO;AAAA,UACnB,UAAU,KAAK;AAAA,UACf,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,QACd,EAAE;AAAA,MACJ;AAAA,IACD;AAEA,QAAI,cAAc,IAAI;AACtB,uBAAK,cAAe,oBAAI,IAAI;AAAA,EAC7B;AAEA,QAAM,UAAU,IAAI,MAAM,SAAS,EAAE,KAAK,mBAAK,oBAAmB;AAClE,QAAM,UAAU,IAAI,WAAW,IAAI,KAAK,OAAO;AAC/C,QAAM,cAAc,CAAC;AACrB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAY,KAAK,QAAQ,CAAC,CAAC;AAAA,EAC5B;AACA,MAAI,gBAAgB,aAAa,OAAO;AAExC,QAAM,KAAK,uBAAuB;AAElC,QAAM,SAAS,MAAM,mBAAK,SAAQ,0BAA0B;AAAA,IAC3D,aAAa;AAAA,IACb,QAAQ,mBAAK;AAAA,IACb,SAAS;AAAA,MACR,gBAAgB;AAAA,IACjB;AAAA,EACD,CAAC;AAED,QAAM,UAAU,gBAAI,mBAAmB,MAAM,WAAW,KAAK,OAAO,UAAW,CAAC;AAChF,UAAQ,IAAI,eAAe,QAAQ,CAAC,CAAC,IAAI,EAAE,YAAY,CAAC,GAAG,MAAM;AAChE,QAAI,MAAM,QAAQ,IAAI,kBAAkB,CAAC,YAAY,aAAa;AACjE;AAAA,IACD;AAEA,uBAAK,WAAU,KAAK;AAAA,MACnB;AAAA,MACA,SAAS,QAAQ,GAAI;AAAA,MACrB,QAAQ,YAAY,YAAY,CAAC;AAAA,MACjC,SAAS,OAAO,mBAAK,oBAAmB;AAAA,IACzC,CAAC;AAAA,EACF,CAAC;AAED,MAAI,CAAC,mBAAK,eAAc;AACvB,uBAAK,cAAe,oBAAI,IAAI;AAAA,EAC7B;AAEA,QAAM,gBAAY,qCAAsB,OAAO,EAAE;AACjD,qBAAK,cAAc,IAAI,UAAU,UAAU,SAAS;AAEpD,QAAM,mBAAK,SAAQ,mBAAmB,EAAE,QAAQ,OAAO,OAAO,CAAC;AAChE;AAGD,SAAS,uBAA0B;AAClC,MAAI;AACJ,MAAI;AAEJ,QAAM,UAAU,IAAI,QAAW,CAAC,UAAU,YAAY;AACrD,cAAU;AACV,aAAS;AAAA,EACV,CAAC;AAED,SAAO,EAAE,SAAS,SAAmB,OAAgB;AACtD;", "names": ["import_bcs", "promise"]}