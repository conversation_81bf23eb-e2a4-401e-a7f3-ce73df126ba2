{"version": 3, "sources": ["../../../../src/transactions/data/internal.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { EnumInputShape, EnumOutputShape } from '@mysten/bcs';\nimport type { GenericSchema, InferInput, InferOutput } from 'valibot';\nimport {\n\tarray,\n\tboolean,\n\tcheck,\n\tinteger,\n\tlazy,\n\tliteral,\n\tnullable,\n\tnullish,\n\tnumber,\n\tobject,\n\toptional,\n\tpipe,\n\trecord,\n\tstring,\n\ttransform,\n\ttuple,\n\tunion,\n\tunknown,\n} from 'valibot';\n\nimport { isValidSuiAddress, normalizeSuiAddress } from '../../utils/sui-types.js';\n\ntype Merge<T> = T extends object ? { [K in keyof T]: T[K] } : never;\n\ntype EnumSchema<T extends Record<string, GenericSchema<any>>> = GenericSchema<\n\tEnumInputShape<\n\t\tMerge<{\n\t\t\t[K in keyof T]: InferInput<T[K]>;\n\t\t}>\n\t>,\n\tEnumOutputShape<\n\t\tMerge<{\n\t\t\t[K in keyof T]: InferOutput<T[K]>;\n\t\t}>\n\t>\n>;\n\nexport function safeEnum<T extends Record<string, GenericSchema<any>>>(options: T): EnumSchema<T> {\n\tconst unionOptions = Object.entries(options).map(([key, value]) => object({ [key]: value }));\n\n\treturn pipe(\n\t\tunion(unionOptions),\n\t\ttransform((value) => ({\n\t\t\t...value,\n\t\t\t$kind: Object.keys(value)[0] as keyof typeof value,\n\t\t})),\n\t) as EnumSchema<T>;\n}\n\nexport const SuiAddress = pipe(\n\tstring(),\n\ttransform((value) => normalizeSuiAddress(value)),\n\tcheck(isValidSuiAddress),\n);\nexport const ObjectID = SuiAddress;\nexport const BCSBytes = string();\nexport const JsonU64 = pipe(\n\tunion([string(), pipe(number(), integer())]),\n\n\tcheck((val) => {\n\t\ttry {\n\t\t\tBigInt(val);\n\t\t\treturn BigInt(val) >= 0 && BigInt(val) <= 18446744073709551615n;\n\t\t} catch {\n\t\t\treturn false;\n\t\t}\n\t}, 'Invalid u64'),\n);\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/base_types.rs#L138\n// Implemented as a tuple in rust\nexport const ObjectRef = object({\n\tobjectId: SuiAddress,\n\tversion: JsonU64,\n\tdigest: string(),\n});\nexport type ObjectRef = InferOutput<typeof ObjectRef>;\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L690-L702\nexport const Argument = pipe(\n\tunion([\n\t\tobject({ GasCoin: literal(true) }),\n\t\tobject({ Input: pipe(number(), integer()), type: optional(literal('pure')) }),\n\t\tobject({ Input: pipe(number(), integer()), type: optional(literal('object')) }),\n\t\tobject({ Result: pipe(number(), integer()) }),\n\t\tobject({ NestedResult: tuple([pipe(number(), integer()), pipe(number(), integer())]) }),\n\t]),\n\ttransform((value) => ({\n\t\t...value,\n\t\t$kind: Object.keys(value)[0] as keyof typeof value,\n\t})),\n\t// Defined manually to add `type?: 'pure' | 'object'` to Input\n) as GenericSchema<\n\t// Input\n\t| { GasCoin: true }\n\t| { Input: number; type?: 'pure' | 'object' }\n\t| { Result: number }\n\t| { NestedResult: [number, number] },\n\t// Output\n\t| { $kind: 'GasCoin'; GasCoin: true }\n\t| { $kind: 'Input'; Input: number; type?: 'pure' }\n\t| { $kind: 'Input'; Input: number; type?: 'object' }\n\t| { $kind: 'Result'; Result: number }\n\t| { $kind: 'NestedResult'; NestedResult: [number, number] }\n>;\n\nexport type Argument = InferOutput<typeof Argument>;\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L1387-L1392\nexport const GasData = object({\n\tbudget: nullable(JsonU64),\n\tprice: nullable(JsonU64),\n\towner: nullable(SuiAddress),\n\tpayment: nullable(array(ObjectRef)),\n});\nexport type GasData = InferOutput<typeof GasData>;\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/external-crates/move/crates/move-core-types/src/language_storage.rs#L140-L147\nexport const StructTag = object({\n\taddress: string(),\n\tmodule: string(),\n\tname: string(),\n\t// type_params in rust, should be updated to use camelCase\n\ttypeParams: array(string()),\n});\nexport type StructTag = InferOutput<typeof StructTag>;\n\n// https://github.com/MystenLabs/sui/blob/cea8742e810142a8145fd83c4c142d61e561004a/crates/sui-graphql-rpc/schema/current_progress_schema.graphql#L1614-L1627\nexport type OpenMoveTypeSignatureBody =\n\t| 'address'\n\t| 'bool'\n\t| 'u8'\n\t| 'u16'\n\t| 'u32'\n\t| 'u64'\n\t| 'u128'\n\t| 'u256'\n\t| { vector: OpenMoveTypeSignatureBody }\n\t| {\n\t\t\tdatatype: {\n\t\t\t\tpackage: string;\n\t\t\t\tmodule: string;\n\t\t\t\ttype: string;\n\t\t\t\ttypeParameters: OpenMoveTypeSignatureBody[];\n\t\t\t};\n\t  }\n\t| { typeParameter: number };\n\nexport const OpenMoveTypeSignatureBody: GenericSchema<OpenMoveTypeSignatureBody> = union([\n\tliteral('address'),\n\tliteral('bool'),\n\tliteral('u8'),\n\tliteral('u16'),\n\tliteral('u32'),\n\tliteral('u64'),\n\tliteral('u128'),\n\tliteral('u256'),\n\tobject({ vector: lazy(() => OpenMoveTypeSignatureBody) }),\n\tobject({\n\t\tdatatype: object({\n\t\t\tpackage: string(),\n\t\t\tmodule: string(),\n\t\t\ttype: string(),\n\t\t\ttypeParameters: array(lazy(() => OpenMoveTypeSignatureBody)),\n\t\t}),\n\t}),\n\tobject({ typeParameter: pipe(number(), integer()) }),\n]);\n\n// https://github.com/MystenLabs/sui/blob/cea8742e810142a8145fd83c4c142d61e561004a/crates/sui-graphql-rpc/schema/current_progress_schema.graphql#L1609-L1612\nexport const OpenMoveTypeSignature = object({\n\tref: nullable(union([literal('&'), literal('&mut')])),\n\tbody: OpenMoveTypeSignatureBody,\n});\nexport type OpenMoveTypeSignature = InferOutput<typeof OpenMoveTypeSignature>;\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L707-L718\nconst ProgrammableMoveCall = object({\n\tpackage: ObjectID,\n\tmodule: string(),\n\tfunction: string(),\n\t// snake case in rust\n\ttypeArguments: array(string()),\n\targuments: array(Argument),\n\t_argumentTypes: optional(nullable(array(OpenMoveTypeSignature))),\n});\nexport type ProgrammableMoveCall = InferOutput<typeof ProgrammableMoveCall>;\n\nexport const $Intent = object({\n\tname: string(),\n\tinputs: record(string(), union([Argument, array(Argument)])),\n\tdata: record(string(), unknown()),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L657-L685\nexport const Command = safeEnum({\n\tMoveCall: ProgrammableMoveCall,\n\tTransferObjects: object({\n\t\tobjects: array(Argument),\n\t\taddress: Argument,\n\t}),\n\tSplitCoins: object({\n\t\tcoin: Argument,\n\t\tamounts: array(Argument),\n\t}),\n\tMergeCoins: object({\n\t\tdestination: Argument,\n\t\tsources: array(Argument),\n\t}),\n\tPublish: object({\n\t\tmodules: array(BCSBytes),\n\t\tdependencies: array(ObjectID),\n\t}),\n\tMakeMoveVec: object({\n\t\ttype: nullable(string()),\n\t\telements: array(Argument),\n\t}),\n\tUpgrade: object({\n\t\tmodules: array(BCSBytes),\n\t\tdependencies: array(ObjectID),\n\t\tpackage: ObjectID,\n\t\tticket: Argument,\n\t}),\n\t$Intent,\n});\n\nexport type Command<Arg = Argument> = EnumOutputShape<{\n\tMoveCall: {\n\t\tpackage: string;\n\t\tmodule: string;\n\t\tfunction: string;\n\t\ttypeArguments: string[];\n\t\targuments: Arg[];\n\t\t_argumentTypes?: OpenMoveTypeSignature[] | null;\n\t};\n\tTransferObjects: {\n\t\tobjects: Arg[];\n\t\taddress: Arg;\n\t};\n\tSplitCoins: {\n\t\tcoin: Arg;\n\t\tamounts: Arg[];\n\t};\n\tMergeCoins: {\n\t\tdestination: Arg;\n\t\tsources: Arg[];\n\t};\n\tPublish: {\n\t\tmodules: string[];\n\t\tdependencies: string[];\n\t};\n\tMakeMoveVec: {\n\t\ttype: string | null;\n\t\telements: Arg[];\n\t};\n\tUpgrade: {\n\t\tmodules: string[];\n\t\tdependencies: string[];\n\t\tpackage: string;\n\t\tticket: Arg;\n\t};\n\t$Intent: {\n\t\tname: string;\n\t\tinputs: Record<string, Argument | Argument[]>;\n\t\tdata: Record<string, unknown>;\n\t};\n}>;\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L102-L114\nexport const ObjectArg = safeEnum({\n\tImmOrOwnedObject: ObjectRef,\n\tSharedObject: object({\n\t\tobjectId: ObjectID,\n\t\t// snake case in rust\n\t\tinitialSharedVersion: JsonU64,\n\t\tmutable: boolean(),\n\t}),\n\tReceiving: ObjectRef,\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L75-L80\nconst CallArg = safeEnum({\n\tObject: ObjectArg,\n\tPure: object({\n\t\tbytes: BCSBytes,\n\t}),\n\tUnresolvedPure: object({\n\t\tvalue: unknown(),\n\t}),\n\tUnresolvedObject: object({\n\t\tobjectId: ObjectID,\n\t\tversion: optional(nullable(JsonU64)),\n\t\tdigest: optional(nullable(string())),\n\t\tinitialSharedVersion: optional(nullable(JsonU64)),\n\t}),\n});\nexport type CallArg = InferOutput<typeof CallArg>;\n\nexport const NormalizedCallArg = safeEnum({\n\tObject: ObjectArg,\n\tPure: object({\n\t\tbytes: BCSBytes,\n\t}),\n});\n\nexport const TransactionExpiration = safeEnum({\n\tNone: literal(true),\n\tEpoch: JsonU64,\n});\n\nexport type TransactionExpiration = InferOutput<typeof TransactionExpiration>;\n\nexport const TransactionData = object({\n\tversion: literal(2),\n\tsender: nullish(SuiAddress),\n\texpiration: nullish(TransactionExpiration),\n\tgasData: GasData,\n\tinputs: array(CallArg),\n\tcommands: array(Command),\n});\nexport type TransactionData = InferOutput<typeof TransactionData>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,qBAmBO;AAEP,uBAAuD;AAiBhD,SAAS,SAAuD,SAA2B;AACjG,QAAM,eAAe,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,UAAM,uBAAO,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;AAE3F,aAAO;AAAA,QACN,sBAAM,YAAY;AAAA,QAClB,0BAAU,CAAC,WAAW;AAAA,MACrB,GAAG;AAAA,MACH,OAAO,OAAO,KAAK,KAAK,EAAE,CAAC;AAAA,IAC5B,EAAE;AAAA,EACH;AACD;AAEO,MAAM,iBAAa;AAAA,MACzB,uBAAO;AAAA,MACP,0BAAU,CAAC,cAAU,sCAAoB,KAAK,CAAC;AAAA,MAC/C,sBAAM,kCAAiB;AACxB;AACO,MAAM,WAAW;AACjB,MAAM,eAAW,uBAAO;AACxB,MAAM,cAAU;AAAA,MACtB,sBAAM,KAAC,uBAAO,OAAG,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,CAAC,CAAC;AAAA,MAE3C,sBAAM,CAAC,QAAQ;AACd,QAAI;AACH,aAAO,GAAG;AACV,aAAO,OAAO,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK;AAAA,IAC3C,QAAQ;AACP,aAAO;AAAA,IACR;AAAA,EACD,GAAG,aAAa;AACjB;AAGO,MAAM,gBAAY,uBAAO;AAAA,EAC/B,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAQ,uBAAO;AAChB,CAAC;AAIM,MAAM,eAAW;AAAA,MACvB,sBAAM;AAAA,QACL,uBAAO,EAAE,aAAS,wBAAQ,IAAI,EAAE,CAAC;AAAA,QACjC,uBAAO,EAAE,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,GAAG,UAAM,6BAAS,wBAAQ,MAAM,CAAC,EAAE,CAAC;AAAA,QAC5E,uBAAO,EAAE,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,GAAG,UAAM,6BAAS,wBAAQ,QAAQ,CAAC,EAAE,CAAC;AAAA,QAC9E,uBAAO,EAAE,YAAQ,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,EAAE,CAAC;AAAA,QAC5C,uBAAO,EAAE,kBAAc,sBAAM,KAAC,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,OAAG,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;AAAA,EACvF,CAAC;AAAA,MACD,0BAAU,CAAC,WAAW;AAAA,IACrB,GAAG;AAAA,IACH,OAAO,OAAO,KAAK,KAAK,EAAE,CAAC;AAAA,EAC5B,EAAE;AAAA;AAEH;AAiBO,MAAM,cAAU,uBAAO;AAAA,EAC7B,YAAQ,yBAAS,OAAO;AAAA,EACxB,WAAO,yBAAS,OAAO;AAAA,EACvB,WAAO,yBAAS,UAAU;AAAA,EAC1B,aAAS,6BAAS,sBAAM,SAAS,CAAC;AACnC,CAAC;AAIM,MAAM,gBAAY,uBAAO;AAAA,EAC/B,aAAS,uBAAO;AAAA,EAChB,YAAQ,uBAAO;AAAA,EACf,UAAM,uBAAO;AAAA;AAAA,EAEb,gBAAY,0BAAM,uBAAO,CAAC;AAC3B,CAAC;AAwBM,MAAM,gCAAsE,sBAAM;AAAA,MACxF,wBAAQ,SAAS;AAAA,MACjB,wBAAQ,MAAM;AAAA,MACd,wBAAQ,IAAI;AAAA,MACZ,wBAAQ,KAAK;AAAA,MACb,wBAAQ,KAAK;AAAA,MACb,wBAAQ,KAAK;AAAA,MACb,wBAAQ,MAAM;AAAA,MACd,wBAAQ,MAAM;AAAA,MACd,uBAAO,EAAE,YAAQ,qBAAK,MAAM,yBAAyB,EAAE,CAAC;AAAA,MACxD,uBAAO;AAAA,IACN,cAAU,uBAAO;AAAA,MAChB,aAAS,uBAAO;AAAA,MAChB,YAAQ,uBAAO;AAAA,MACf,UAAM,uBAAO;AAAA,MACb,oBAAgB,0BAAM,qBAAK,MAAM,yBAAyB,CAAC;AAAA,IAC5D,CAAC;AAAA,EACF,CAAC;AAAA,MACD,uBAAO,EAAE,mBAAe,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,EAAE,CAAC;AACpD,CAAC;AAGM,MAAM,4BAAwB,uBAAO;AAAA,EAC3C,SAAK,6BAAS,sBAAM,KAAC,wBAAQ,GAAG,OAAG,wBAAQ,MAAM,CAAC,CAAC,CAAC;AAAA,EACpD,MAAM;AACP,CAAC;AAID,MAAM,2BAAuB,uBAAO;AAAA,EACnC,SAAS;AAAA,EACT,YAAQ,uBAAO;AAAA,EACf,cAAU,uBAAO;AAAA;AAAA,EAEjB,mBAAe,0BAAM,uBAAO,CAAC;AAAA,EAC7B,eAAW,sBAAM,QAAQ;AAAA,EACzB,oBAAgB,6BAAS,6BAAS,sBAAM,qBAAqB,CAAC,CAAC;AAChE,CAAC;AAGM,MAAM,cAAU,uBAAO;AAAA,EAC7B,UAAM,uBAAO;AAAA,EACb,YAAQ,2BAAO,uBAAO,OAAG,sBAAM,CAAC,cAAU,sBAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3D,UAAM,2BAAO,uBAAO,OAAG,wBAAQ,CAAC;AACjC,CAAC;AAGM,MAAM,UAAU,SAAS;AAAA,EAC/B,UAAU;AAAA,EACV,qBAAiB,uBAAO;AAAA,IACvB,aAAS,sBAAM,QAAQ;AAAA,IACvB,SAAS;AAAA,EACV,CAAC;AAAA,EACD,gBAAY,uBAAO;AAAA,IAClB,MAAM;AAAA,IACN,aAAS,sBAAM,QAAQ;AAAA,EACxB,CAAC;AAAA,EACD,gBAAY,uBAAO;AAAA,IAClB,aAAa;AAAA,IACb,aAAS,sBAAM,QAAQ;AAAA,EACxB,CAAC;AAAA,EACD,aAAS,uBAAO;AAAA,IACf,aAAS,sBAAM,QAAQ;AAAA,IACvB,kBAAc,sBAAM,QAAQ;AAAA,EAC7B,CAAC;AAAA,EACD,iBAAa,uBAAO;AAAA,IACnB,UAAM,6BAAS,uBAAO,CAAC;AAAA,IACvB,cAAU,sBAAM,QAAQ;AAAA,EACzB,CAAC;AAAA,EACD,aAAS,uBAAO;AAAA,IACf,aAAS,sBAAM,QAAQ;AAAA,IACvB,kBAAc,sBAAM,QAAQ;AAAA,IAC5B,SAAS;AAAA,IACT,QAAQ;AAAA,EACT,CAAC;AAAA,EACD;AACD,CAAC;AA6CM,MAAM,YAAY,SAAS;AAAA,EACjC,kBAAkB;AAAA,EAClB,kBAAc,uBAAO;AAAA,IACpB,UAAU;AAAA;AAAA,IAEV,sBAAsB;AAAA,IACtB,aAAS,wBAAQ;AAAA,EAClB,CAAC;AAAA,EACD,WAAW;AACZ,CAAC;AAGD,MAAM,UAAU,SAAS;AAAA,EACxB,QAAQ;AAAA,EACR,UAAM,uBAAO;AAAA,IACZ,OAAO;AAAA,EACR,CAAC;AAAA,EACD,oBAAgB,uBAAO;AAAA,IACtB,WAAO,wBAAQ;AAAA,EAChB,CAAC;AAAA,EACD,sBAAkB,uBAAO;AAAA,IACxB,UAAU;AAAA,IACV,aAAS,6BAAS,yBAAS,OAAO,CAAC;AAAA,IACnC,YAAQ,6BAAS,6BAAS,uBAAO,CAAC,CAAC;AAAA,IACnC,0BAAsB,6BAAS,yBAAS,OAAO,CAAC;AAAA,EACjD,CAAC;AACF,CAAC;AAGM,MAAM,oBAAoB,SAAS;AAAA,EACzC,QAAQ;AAAA,EACR,UAAM,uBAAO;AAAA,IACZ,OAAO;AAAA,EACR,CAAC;AACF,CAAC;AAEM,MAAM,wBAAwB,SAAS;AAAA,EAC7C,UAAM,wBAAQ,IAAI;AAAA,EAClB,OAAO;AACR,CAAC;AAIM,MAAM,sBAAkB,uBAAO;AAAA,EACrC,aAAS,wBAAQ,CAAC;AAAA,EAClB,YAAQ,wBAAQ,UAAU;AAAA,EAC1B,gBAAY,wBAAQ,qBAAqB;AAAA,EACzC,SAAS;AAAA,EACT,YAAQ,sBAAM,OAAO;AAAA,EACrB,cAAU,sBAAM,OAAO;AACxB,CAAC;", "names": []}