import type { Transaction, TransactionObjectInput } from './Transaction.js';
export declare const Arguments: {
    pure: {
        <T extends import("./pure.js").PureTypeName>(type: T extends import("./pure.js").PureTypeName ? import("./pure.js").ValidPureTypeName<T> : T, value: T extends import("./pure.js").BasePureType ? Parameters<{
            <T_1 extends import("./pure.js").PureTypeName>(type: T_1 extends import("./pure.js").PureTypeName ? import("./pure.js").ValidPureTypeName<T_1> : T_1, value: T_1 extends import("./pure.js").BasePureType ? Parameters<any[T_1]>[0] : T_1 extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : T_1 extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never): T_1;
            (value: import("@mysten/bcs").SerializedBcs<any, any> | Uint8Array): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u8(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u16(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u32(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u64(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u128(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u256(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            bool(value: boolean): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            string(value: string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            address(value: string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            id: (value: string) => {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            vector<Type extends import("./pure.js").PureTypeName>(type: Type, value: Iterable<Type extends import("./pure.js").BasePureType ? Parameters<any[Type]>[0] : Type extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never> & {
                length: number;
            }): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            option<Type extends import("./pure.js").PureTypeName>(type: Type, value: (Type extends import("./pure.js").BasePureType ? Parameters<any[Type]>[0] : Type extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never) | null | undefined): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
        }[T]>[0] : T extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : T extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never): T;
        (value: import("@mysten/bcs").SerializedBcs<any, any> | Uint8Array): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        u8(value: number): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        u16(value: number): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        u32(value: number): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        u64(value: bigint | number | string): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        u128(value: bigint | number | string): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        u256(value: bigint | number | string): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        bool(value: boolean): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        string(value: string): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        address(value: string): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        id: (value: string) => (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        vector<Type extends import("./pure.js").PureTypeName>(type: Type, value: Iterable<Type extends import("./pure.js").BasePureType ? Parameters<{
            <T extends import("./pure.js").PureTypeName>(type: T extends import("./pure.js").PureTypeName ? import("./pure.js").ValidPureTypeName<T> : T, value: T extends import("./pure.js").BasePureType ? Parameters<any[T]>[0] : T extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : T extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never): T;
            (value: import("@mysten/bcs").SerializedBcs<any, any> | Uint8Array): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u8(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u16(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u32(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u64(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u128(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u256(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            bool(value: boolean): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            string(value: string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            address(value: string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            id: (value: string) => {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            vector<Type_1 extends import("./pure.js").PureTypeName>(type: Type_1, value: Iterable<Type_1 extends import("./pure.js").BasePureType ? Parameters<any[Type_1]>[0] : Type_1 extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type_1 extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never> & {
                length: number;
            }): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            option<Type_1 extends import("./pure.js").PureTypeName>(type: Type_1, value: (Type_1 extends import("./pure.js").BasePureType ? Parameters<any[Type_1]>[0] : Type_1 extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type_1 extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never) | null | undefined): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
        }[Type]>[0] : Type extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never> & {
            length: number;
        }): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
        option<Type extends import("./pure.js").PureTypeName>(type: Type, value: (Type extends import("./pure.js").BasePureType ? Parameters<{
            <T extends import("./pure.js").PureTypeName>(type: T extends import("./pure.js").PureTypeName ? import("./pure.js").ValidPureTypeName<T> : T, value: T extends import("./pure.js").BasePureType ? Parameters<any[T]>[0] : T extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : T extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never): T;
            (value: import("@mysten/bcs").SerializedBcs<any, any> | Uint8Array): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u8(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u16(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u32(value: number): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u64(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u128(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            u256(value: bigint | number | string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            bool(value: boolean): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            string(value: string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            address(value: string): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            id: (value: string) => {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            vector<Type_1 extends import("./pure.js").PureTypeName>(type: Type_1, value: Iterable<Type_1 extends import("./pure.js").BasePureType ? Parameters<any[Type_1]>[0] : Type_1 extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type_1 extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never> & {
                length: number;
            }): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
            option<Type_1 extends import("./pure.js").PureTypeName>(type: Type_1, value: (Type_1 extends import("./pure.js").BasePureType ? Parameters<any[Type_1]>[0] : Type_1 extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type_1 extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never) | null | undefined): {
                $kind: "GasCoin";
                GasCoin: true;
            } | {
                $kind: "Input";
                Input: number;
                type?: "pure";
            } | {
                $kind: "Input";
                Input: number;
                type?: "object";
            } | {
                $kind: "Result";
                Result: number;
            } | {
                $kind: "NestedResult";
                NestedResult: [number, number];
            };
        }[Type]>[0] : Type extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? (U extends import("./pure.js").BasePureType ? Parameters<any[U]>[0] : U extends `vector<${infer U extends import("./pure.js").PureTypeName}>` ? any[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : U extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never)[] : Type extends `option<${infer U_1 extends import("./pure.js").PureTypeName}>` ? any | null : never) | null | undefined): (tx: Transaction) => {
            $kind: "GasCoin";
            GasCoin: true;
        } | {
            $kind: "Input";
            Input: number;
            type?: "pure";
        } | {
            $kind: "Input";
            Input: number;
            type?: "object";
        } | {
            $kind: "Result";
            Result: number;
        } | {
            $kind: "NestedResult";
            NestedResult: [number, number];
        };
    };
    object: {
        (value: TransactionObjectInput): (tx: Transaction) => {
            $kind: "Input";
            Input: number;
            type?: "object";
        };
        system(): (tx: Transaction) => {
            $kind: "Input";
            Input: number;
            type?: "object";
        };
        clock(): (tx: Transaction) => {
            $kind: "Input";
            Input: number;
            type?: "object";
        };
        random(): (tx: Transaction) => {
            $kind: "Input";
            Input: number;
            type?: "object";
        };
        denyList(): (tx: Transaction) => {
            $kind: "Input";
            Input: number;
            type?: "object";
        };
        option({ type, value }: {
            type: string;
            value: TransactionObjectInput | null;
        }): (tx: Transaction) => import("./Transaction.js").TransactionResult;
    };
    sharedObjectRef: (args_0: {
        objectId: string;
        mutable: boolean;
        initialSharedVersion: number | string;
    }) => (tx: Transaction) => {
        $kind: "Input";
        Input: number;
        type?: "object";
    };
    objectRef: (args_0: {
        objectId: string;
        version: string | number;
        digest: string;
    }) => (tx: Transaction) => {
        $kind: "Input";
        Input: number;
        type?: "object";
    };
    receivingRef: (args_0: {
        objectId: string;
        version: string | number;
        digest: string;
    }) => (tx: Transaction) => {
        $kind: "Input";
        Input: number;
        type?: "object";
    };
};
