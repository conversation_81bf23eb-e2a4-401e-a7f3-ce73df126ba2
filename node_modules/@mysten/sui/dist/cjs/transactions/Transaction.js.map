{"version": 3, "sources": ["../../../src/transactions/Transaction.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SerializedBcs } from '@mysten/bcs';\nimport { fromBase64, isSerializedBcs } from '@mysten/bcs';\nimport type { InferInput } from 'valibot';\nimport { is, parse } from 'valibot';\n\nimport type { SuiClient } from '../client/index.js';\nimport type { SignatureWithBytes, Signer } from '../cryptography/index.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { TransactionArgument } from './Commands.js';\nimport { Commands } from './Commands.js';\nimport type { CallArg, Command } from './data/internal.js';\nimport { Argument, NormalizedCallArg, ObjectRef, TransactionExpiration } from './data/internal.js';\nimport { serializeV1TransactionData } from './data/v1.js';\nimport { SerializedTransactionDataV2 } from './data/v2.js';\nimport { Inputs } from './Inputs.js';\nimport type {\n\tBuildTransactionOptions,\n\tSerializeTransactionOptions,\n\tTransactionPlugin,\n} from './json-rpc-resolver.js';\nimport { resolveTransactionData } from './json-rpc-resolver.js';\nimport { createObjectMethods } from './object.js';\nimport { createPure } from './pure.js';\nimport { TransactionDataBuilder } from './TransactionData.js';\nimport { getIdFromCallArg } from './utils.js';\n\nexport type TransactionObjectArgument =\n\t| Exclude<InferInput<typeof Argument>, { Input: unknown; type?: 'pure' }>\n\t| ((tx: Transaction) => Exclude<InferInput<typeof Argument>, { Input: unknown; type?: 'pure' }>);\n\nexport type TransactionResult = Extract<Argument, { Result: unknown }> &\n\tExtract<Argument, { NestedResult: unknown }>[];\n\nfunction createTransactionResult(index: number, length = Infinity): TransactionResult {\n\tconst baseResult = { $kind: 'Result' as const, Result: index };\n\n\tconst nestedResults: {\n\t\t$kind: 'NestedResult';\n\t\tNestedResult: [number, number];\n\t}[] = [];\n\tconst nestedResultFor = (\n\t\tresultIndex: number,\n\t): {\n\t\t$kind: 'NestedResult';\n\t\tNestedResult: [number, number];\n\t} =>\n\t\t(nestedResults[resultIndex] ??= {\n\t\t\t$kind: 'NestedResult' as const,\n\t\t\tNestedResult: [index, resultIndex],\n\t\t});\n\n\treturn new Proxy(baseResult, {\n\t\tset() {\n\t\t\tthrow new Error(\n\t\t\t\t'The transaction result is a proxy, and does not support setting properties directly',\n\t\t\t);\n\t\t},\n\t\t// TODO: Instead of making this return a concrete argument, we should ideally\n\t\t// make it reference-based (so that this gets resolved at build-time), which\n\t\t// allows re-ordering transactions.\n\t\tget(target, property) {\n\t\t\t// This allows this transaction argument to be used in the singular form:\n\t\t\tif (property in target) {\n\t\t\t\treturn Reflect.get(target, property);\n\t\t\t}\n\n\t\t\t// Support destructuring:\n\t\t\tif (property === Symbol.iterator) {\n\t\t\t\treturn function* () {\n\t\t\t\t\tlet i = 0;\n\t\t\t\t\twhile (i < length) {\n\t\t\t\t\t\tyield nestedResultFor(i);\n\t\t\t\t\t\ti++;\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (typeof property === 'symbol') return;\n\n\t\t\tconst resultIndex = parseInt(property, 10);\n\t\t\tif (Number.isNaN(resultIndex) || resultIndex < 0) return;\n\t\t\treturn nestedResultFor(resultIndex);\n\t\t},\n\t}) as TransactionResult;\n}\n\nconst TRANSACTION_BRAND = Symbol.for('@mysten/transaction') as never;\n\ninterface SignOptions extends BuildTransactionOptions {\n\tsigner: Signer;\n}\n\nexport function isTransaction(obj: unknown): obj is Transaction {\n\treturn !!obj && typeof obj === 'object' && (obj as any)[TRANSACTION_BRAND] === true;\n}\n\nexport type TransactionObjectInput = string | CallArg | TransactionObjectArgument;\n\ninterface TransactionPluginRegistry {\n\t// eslint-disable-next-line @typescript-eslint/ban-types\n\tbuildPlugins: Map<string | Function, TransactionPlugin>;\n\t// eslint-disable-next-line @typescript-eslint/ban-types\n\tserializationPlugins: Map<string | Function, TransactionPlugin>;\n}\n\nconst modulePluginRegistry: TransactionPluginRegistry = {\n\tbuildPlugins: new Map(),\n\tserializationPlugins: new Map(),\n};\n\nconst TRANSACTION_REGISTRY_KEY = Symbol.for('@mysten/transaction/registry');\nfunction getGlobalPluginRegistry() {\n\ttry {\n\t\tconst target = globalThis as {\n\t\t\t[TRANSACTION_REGISTRY_KEY]?: TransactionPluginRegistry;\n\t\t};\n\n\t\tif (!target[TRANSACTION_REGISTRY_KEY]) {\n\t\t\ttarget[TRANSACTION_REGISTRY_KEY] = modulePluginRegistry;\n\t\t}\n\n\t\treturn target[TRANSACTION_REGISTRY_KEY];\n\t} catch (e) {\n\t\treturn modulePluginRegistry;\n\t}\n}\n\n/**\n * Transaction Builder\n */\nexport class Transaction {\n\t#serializationPlugins: TransactionPlugin[];\n\t#buildPlugins: TransactionPlugin[];\n\t#intentResolvers = new Map<string, TransactionPlugin>();\n\n\t/**\n\t * Converts from a serialize transaction kind (built with `build({ onlyTransactionKind: true })`) to a `Transaction` class.\n\t * Supports either a byte array, or base64-encoded bytes.\n\t */\n\tstatic fromKind(serialized: string | Uint8Array) {\n\t\tconst tx = new Transaction();\n\n\t\ttx.#data = TransactionDataBuilder.fromKindBytes(\n\t\t\ttypeof serialized === 'string' ? fromBase64(serialized) : serialized,\n\t\t);\n\n\t\treturn tx;\n\t}\n\n\t/**\n\t * Converts from a serialized transaction format to a `Transaction` class.\n\t * There are two supported serialized formats:\n\t * - A string returned from `Transaction#serialize`. The serialized format must be compatible, or it will throw an error.\n\t * - A byte array (or base64-encoded bytes) containing BCS transaction data.\n\t */\n\tstatic from(transaction: string | Uint8Array | Transaction) {\n\t\tconst newTransaction = new Transaction();\n\n\t\tif (isTransaction(transaction)) {\n\t\t\tnewTransaction.#data = new TransactionDataBuilder(transaction.getData());\n\t\t} else if (typeof transaction !== 'string' || !transaction.startsWith('{')) {\n\t\t\tnewTransaction.#data = TransactionDataBuilder.fromBytes(\n\t\t\t\ttypeof transaction === 'string' ? fromBase64(transaction) : transaction,\n\t\t\t);\n\t\t} else {\n\t\t\tnewTransaction.#data = TransactionDataBuilder.restore(JSON.parse(transaction));\n\t\t}\n\n\t\treturn newTransaction;\n\t}\n\n\t/** @deprecated global plugins should be registered with a name */\n\tstatic registerGlobalSerializationPlugin(step: TransactionPlugin): void;\n\tstatic registerGlobalSerializationPlugin(name: string, step: TransactionPlugin): void;\n\tstatic registerGlobalSerializationPlugin(\n\t\tstepOrStep: TransactionPlugin | string,\n\t\tstep?: TransactionPlugin,\n\t) {\n\t\tgetGlobalPluginRegistry().serializationPlugins.set(\n\t\t\tstepOrStep,\n\t\t\tstep ?? (stepOrStep as TransactionPlugin),\n\t\t);\n\t}\n\n\tstatic unregisterGlobalSerializationPlugin(name: string) {\n\t\tgetGlobalPluginRegistry().serializationPlugins.delete(name);\n\t}\n\n\t/** @deprecated global plugins should be registered with a name */\n\tstatic registerGlobalBuildPlugin(step: TransactionPlugin): void;\n\tstatic registerGlobalBuildPlugin(name: string, step: TransactionPlugin): void;\n\tstatic registerGlobalBuildPlugin(\n\t\tstepOrStep: TransactionPlugin | string,\n\t\tstep?: TransactionPlugin,\n\t) {\n\t\tgetGlobalPluginRegistry().buildPlugins.set(\n\t\t\tstepOrStep,\n\t\t\tstep ?? (stepOrStep as TransactionPlugin),\n\t\t);\n\t}\n\n\tstatic unregisterGlobalBuildPlugin(name: string) {\n\t\tgetGlobalPluginRegistry().buildPlugins.delete(name);\n\t}\n\n\taddSerializationPlugin(step: TransactionPlugin) {\n\t\tthis.#serializationPlugins.push(step);\n\t}\n\n\taddBuildPlugin(step: TransactionPlugin) {\n\t\tthis.#buildPlugins.push(step);\n\t}\n\n\taddIntentResolver(intent: string, resolver: TransactionPlugin) {\n\t\tif (this.#intentResolvers.has(intent) && this.#intentResolvers.get(intent) !== resolver) {\n\t\t\tthrow new Error(`Intent resolver for ${intent} already exists`);\n\t\t}\n\n\t\tthis.#intentResolvers.set(intent, resolver);\n\t}\n\n\tsetSender(sender: string) {\n\t\tthis.#data.sender = sender;\n\t}\n\t/**\n\t * Sets the sender only if it has not already been set.\n\t * This is useful for sponsored transaction flows where the sender may not be the same as the signer address.\n\t */\n\tsetSenderIfNotSet(sender: string) {\n\t\tif (!this.#data.sender) {\n\t\t\tthis.#data.sender = sender;\n\t\t}\n\t}\n\tsetExpiration(expiration?: InferInput<typeof TransactionExpiration> | null) {\n\t\tthis.#data.expiration = expiration ? parse(TransactionExpiration, expiration) : null;\n\t}\n\tsetGasPrice(price: number | bigint) {\n\t\tthis.#data.gasConfig.price = String(price);\n\t}\n\tsetGasBudget(budget: number | bigint) {\n\t\tthis.#data.gasConfig.budget = String(budget);\n\t}\n\n\tsetGasBudgetIfNotSet(budget: number | bigint) {\n\t\tif (this.#data.gasData.budget == null) {\n\t\t\tthis.#data.gasConfig.budget = String(budget);\n\t\t}\n\t}\n\n\tsetGasOwner(owner: string) {\n\t\tthis.#data.gasConfig.owner = owner;\n\t}\n\tsetGasPayment(payments: ObjectRef[]) {\n\t\tthis.#data.gasConfig.payment = payments.map((payment) => parse(ObjectRef, payment));\n\t}\n\n\t#data: TransactionDataBuilder;\n\n\t/** @deprecated Use `getData()` instead. */\n\tget blockData() {\n\t\treturn serializeV1TransactionData(this.#data.snapshot());\n\t}\n\n\t/** Get a snapshot of the transaction data, in JSON form: */\n\tgetData() {\n\t\treturn this.#data.snapshot();\n\t}\n\n\t// Used to brand transaction classes so that they can be identified, even between multiple copies\n\t// of the builder.\n\tget [TRANSACTION_BRAND]() {\n\t\treturn true;\n\t}\n\n\t// Temporary workaround for the wallet interface accidentally serializing transactions via postMessage\n\tget pure(): ReturnType<typeof createPure<Argument>> {\n\t\tObject.defineProperty(this, 'pure', {\n\t\t\tenumerable: false,\n\t\t\tvalue: createPure<Argument>((value): Argument => {\n\t\t\t\tif (isSerializedBcs(value)) {\n\t\t\t\t\treturn this.#data.addInput('pure', {\n\t\t\t\t\t\t$kind: 'Pure',\n\t\t\t\t\t\tPure: {\n\t\t\t\t\t\t\tbytes: value.toBase64(),\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\t// TODO: we can also do some deduplication here\n\t\t\t\treturn this.#data.addInput(\n\t\t\t\t\t'pure',\n\t\t\t\t\tis(NormalizedCallArg, value)\n\t\t\t\t\t\t? parse(NormalizedCallArg, value)\n\t\t\t\t\t\t: value instanceof Uint8Array\n\t\t\t\t\t\t\t? Inputs.Pure(value)\n\t\t\t\t\t\t\t: { $kind: 'UnresolvedPure', UnresolvedPure: { value } },\n\t\t\t\t);\n\t\t\t}),\n\t\t});\n\n\t\treturn this.pure;\n\t}\n\n\tconstructor() {\n\t\tconst globalPlugins = getGlobalPluginRegistry();\n\t\tthis.#data = new TransactionDataBuilder();\n\t\tthis.#buildPlugins = [...globalPlugins.buildPlugins.values()];\n\t\tthis.#serializationPlugins = [...globalPlugins.serializationPlugins.values()];\n\t}\n\n\t/** Returns an argument for the gas coin, to be used in a transaction. */\n\tget gas() {\n\t\treturn { $kind: 'GasCoin' as const, GasCoin: true as const };\n\t}\n\n\t/**\n\t * Add a new object input to the transaction.\n\t */\n\tobject = createObjectMethods(\n\t\t(value: TransactionObjectInput): { $kind: 'Input'; Input: number; type?: 'object' } => {\n\t\t\tif (typeof value === 'function') {\n\t\t\t\treturn this.object(value(this));\n\t\t\t}\n\n\t\t\tif (typeof value === 'object' && is(Argument, value)) {\n\t\t\t\treturn value as { $kind: 'Input'; Input: number; type?: 'object' };\n\t\t\t}\n\n\t\t\tconst id = getIdFromCallArg(value);\n\n\t\t\tconst inserted = this.#data.inputs.find((i) => id === getIdFromCallArg(i));\n\n\t\t\t// Upgrade shared object inputs to mutable if needed:\n\t\t\tif (\n\t\t\t\tinserted?.Object?.SharedObject &&\n\t\t\t\ttypeof value === 'object' &&\n\t\t\t\tvalue.Object?.SharedObject\n\t\t\t) {\n\t\t\t\tinserted.Object.SharedObject.mutable =\n\t\t\t\t\tinserted.Object.SharedObject.mutable || value.Object.SharedObject.mutable;\n\t\t\t}\n\n\t\t\treturn inserted\n\t\t\t\t? { $kind: 'Input', Input: this.#data.inputs.indexOf(inserted), type: 'object' }\n\t\t\t\t: this.#data.addInput(\n\t\t\t\t\t\t'object',\n\t\t\t\t\t\ttypeof value === 'string'\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t$kind: 'UnresolvedObject',\n\t\t\t\t\t\t\t\t\tUnresolvedObject: { objectId: normalizeSuiAddress(value) },\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t: value,\n\t\t\t\t\t);\n\t\t},\n\t);\n\n\t/**\n\t * Add a new object input to the transaction using the fully-resolved object reference.\n\t * If you only have an object ID, use `builder.object(id)` instead.\n\t */\n\tobjectRef(...args: Parameters<(typeof Inputs)['ObjectRef']>) {\n\t\treturn this.object(Inputs.ObjectRef(...args));\n\t}\n\n\t/**\n\t * Add a new receiving input to the transaction using the fully-resolved object reference.\n\t * If you only have an object ID, use `builder.object(id)` instead.\n\t */\n\treceivingRef(...args: Parameters<(typeof Inputs)['ReceivingRef']>) {\n\t\treturn this.object(Inputs.ReceivingRef(...args));\n\t}\n\n\t/**\n\t * Add a new shared object input to the transaction using the fully-resolved shared object reference.\n\t * If you only have an object ID, use `builder.object(id)` instead.\n\t */\n\tsharedObjectRef(...args: Parameters<(typeof Inputs)['SharedObjectRef']>) {\n\t\treturn this.object(Inputs.SharedObjectRef(...args));\n\t}\n\n\t/** Add a transaction to the transaction */\n\tadd<T = TransactionResult>(command: Command | ((tx: Transaction) => T)): T {\n\t\tif (typeof command === 'function') {\n\t\t\treturn command(this);\n\t\t}\n\n\t\tconst index = this.#data.commands.push(command);\n\n\t\treturn createTransactionResult(index - 1) as T;\n\t}\n\n\t#normalizeTransactionArgument(arg: TransactionArgument | SerializedBcs<any>) {\n\t\tif (isSerializedBcs(arg)) {\n\t\t\treturn this.pure(arg);\n\t\t}\n\n\t\treturn this.#resolveArgument(arg as TransactionArgument);\n\t}\n\n\t#resolveArgument(arg: TransactionArgument): Argument {\n\t\tif (typeof arg === 'function') {\n\t\t\treturn parse(Argument, arg(this));\n\t\t}\n\n\t\treturn parse(Argument, arg);\n\t}\n\n\t// Method shorthands:\n\n\tsplitCoins<\n\t\tconst Amounts extends (TransactionArgument | SerializedBcs<any> | number | string | bigint)[],\n\t>(coin: TransactionObjectArgument | string, amounts: Amounts) {\n\t\tconst command = Commands.SplitCoins(\n\t\t\ttypeof coin === 'string' ? this.object(coin) : this.#resolveArgument(coin),\n\t\t\tamounts.map((amount) =>\n\t\t\t\ttypeof amount === 'number' || typeof amount === 'bigint' || typeof amount === 'string'\n\t\t\t\t\t? this.pure.u64(amount)\n\t\t\t\t\t: this.#normalizeTransactionArgument(amount),\n\t\t\t),\n\t\t);\n\t\tconst index = this.#data.commands.push(command);\n\t\treturn createTransactionResult(index - 1, amounts.length) as Extract<\n\t\t\tArgument,\n\t\t\t{ Result: unknown }\n\t\t> & {\n\t\t\t[K in keyof Amounts]: Extract<Argument, { NestedResult: unknown }>;\n\t\t};\n\t}\n\tmergeCoins(\n\t\tdestination: TransactionObjectArgument | string,\n\t\tsources: (TransactionObjectArgument | string)[],\n\t) {\n\t\treturn this.add(\n\t\t\tCommands.MergeCoins(\n\t\t\t\tthis.object(destination),\n\t\t\t\tsources.map((src) => this.object(src)),\n\t\t\t),\n\t\t);\n\t}\n\tpublish({ modules, dependencies }: { modules: number[][] | string[]; dependencies: string[] }) {\n\t\treturn this.add(\n\t\t\tCommands.Publish({\n\t\t\t\tmodules,\n\t\t\t\tdependencies,\n\t\t\t}),\n\t\t);\n\t}\n\tupgrade({\n\t\tmodules,\n\t\tdependencies,\n\t\tpackage: packageId,\n\t\tticket,\n\t}: {\n\t\tmodules: number[][] | string[];\n\t\tdependencies: string[];\n\t\tpackage: string;\n\t\tticket: TransactionObjectArgument | string;\n\t}) {\n\t\treturn this.add(\n\t\t\tCommands.Upgrade({\n\t\t\t\tmodules,\n\t\t\t\tdependencies,\n\t\t\t\tpackage: packageId,\n\t\t\t\tticket: this.object(ticket),\n\t\t\t}),\n\t\t);\n\t}\n\tmoveCall({\n\t\targuments: args,\n\t\t...input\n\t}:\n\t\t| {\n\t\t\t\tpackage: string;\n\t\t\t\tmodule: string;\n\t\t\t\tfunction: string;\n\t\t\t\targuments?: (TransactionArgument | SerializedBcs<any>)[];\n\t\t\t\ttypeArguments?: string[];\n\t\t  }\n\t\t| {\n\t\t\t\ttarget: string;\n\t\t\t\targuments?: (TransactionArgument | SerializedBcs<any>)[];\n\t\t\t\ttypeArguments?: string[];\n\t\t  }) {\n\t\treturn this.add(\n\t\t\tCommands.MoveCall({\n\t\t\t\t...input,\n\t\t\t\targuments: args?.map((arg) => this.#normalizeTransactionArgument(arg)),\n\t\t\t} as Parameters<typeof Commands.MoveCall>[0]),\n\t\t);\n\t}\n\ttransferObjects(\n\t\tobjects: (TransactionObjectArgument | string)[],\n\t\taddress: TransactionArgument | SerializedBcs<any> | string,\n\t) {\n\t\treturn this.add(\n\t\t\tCommands.TransferObjects(\n\t\t\t\tobjects.map((obj) => this.object(obj)),\n\t\t\t\ttypeof address === 'string'\n\t\t\t\t\t? this.pure.address(address)\n\t\t\t\t\t: this.#normalizeTransactionArgument(address),\n\t\t\t),\n\t\t);\n\t}\n\tmakeMoveVec({\n\t\ttype,\n\t\telements,\n\t}: {\n\t\telements: (TransactionObjectArgument | string)[];\n\t\ttype?: string;\n\t}) {\n\t\treturn this.add(\n\t\t\tCommands.MakeMoveVec({\n\t\t\t\ttype,\n\t\t\t\telements: elements.map((obj) => this.object(obj)),\n\t\t\t}),\n\t\t);\n\t}\n\n\t/**\n\t * @deprecated Use toJSON instead.\n\t * For synchronous serialization, you can use `getData()`\n\t * */\n\tserialize() {\n\t\treturn JSON.stringify(serializeV1TransactionData(this.#data.snapshot()));\n\t}\n\n\tasync toJSON(options: SerializeTransactionOptions = {}): Promise<string> {\n\t\tawait this.prepareForSerialization(options);\n\t\treturn JSON.stringify(\n\t\t\tparse(SerializedTransactionDataV2, this.#data.snapshot()),\n\t\t\t(_key, value) => (typeof value === 'bigint' ? value.toString() : value),\n\t\t\t2,\n\t\t);\n\t}\n\n\t/** Build the transaction to BCS bytes, and sign it with the provided keypair. */\n\tasync sign(options: SignOptions): Promise<SignatureWithBytes> {\n\t\tconst { signer, ...buildOptions } = options;\n\t\tconst bytes = await this.build(buildOptions);\n\t\treturn signer.signTransaction(bytes);\n\t}\n\n\t/** Build the transaction to BCS bytes. */\n\tasync build(options: BuildTransactionOptions = {}): Promise<Uint8Array> {\n\t\tawait this.prepareForSerialization(options);\n\t\tawait this.#prepareBuild(options);\n\t\treturn this.#data.build({\n\t\t\tonlyTransactionKind: options.onlyTransactionKind,\n\t\t});\n\t}\n\n\t/** Derive transaction digest */\n\tasync getDigest(\n\t\toptions: {\n\t\t\tclient?: SuiClient;\n\t\t} = {},\n\t): Promise<string> {\n\t\tawait this.#prepareBuild(options);\n\t\treturn this.#data.getDigest();\n\t}\n\n\t/**\n\t * Prepare the transaction by validating the transaction data and resolving all inputs\n\t * so that it can be built into bytes.\n\t */\n\tasync #prepareBuild(options: BuildTransactionOptions) {\n\t\tif (!options.onlyTransactionKind && !this.#data.sender) {\n\t\t\tthrow new Error('Missing transaction sender');\n\t\t}\n\n\t\tawait this.#runPlugins([...this.#buildPlugins, resolveTransactionData], options);\n\t}\n\n\tasync #runPlugins(plugins: TransactionPlugin[], options: SerializeTransactionOptions) {\n\t\tconst createNext = (i: number) => {\n\t\t\tif (i >= plugins.length) {\n\t\t\t\treturn () => {};\n\t\t\t}\n\t\t\tconst plugin = plugins[i];\n\n\t\t\treturn async () => {\n\t\t\t\tconst next = createNext(i + 1);\n\t\t\t\tlet calledNext = false;\n\t\t\t\tlet nextResolved = false;\n\n\t\t\t\tawait plugin(this.#data, options, async () => {\n\t\t\t\t\tif (calledNext) {\n\t\t\t\t\t\tthrow new Error(`next() was call multiple times in TransactionPlugin ${i}`);\n\t\t\t\t\t}\n\n\t\t\t\t\tcalledNext = true;\n\n\t\t\t\t\tawait next();\n\n\t\t\t\t\tnextResolved = true;\n\t\t\t\t});\n\n\t\t\t\tif (!calledNext) {\n\t\t\t\t\tthrow new Error(`next() was not called in TransactionPlugin ${i}`);\n\t\t\t\t}\n\n\t\t\t\tif (!nextResolved) {\n\t\t\t\t\tthrow new Error(`next() was not awaited in TransactionPlugin ${i}`);\n\t\t\t\t}\n\t\t\t};\n\t\t};\n\n\t\tawait createNext(0)();\n\t}\n\n\tasync prepareForSerialization(options: SerializeTransactionOptions) {\n\t\tconst intents = new Set<string>();\n\t\tfor (const command of this.#data.commands) {\n\t\t\tif (command.$Intent) {\n\t\t\t\tintents.add(command.$Intent.name);\n\t\t\t}\n\t\t}\n\n\t\tconst steps = [...this.#serializationPlugins];\n\n\t\tfor (const intent of intents) {\n\t\t\tif (options.supportedIntents?.includes(intent)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (!this.#intentResolvers.has(intent)) {\n\t\t\t\tthrow new Error(`Missing intent resolver for ${intent}`);\n\t\t\t}\n\n\t\t\tsteps.push(this.#intentResolvers.get(intent)!);\n\t\t}\n\n\t\tawait this.#runPlugins(steps, options);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,iBAA4C;AAE5C,qBAA0B;AAI1B,uBAAoC;AAEpC,sBAAyB;AAEzB,sBAA8E;AAC9E,gBAA2C;AAC3C,gBAA4C;AAC5C,oBAAuB;AAMvB,+BAAuC;AACvC,oBAAoC;AACpC,kBAA2B;AAC3B,6BAAuC;AACvC,mBAAiC;AA3BjC;AAoCA,SAAS,wBAAwB,OAAe,SAAS,UAA6B;AACrF,QAAM,aAAa,EAAE,OAAO,UAAmB,QAAQ,MAAM;AAE7D,QAAM,gBAGA,CAAC;AACP,QAAM,kBAAkB,CACvB,gBAKC,4DAA+B;AAAA,IAC/B,OAAO;AAAA,IACP,cAAc,CAAC,OAAO,WAAW;AAAA,EAClC;AAED,SAAO,IAAI,MAAM,YAAY;AAAA,IAC5B,MAAM;AACL,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAIA,IAAI,QAAQ,UAAU;AAErB,UAAI,YAAY,QAAQ;AACvB,eAAO,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACpC;AAGA,UAAI,aAAa,OAAO,UAAU;AACjC,eAAO,aAAa;AACnB,cAAI,IAAI;AACR,iBAAO,IAAI,QAAQ;AAClB,kBAAM,gBAAgB,CAAC;AACvB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,UAAI,OAAO,aAAa,SAAU;AAElC,YAAM,cAAc,SAAS,UAAU,EAAE;AACzC,UAAI,OAAO,MAAM,WAAW,KAAK,cAAc,EAAG;AAClD,aAAO,gBAAgB,WAAW;AAAA,IACnC;AAAA,EACD,CAAC;AACF;AAEA,MAAM,oBAAoB,OAAO,IAAI,qBAAqB;AAMnD,SAAS,cAAc,KAAkC;AAC/D,SAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,YAAa,IAAY,iBAAiB,MAAM;AAChF;AAWA,MAAM,uBAAkD;AAAA,EACvD,cAAc,oBAAI,IAAI;AAAA,EACtB,sBAAsB,oBAAI,IAAI;AAC/B;AAEA,MAAM,2BAA2B,OAAO,IAAI,8BAA8B;AAC1E,SAAS,0BAA0B;AAClC,MAAI;AACH,UAAM,SAAS;AAIf,QAAI,CAAC,OAAO,wBAAwB,GAAG;AACtC,aAAO,wBAAwB,IAAI;AAAA,IACpC;AAEA,WAAO,OAAO,wBAAwB;AAAA,EACvC,SAAS,GAAG;AACX,WAAO;AAAA,EACR;AACD;AAKO,MAAM,eAAN,MAAM,aAAY;AAAA,EA6KxB,cAAc;AA7KR;AACN;AACA;AACA,yCAAmB,oBAAI,IAA+B;AA2HtD;AA8DA;AAAA;AAAA;AAAA,sBAAS;AAAA,MACR,CAAC,UAAsF;AACtF,YAAI,OAAO,UAAU,YAAY;AAChC,iBAAO,KAAK,OAAO,MAAM,IAAI,CAAC;AAAA,QAC/B;AAEA,YAAI,OAAO,UAAU,gBAAY,mBAAG,0BAAU,KAAK,GAAG;AACrD,iBAAO;AAAA,QACR;AAEA,cAAM,SAAK,+BAAiB,KAAK;AAEjC,cAAM,WAAW,mBAAK,OAAM,OAAO,KAAK,CAAC,MAAM,WAAO,+BAAiB,CAAC,CAAC;AAGzE,YACC,UAAU,QAAQ,gBAClB,OAAO,UAAU,YACjB,MAAM,QAAQ,cACb;AACD,mBAAS,OAAO,aAAa,UAC5B,SAAS,OAAO,aAAa,WAAW,MAAM,OAAO,aAAa;AAAA,QACpE;AAEA,eAAO,WACJ,EAAE,OAAO,SAAS,OAAO,mBAAK,OAAM,OAAO,QAAQ,QAAQ,GAAG,MAAM,SAAS,IAC7E,mBAAK,OAAM;AAAA,UACX;AAAA,UACA,OAAO,UAAU,WACd;AAAA,YACA,OAAO;AAAA,YACP,kBAAkB,EAAE,cAAU,sCAAoB,KAAK,EAAE;AAAA,UAC1D,IACC;AAAA,QACJ;AAAA,MACH;AAAA,IACD;AAlDC,UAAM,gBAAgB,wBAAwB;AAC9C,uBAAK,OAAQ,IAAI,8CAAuB;AACxC,uBAAK,eAAgB,CAAC,GAAG,cAAc,aAAa,OAAO,CAAC;AAC5D,uBAAK,uBAAwB,CAAC,GAAG,cAAc,qBAAqB,OAAO,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA,EAzKA,OAAO,SAAS,YAAiC;AAChD,UAAM,KAAK,IAAI,aAAY;AAE3B,qBAAG,OAAQ,8CAAuB;AAAA,MACjC,OAAO,eAAe,eAAW,uBAAW,UAAU,IAAI;AAAA,IAC3D;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK,aAAgD;AAC3D,UAAM,iBAAiB,IAAI,aAAY;AAEvC,QAAI,cAAc,WAAW,GAAG;AAC/B,mCAAe,OAAQ,IAAI,8CAAuB,YAAY,QAAQ,CAAC;AAAA,IACxE,WAAW,OAAO,gBAAgB,YAAY,CAAC,YAAY,WAAW,GAAG,GAAG;AAC3E,mCAAe,OAAQ,8CAAuB;AAAA,QAC7C,OAAO,gBAAgB,eAAW,uBAAW,WAAW,IAAI;AAAA,MAC7D;AAAA,IACD,OAAO;AACN,mCAAe,OAAQ,8CAAuB,QAAQ,KAAK,MAAM,WAAW,CAAC;AAAA,IAC9E;AAEA,WAAO;AAAA,EACR;AAAA,EAKA,OAAO,kCACN,YACA,MACC;AACD,4BAAwB,EAAE,qBAAqB;AAAA,MAC9C;AAAA,MACA,QAAS;AAAA,IACV;AAAA,EACD;AAAA,EAEA,OAAO,oCAAoC,MAAc;AACxD,4BAAwB,EAAE,qBAAqB,OAAO,IAAI;AAAA,EAC3D;AAAA,EAKA,OAAO,0BACN,YACA,MACC;AACD,4BAAwB,EAAE,aAAa;AAAA,MACtC;AAAA,MACA,QAAS;AAAA,IACV;AAAA,EACD;AAAA,EAEA,OAAO,4BAA4B,MAAc;AAChD,4BAAwB,EAAE,aAAa,OAAO,IAAI;AAAA,EACnD;AAAA,EAEA,uBAAuB,MAAyB;AAC/C,uBAAK,uBAAsB,KAAK,IAAI;AAAA,EACrC;AAAA,EAEA,eAAe,MAAyB;AACvC,uBAAK,eAAc,KAAK,IAAI;AAAA,EAC7B;AAAA,EAEA,kBAAkB,QAAgB,UAA6B;AAC9D,QAAI,mBAAK,kBAAiB,IAAI,MAAM,KAAK,mBAAK,kBAAiB,IAAI,MAAM,MAAM,UAAU;AACxF,YAAM,IAAI,MAAM,uBAAuB,MAAM,iBAAiB;AAAA,IAC/D;AAEA,uBAAK,kBAAiB,IAAI,QAAQ,QAAQ;AAAA,EAC3C;AAAA,EAEA,UAAU,QAAgB;AACzB,uBAAK,OAAM,SAAS;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,QAAgB;AACjC,QAAI,CAAC,mBAAK,OAAM,QAAQ;AACvB,yBAAK,OAAM,SAAS;AAAA,IACrB;AAAA,EACD;AAAA,EACA,cAAc,YAA8D;AAC3E,uBAAK,OAAM,aAAa,iBAAa,sBAAM,uCAAuB,UAAU,IAAI;AAAA,EACjF;AAAA,EACA,YAAY,OAAwB;AACnC,uBAAK,OAAM,UAAU,QAAQ,OAAO,KAAK;AAAA,EAC1C;AAAA,EACA,aAAa,QAAyB;AACrC,uBAAK,OAAM,UAAU,SAAS,OAAO,MAAM;AAAA,EAC5C;AAAA,EAEA,qBAAqB,QAAyB;AAC7C,QAAI,mBAAK,OAAM,QAAQ,UAAU,MAAM;AACtC,yBAAK,OAAM,UAAU,SAAS,OAAO,MAAM;AAAA,IAC5C;AAAA,EACD;AAAA,EAEA,YAAY,OAAe;AAC1B,uBAAK,OAAM,UAAU,QAAQ;AAAA,EAC9B;AAAA,EACA,cAAc,UAAuB;AACpC,uBAAK,OAAM,UAAU,UAAU,SAAS,IAAI,CAAC,gBAAY,sBAAM,2BAAW,OAAO,CAAC;AAAA,EACnF;AAAA;AAAA,EAKA,IAAI,YAAY;AACf,eAAO,sCAA2B,mBAAK,OAAM,SAAS,CAAC;AAAA,EACxD;AAAA;AAAA,EAGA,UAAU;AACT,WAAO,mBAAK,OAAM,SAAS;AAAA,EAC5B;AAAA;AAAA;AAAA,EAIA,KAAK,iBAAiB,IAAI;AACzB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,IAAI,OAAgD;AACnD,WAAO,eAAe,MAAM,QAAQ;AAAA,MACnC,YAAY;AAAA,MACZ,WAAO,wBAAqB,CAAC,UAAoB;AAChD,gBAAI,4BAAgB,KAAK,GAAG;AAC3B,iBAAO,mBAAK,OAAM,SAAS,QAAQ;AAAA,YAClC,OAAO;AAAA,YACP,MAAM;AAAA,cACL,OAAO,MAAM,SAAS;AAAA,YACvB;AAAA,UACD,CAAC;AAAA,QACF;AAGA,eAAO,mBAAK,OAAM;AAAA,UACjB;AAAA,cACA,mBAAG,mCAAmB,KAAK,QACxB,sBAAM,mCAAmB,KAAK,IAC9B,iBAAiB,aAChB,qBAAO,KAAK,KAAK,IACjB,EAAE,OAAO,kBAAkB,gBAAgB,EAAE,MAAM,EAAE;AAAA,QAC1D;AAAA,MACD,CAAC;AAAA,IACF,CAAC;AAED,WAAO,KAAK;AAAA,EACb;AAAA;AAAA,EAUA,IAAI,MAAM;AACT,WAAO,EAAE,OAAO,WAAoB,SAAS,KAAc;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EA+CA,aAAa,MAAgD;AAC5D,WAAO,KAAK,OAAO,qBAAO,UAAU,GAAG,IAAI,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,MAAmD;AAClE,WAAO,KAAK,OAAO,qBAAO,aAAa,GAAG,IAAI,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,MAAsD;AACxE,WAAO,KAAK,OAAO,qBAAO,gBAAgB,GAAG,IAAI,CAAC;AAAA,EACnD;AAAA;AAAA,EAGA,IAA2B,SAAgD;AAC1E,QAAI,OAAO,YAAY,YAAY;AAClC,aAAO,QAAQ,IAAI;AAAA,IACpB;AAEA,UAAM,QAAQ,mBAAK,OAAM,SAAS,KAAK,OAAO;AAE9C,WAAO,wBAAwB,QAAQ,CAAC;AAAA,EACzC;AAAA;AAAA,EAoBA,WAEE,MAA0C,SAAkB;AAC7D,UAAM,UAAU,yBAAS;AAAA,MACxB,OAAO,SAAS,WAAW,KAAK,OAAO,IAAI,IAAI,sBAAK,4CAAL,WAAsB;AAAA,MACrE,QAAQ;AAAA,QAAI,CAAC,WACZ,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY,OAAO,WAAW,WAC3E,KAAK,KAAK,IAAI,MAAM,IACpB,sBAAK,yDAAL,WAAmC;AAAA,MACvC;AAAA,IACD;AACA,UAAM,QAAQ,mBAAK,OAAM,SAAS,KAAK,OAAO;AAC9C,WAAO,wBAAwB,QAAQ,GAAG,QAAQ,MAAM;AAAA,EAMzD;AAAA,EACA,WACC,aACA,SACC;AACD,WAAO,KAAK;AAAA,MACX,yBAAS;AAAA,QACR,KAAK,OAAO,WAAW;AAAA,QACvB,QAAQ,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAAA,EACA,QAAQ,EAAE,SAAS,aAAa,GAA+D;AAC9F,WAAO,KAAK;AAAA,MACX,yBAAS,QAAQ;AAAA,QAChB;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EACA,QAAQ;AAAA,IACP;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACD,GAKG;AACF,WAAO,KAAK;AAAA,MACX,yBAAS,QAAQ;AAAA,QAChB;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,QAAQ,KAAK,OAAO,MAAM;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EACA,SAAS;AAAA,IACR,WAAW;AAAA,IACX,GAAG;AAAA,EACJ,GAYM;AACL,WAAO,KAAK;AAAA,MACX,yBAAS,SAAS;AAAA,QACjB,GAAG;AAAA,QACH,WAAW,MAAM,IAAI,CAAC,QAAQ,sBAAK,yDAAL,WAAmC,IAAI;AAAA,MACtE,CAA4C;AAAA,IAC7C;AAAA,EACD;AAAA,EACA,gBACC,SACA,SACC;AACD,WAAO,KAAK;AAAA,MACX,yBAAS;AAAA,QACR,QAAQ,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QACrC,OAAO,YAAY,WAChB,KAAK,KAAK,QAAQ,OAAO,IACzB,sBAAK,yDAAL,WAAmC;AAAA,MACvC;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACD,GAGG;AACF,WAAO,KAAK;AAAA,MACX,yBAAS,YAAY;AAAA,QACpB;AAAA,QACA,UAAU,SAAS,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MACjD,CAAC;AAAA,IACF;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACX,WAAO,KAAK,cAAU,sCAA2B,mBAAK,OAAM,SAAS,CAAC,CAAC;AAAA,EACxE;AAAA,EAEA,MAAM,OAAO,UAAuC,CAAC,GAAoB;AACxE,UAAM,KAAK,wBAAwB,OAAO;AAC1C,WAAO,KAAK;AAAA,UACX,sBAAM,uCAA6B,mBAAK,OAAM,SAAS,CAAC;AAAA,MACxD,CAAC,MAAM,UAAW,OAAO,UAAU,WAAW,MAAM,SAAS,IAAI;AAAA,MACjE;AAAA,IACD;AAAA,EACD;AAAA;AAAA,EAGA,MAAM,KAAK,SAAmD;AAC7D,UAAM,EAAE,QAAQ,GAAG,aAAa,IAAI;AACpC,UAAM,QAAQ,MAAM,KAAK,MAAM,YAAY;AAC3C,WAAO,OAAO,gBAAgB,KAAK;AAAA,EACpC;AAAA;AAAA,EAGA,MAAM,MAAM,UAAmC,CAAC,GAAwB;AACvE,UAAM,KAAK,wBAAwB,OAAO;AAC1C,UAAM,sBAAK,yCAAL,WAAmB;AACzB,WAAO,mBAAK,OAAM,MAAM;AAAA,MACvB,qBAAqB,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,UACL,UAEI,CAAC,GACa;AAClB,UAAM,sBAAK,yCAAL,WAAmB;AACzB,WAAO,mBAAK,OAAM,UAAU;AAAA,EAC7B;AAAA,EAmDA,MAAM,wBAAwB,SAAsC;AACnE,UAAM,UAAU,oBAAI,IAAY;AAChC,eAAW,WAAW,mBAAK,OAAM,UAAU;AAC1C,UAAI,QAAQ,SAAS;AACpB,gBAAQ,IAAI,QAAQ,QAAQ,IAAI;AAAA,MACjC;AAAA,IACD;AAEA,UAAM,QAAQ,CAAC,GAAG,mBAAK,sBAAqB;AAE5C,eAAW,UAAU,SAAS;AAC7B,UAAI,QAAQ,kBAAkB,SAAS,MAAM,GAAG;AAC/C;AAAA,MACD;AAEA,UAAI,CAAC,mBAAK,kBAAiB,IAAI,MAAM,GAAG;AACvC,cAAM,IAAI,MAAM,+BAA+B,MAAM,EAAE;AAAA,MACxD;AAEA,YAAM,KAAK,mBAAK,kBAAiB,IAAI,MAAM,CAAE;AAAA,IAC9C;AAEA,UAAM,sBAAK,uCAAL,WAAiB,OAAO;AAAA,EAC/B;AACD;AAvfC;AACA;AACA;AA2HA;AA9HM;AAqQN,kCAA6B,SAAC,KAA+C;AAC5E,UAAI,4BAAgB,GAAG,GAAG;AACzB,WAAO,KAAK,KAAK,GAAG;AAAA,EACrB;AAEA,SAAO,sBAAK,4CAAL,WAAsB;AAC9B;AAEA,qBAAgB,SAAC,KAAoC;AACpD,MAAI,OAAO,QAAQ,YAAY;AAC9B,eAAO,sBAAM,0BAAU,IAAI,IAAI,CAAC;AAAA,EACjC;AAEA,aAAO,sBAAM,0BAAU,GAAG;AAC3B;AAgKM,kBAAa,eAAC,SAAkC;AACrD,MAAI,CAAC,QAAQ,uBAAuB,CAAC,mBAAK,OAAM,QAAQ;AACvD,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC7C;AAEA,QAAM,sBAAK,uCAAL,WAAiB,CAAC,GAAG,mBAAK,gBAAe,+CAAsB,GAAG;AACzE;AAEM,gBAAW,eAAC,SAA8B,SAAsC;AACrF,QAAM,aAAa,CAAC,MAAc;AACjC,QAAI,KAAK,QAAQ,QAAQ;AACxB,aAAO,MAAM;AAAA,MAAC;AAAA,IACf;AACA,UAAM,SAAS,QAAQ,CAAC;AAExB,WAAO,YAAY;AAClB,YAAM,OAAO,WAAW,IAAI,CAAC;AAC7B,UAAI,aAAa;AACjB,UAAI,eAAe;AAEnB,YAAM,OAAO,mBAAK,QAAO,SAAS,YAAY;AAC7C,YAAI,YAAY;AACf,gBAAM,IAAI,MAAM,uDAAuD,CAAC,EAAE;AAAA,QAC3E;AAEA,qBAAa;AAEb,cAAM,KAAK;AAEX,uBAAe;AAAA,MAChB,CAAC;AAED,UAAI,CAAC,YAAY;AAChB,cAAM,IAAI,MAAM,8CAA8C,CAAC,EAAE;AAAA,MAClE;AAEA,UAAI,CAAC,cAAc;AAClB,cAAM,IAAI,MAAM,+CAA+C,CAAC,EAAE;AAAA,MACnE;AAAA,IACD;AAAA,EACD;AAEA,QAAM,WAAW,CAAC,EAAE;AACrB;AA9dM,IAAM,cAAN;", "names": []}