{"version": 3, "sources": ["../../../src/zklogin/utils.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { hexToBytes } from '@noble/hashes/utils';\n\nimport type { PublicKey } from '../cryptography/publickey.js';\nimport { poseidonHash } from './poseidon.js';\n\nconst MAX_KEY_CLAIM_NAME_LENGTH = 32;\nconst MAX_KEY_CLAIM_VALUE_LENGTH = 115;\nconst MAX_AUD_VALUE_LENGTH = 145;\nconst PACK_WIDTH = 248;\n\nfunction findFirstNonZeroIndex(bytes: Uint8Array) {\n\tfor (let i = 0; i < bytes.length; i++) {\n\t\tif (bytes[i] !== 0) {\n\t\t\treturn i;\n\t\t}\n\t}\n\n\treturn -1;\n}\n\n// Derive bytearray from num where the bytearray is padded to the left with 0s to the specified width.\nexport function toPaddedBigEndianBytes(num: bigint, width: number): Uint8Array {\n\tconst hex = num.toString(16);\n\treturn hexToBytes(hex.padStart(width * 2, '0').slice(-width * 2));\n}\n\n// Derive bytearray from num where the bytearray is not padded with 0.\nexport function toBigEndianBytes(num: bigint, width: number): Uint8Array {\n\tconst bytes = toPaddedBigEndianBytes(num, width);\n\n\tconst firstNonZeroIndex = findFirstNonZeroIndex(bytes);\n\n\tif (firstNonZeroIndex === -1) {\n\t\treturn new Uint8Array([0]);\n\t}\n\n\treturn bytes.slice(firstNonZeroIndex);\n}\n\nexport function getExtendedEphemeralPublicKey(publicKey: PublicKey) {\n\treturn publicKey.toSuiPublicKey();\n}\n\n/**\n * Splits an array into chunks of size chunk_size. If the array is not evenly\n * divisible by chunk_size, the first chunk will be smaller than chunk_size.\n *\n * E.g., arrayChunk([1, 2, 3, 4, 5], 2) => [[1], [2, 3], [4, 5]]\n *\n * Note: Can be made more efficient by avoiding the reverse() calls.\n */\nexport function chunkArray<T>(array: T[], chunk_size: number): T[][] {\n\tconst chunks = Array(Math.ceil(array.length / chunk_size));\n\tconst revArray = array.reverse();\n\tfor (let i = 0; i < chunks.length; i++) {\n\t\tchunks[i] = revArray.slice(i * chunk_size, (i + 1) * chunk_size).reverse();\n\t}\n\treturn chunks.reverse();\n}\n\nfunction bytesBEToBigInt(bytes: number[]): bigint {\n\tconst hex = bytes.map((b) => b.toString(16).padStart(2, '0')).join('');\n\tif (hex.length === 0) {\n\t\treturn BigInt(0);\n\t}\n\treturn BigInt('0x' + hex);\n}\n\n// hashes an ASCII string to a field element\nexport function hashASCIIStrToField(str: string, maxSize: number) {\n\tif (str.length > maxSize) {\n\t\tthrow new Error(`String ${str} is longer than ${maxSize} chars`);\n\t}\n\n\t// Note: Padding with zeroes is safe because we are only using this function to map human-readable sequence of bytes.\n\t// So the ASCII values of those characters will never be zero (null character).\n\tconst strPadded = str\n\t\t.padEnd(maxSize, String.fromCharCode(0))\n\t\t.split('')\n\t\t.map((c) => c.charCodeAt(0));\n\n\tconst chunkSize = PACK_WIDTH / 8;\n\tconst packed = chunkArray(strPadded, chunkSize).map((chunk) => bytesBEToBigInt(chunk));\n\treturn poseidonHash(packed);\n}\n\nexport function genAddressSeed(\n\tsalt: string | bigint,\n\tname: string,\n\tvalue: string,\n\taud: string,\n\tmax_name_length = MAX_KEY_CLAIM_NAME_LENGTH,\n\tmax_value_length = MAX_KEY_CLAIM_VALUE_LENGTH,\n\tmax_aud_length = MAX_AUD_VALUE_LENGTH,\n): bigint {\n\treturn poseidonHash([\n\t\thashASCIIStrToField(name, max_name_length),\n\t\thashASCIIStrToField(value, max_value_length),\n\t\thashASCIIStrToField(aud, max_aud_length),\n\t\tposeidonHash([BigInt(salt)]),\n\t]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,mBAA2B;AAG3B,sBAA6B;AAE7B,MAAM,4BAA4B;AAClC,MAAM,6BAA6B;AACnC,MAAM,uBAAuB;AAC7B,MAAM,aAAa;AAEnB,SAAS,sBAAsB,OAAmB;AACjD,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,QAAI,MAAM,CAAC,MAAM,GAAG;AACnB,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAGO,SAAS,uBAAuB,KAAa,OAA2B;AAC9E,QAAM,MAAM,IAAI,SAAS,EAAE;AAC3B,aAAO,yBAAW,IAAI,SAAS,QAAQ,GAAG,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;AACjE;AAGO,SAAS,iBAAiB,KAAa,OAA2B;AACxE,QAAM,QAAQ,uBAAuB,KAAK,KAAK;AAE/C,QAAM,oBAAoB,sBAAsB,KAAK;AAErD,MAAI,sBAAsB,IAAI;AAC7B,WAAO,IAAI,WAAW,CAAC,CAAC,CAAC;AAAA,EAC1B;AAEA,SAAO,MAAM,MAAM,iBAAiB;AACrC;AAEO,SAAS,8BAA8B,WAAsB;AACnE,SAAO,UAAU,eAAe;AACjC;AAUO,SAAS,WAAc,OAAY,YAA2B;AACpE,QAAM,SAAS,MAAM,KAAK,KAAK,MAAM,SAAS,UAAU,CAAC;AACzD,QAAM,WAAW,MAAM,QAAQ;AAC/B,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACvC,WAAO,CAAC,IAAI,SAAS,MAAM,IAAI,aAAa,IAAI,KAAK,UAAU,EAAE,QAAQ;AAAA,EAC1E;AACA,SAAO,OAAO,QAAQ;AACvB;AAEA,SAAS,gBAAgB,OAAyB;AACjD,QAAM,MAAM,MAAM,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AACrE,MAAI,IAAI,WAAW,GAAG;AACrB,WAAO,OAAO,CAAC;AAAA,EAChB;AACA,SAAO,OAAO,OAAO,GAAG;AACzB;AAGO,SAAS,oBAAoB,KAAa,SAAiB;AACjE,MAAI,IAAI,SAAS,SAAS;AACzB,UAAM,IAAI,MAAM,UAAU,GAAG,mBAAmB,OAAO,QAAQ;AAAA,EAChE;AAIA,QAAM,YAAY,IAChB,OAAO,SAAS,OAAO,aAAa,CAAC,CAAC,EACtC,MAAM,EAAE,EACR,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAE5B,QAAM,YAAY,aAAa;AAC/B,QAAM,SAAS,WAAW,WAAW,SAAS,EAAE,IAAI,CAAC,UAAU,gBAAgB,KAAK,CAAC;AACrF,aAAO,8BAAa,MAAM;AAC3B;AAEO,SAAS,eACf,MACA,MACA,OACA,KACA,kBAAkB,2BAClB,mBAAmB,4BACnB,iBAAiB,sBACR;AACT,aAAO,8BAAa;AAAA,IACnB,oBAAoB,MAAM,eAAe;AAAA,IACzC,oBAAoB,OAAO,gBAAgB;AAAA,IAC3C,oBAAoB,KAAK,cAAc;AAAA,QACvC,8BAAa,CAAC,OAAO,IAAI,CAAC,CAAC;AAAA,EAC5B,CAAC;AACF;", "names": []}