{"version": 3, "sources": ["../../../src/zklogin/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport { getZkLoginSignature, parseZkLoginSignature } from './signature.js';\nexport {\n\ttoBigEndianBytes,\n\ttoPaddedBigEndianBytes,\n\thashASCIIStrToField,\n\tgenAddressSeed,\n\tgetExtendedEphemeralPublicKey,\n} from './utils.js';\nexport { computeZkLoginAddressFromSeed, computeZkLoginAddress, jwtToAddress } from './address.js';\nexport type { ComputeZkLoginAddressOptions } from './address.js';\nexport { toZkLoginPublicIdentifier, ZkLoginPublicIdentifier } from './publickey.js';\nexport type { ZkLoginSignatureInputs } from './bcs.js';\nexport { poseidonHash } from './poseidon.js';\nexport { generateNonce, generateRandomness } from './nonce.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,uBAA2D;AAC3D,mBAMO;AACP,qBAAmF;AAEnF,uBAAmE;AAEnE,sBAA6B;AAC7B,mBAAkD;", "names": []}