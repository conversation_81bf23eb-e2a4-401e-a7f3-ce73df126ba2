{"version": 3, "sources": ["../../../../src/keypairs/ed25519/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64 } from '@mysten/bcs';\nimport { ed25519 } from '@noble/curves/ed25519';\n\nimport type { PublicKeyInitData } from '../../cryptography/publickey.js';\nimport { bytesEqual, PublicKey } from '../../cryptography/publickey.js';\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../../cryptography/signature-scheme.js';\nimport { parseSerializedSignature } from '../../cryptography/signature.js';\n\nconst PUBLIC_KEY_SIZE = 32;\n\n/**\n * An Ed25519 public key\n */\nexport class Ed25519PublicKey extends PublicKey {\n\tstatic SIZE = PUBLIC_KEY_SIZE;\n\tprivate data: Uint8Array;\n\n\t/**\n\t * Create a new Ed25519PublicKey object\n\t * @param value ed25519 public key as buffer or base-64 encoded string\n\t */\n\tconstructor(value: PublicKeyInitData) {\n\t\tsuper();\n\n\t\tif (typeof value === 'string') {\n\t\t\tthis.data = fromBase64(value);\n\t\t} else if (value instanceof Uint8Array) {\n\t\t\tthis.data = value;\n\t\t} else {\n\t\t\tthis.data = Uint8Array.from(value);\n\t\t}\n\n\t\tif (this.data.length !== PUBLIC_KEY_SIZE) {\n\t\t\tthrow new Error(\n\t\t\t\t`Invalid public key input. Expected ${PUBLIC_KEY_SIZE} bytes, got ${this.data.length}`,\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * Checks if two Ed25519 public keys are equal\n\t */\n\toverride equals(publicKey: Ed25519PublicKey): boolean {\n\t\treturn super.equals(publicKey);\n\t}\n\n\t/**\n\t * Return the byte array representation of the Ed25519 public key\n\t */\n\ttoRawBytes(): Uint8Array {\n\t\treturn this.data;\n\t}\n\n\t/**\n\t * Return the Sui address associated with this Ed25519 public key\n\t */\n\tflag(): number {\n\t\treturn SIGNATURE_SCHEME_TO_FLAG['ED25519'];\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tasync verify(message: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tlet bytes;\n\t\tif (typeof signature === 'string') {\n\t\t\tconst parsed = parseSerializedSignature(signature);\n\t\t\tif (parsed.signatureScheme !== 'ED25519') {\n\t\t\t\tthrow new Error('Invalid signature scheme');\n\t\t\t}\n\n\t\t\tif (!bytesEqual(this.toRawBytes(), parsed.publicKey)) {\n\t\t\t\tthrow new Error('Signature does not match public key');\n\t\t\t}\n\n\t\t\tbytes = parsed.signature;\n\t\t} else {\n\t\t\tbytes = signature;\n\t\t}\n\n\t\treturn ed25519.verify(bytes, message, this.toRawBytes());\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAA2B;AAC3B,qBAAwB;AAGxB,uBAAsC;AACtC,8BAAyC;AACzC,uBAAyC;AAEzC,MAAM,kBAAkB;AAKjB,MAAM,yBAAyB,2BAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ/C,YAAY,OAA0B;AACrC,UAAM;AAEN,QAAI,OAAO,UAAU,UAAU;AAC9B,WAAK,WAAO,uBAAW,KAAK;AAAA,IAC7B,WAAW,iBAAiB,YAAY;AACvC,WAAK,OAAO;AAAA,IACb,OAAO;AACN,WAAK,OAAO,WAAW,KAAK,KAAK;AAAA,IAClC;AAEA,QAAI,KAAK,KAAK,WAAW,iBAAiB;AACzC,YAAM,IAAI;AAAA,QACT,sCAAsC,eAAe,eAAe,KAAK,KAAK,MAAM;AAAA,MACrF;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKS,OAAO,WAAsC;AACrD,WAAO,MAAM,OAAO,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAyB;AACxB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe;AACd,WAAO,iDAAyB,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,SAAqB,WAAkD;AACnF,QAAI;AACJ,QAAI,OAAO,cAAc,UAAU;AAClC,YAAM,aAAS,2CAAyB,SAAS;AACjD,UAAI,OAAO,oBAAoB,WAAW;AACzC,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC3C;AAEA,UAAI,KAAC,6BAAW,KAAK,WAAW,GAAG,OAAO,SAAS,GAAG;AACrD,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACtD;AAEA,cAAQ,OAAO;AAAA,IAChB,OAAO;AACN,cAAQ;AAAA,IACT;AAEA,WAAO,uBAAQ,OAAO,OAAO,SAAS,KAAK,WAAW,CAAC;AAAA,EACxD;AACD;AArEa,iBACL,OAAO;", "names": []}