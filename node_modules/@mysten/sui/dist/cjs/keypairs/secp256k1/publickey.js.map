{"version": 3, "sources": ["../../../../src/keypairs/secp256k1/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64 } from '@mysten/bcs';\nimport { secp256k1 } from '@noble/curves/secp256k1';\nimport { sha256 } from '@noble/hashes/sha256';\n\nimport { bytesEqual, PublicKey } from '../../cryptography/publickey.js';\nimport type { PublicKeyInitData } from '../../cryptography/publickey.js';\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../../cryptography/signature-scheme.js';\nimport { parseSerializedSignature } from '../../cryptography/signature.js';\n\nconst SECP256K1_PUBLIC_KEY_SIZE = 33;\n\n/**\n * A Secp256k1 public key\n */\nexport class Secp256k1PublicKey extends PublicKey {\n\tstatic SIZE = SECP256K1_PUBLIC_KEY_SIZE;\n\tprivate data: Uint8Array;\n\n\t/**\n\t * Create a new Secp256k1PublicKey object\n\t * @param value secp256k1 public key as buffer or base-64 encoded string\n\t */\n\tconstructor(value: PublicKeyInitData) {\n\t\tsuper();\n\n\t\tif (typeof value === 'string') {\n\t\t\tthis.data = fromBase64(value);\n\t\t} else if (value instanceof Uint8Array) {\n\t\t\tthis.data = value;\n\t\t} else {\n\t\t\tthis.data = Uint8Array.from(value);\n\t\t}\n\n\t\tif (this.data.length !== SECP256K1_PUBLIC_KEY_SIZE) {\n\t\t\tthrow new Error(\n\t\t\t\t`Invalid public key input. Expected ${SECP256K1_PUBLIC_KEY_SIZE} bytes, got ${this.data.length}`,\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * Checks if two Secp256k1 public keys are equal\n\t */\n\toverride equals(publicKey: Secp256k1PublicKey): boolean {\n\t\treturn super.equals(publicKey);\n\t}\n\n\t/**\n\t * Return the byte array representation of the Secp256k1 public key\n\t */\n\ttoRawBytes(): Uint8Array {\n\t\treturn this.data;\n\t}\n\n\t/**\n\t * Return the Sui address associated with this Secp256k1 public key\n\t */\n\tflag(): number {\n\t\treturn SIGNATURE_SCHEME_TO_FLAG['Secp256k1'];\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tasync verify(message: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tlet bytes;\n\t\tif (typeof signature === 'string') {\n\t\t\tconst parsed = parseSerializedSignature(signature);\n\t\t\tif (parsed.signatureScheme !== 'Secp256k1') {\n\t\t\t\tthrow new Error('Invalid signature scheme');\n\t\t\t}\n\n\t\t\tif (!bytesEqual(this.toRawBytes(), parsed.publicKey)) {\n\t\t\t\tthrow new Error('Signature does not match public key');\n\t\t\t}\n\n\t\t\tbytes = parsed.signature;\n\t\t} else {\n\t\t\tbytes = signature;\n\t\t}\n\n\t\treturn secp256k1.verify(\n\t\t\tsecp256k1.Signature.fromCompact(bytes),\n\t\t\tsha256(message),\n\t\t\tthis.toRawBytes(),\n\t\t);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAA2B;AAC3B,uBAA0B;AAC1B,oBAAuB;AAEvB,uBAAsC;AAEtC,8BAAyC;AACzC,uBAAyC;AAEzC,MAAM,4BAA4B;AAK3B,MAAM,2BAA2B,2BAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjD,YAAY,OAA0B;AACrC,UAAM;AAEN,QAAI,OAAO,UAAU,UAAU;AAC9B,WAAK,WAAO,uBAAW,KAAK;AAAA,IAC7B,WAAW,iBAAiB,YAAY;AACvC,WAAK,OAAO;AAAA,IACb,OAAO;AACN,WAAK,OAAO,WAAW,KAAK,KAAK;AAAA,IAClC;AAEA,QAAI,KAAK,KAAK,WAAW,2BAA2B;AACnD,YAAM,IAAI;AAAA,QACT,sCAAsC,yBAAyB,eAAe,KAAK,KAAK,MAAM;AAAA,MAC/F;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKS,OAAO,WAAwC;AACvD,WAAO,MAAM,OAAO,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAyB;AACxB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe;AACd,WAAO,iDAAyB,WAAW;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,SAAqB,WAAkD;AACnF,QAAI;AACJ,QAAI,OAAO,cAAc,UAAU;AAClC,YAAM,aAAS,2CAAyB,SAAS;AACjD,UAAI,OAAO,oBAAoB,aAAa;AAC3C,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC3C;AAEA,UAAI,KAAC,6BAAW,KAAK,WAAW,GAAG,OAAO,SAAS,GAAG;AACrD,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACtD;AAEA,cAAQ,OAAO;AAAA,IAChB,OAAO;AACN,cAAQ;AAAA,IACT;AAEA,WAAO,2BAAU;AAAA,MAChB,2BAAU,UAAU,YAAY,KAAK;AAAA,UACrC,sBAAO,OAAO;AAAA,MACd,KAAK,WAAW;AAAA,IACjB;AAAA,EACD;AACD;AAzEa,mBACL,OAAO;", "names": []}