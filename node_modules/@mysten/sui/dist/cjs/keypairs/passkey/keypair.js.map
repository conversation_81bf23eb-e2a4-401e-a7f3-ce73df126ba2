{"version": 3, "sources": ["../../../../src/keypairs/passkey/keypair.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/bcs';\nimport { secp256r1 } from '@noble/curves/p256';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { randomBytes } from '@noble/hashes/utils';\nimport type {\n\tAuthenticationCredential,\n\tRegistrationCredential,\n} from '@simplewebauthn/typescript-types';\n\nimport { PasskeyAuthenticator } from '../../bcs/bcs.js';\nimport type { IntentScope, SignatureWithBytes } from '../../cryptography/index.js';\nimport { messageWithIntent, SIGNATURE_SCHEME_TO_FLAG, Signer } from '../../cryptography/index.js';\nimport type { PublicKey } from '../../cryptography/publickey.js';\nimport type { SignatureScheme } from '../../cryptography/signature-scheme.js';\nimport {\n\tparseDerSPKI,\n\tPASSKEY_PUBLIC_KEY_SIZE,\n\tPASSKEY_SIGNATURE_SIZE,\n\tPasskeyPublicKey,\n} from './publickey.js';\n\ntype DeepPartialConfigKeys = 'rp' | 'user' | 'authenticatorSelection';\n\ntype DeepPartial<T> = T extends object\n\t? {\n\t\t\t[P in keyof T]?: DeepPartial<T[P]>;\n\t\t}\n\t: T;\n\nexport type BrowserPasswordProviderOptions = Pick<\n\tDeepPartial<PublicKeyCredentialCreationOptions>,\n\tDeepPartialConfigKeys\n> &\n\tOmit<\n\t\tPartial<PublicKeyCredentialCreationOptions>,\n\t\tDeepPartialConfigKeys | 'pubKeyCredParams' | 'challenge'\n\t>;\n\nexport interface PasskeyProvider {\n\tcreate(): Promise<RegistrationCredential>;\n\tget(challenge: Uint8Array): Promise<AuthenticationCredential>;\n}\n\n// Default browser implementation\nexport class BrowserPasskeyProvider implements PasskeyProvider {\n\t#name: string;\n\t#options: BrowserPasswordProviderOptions;\n\n\tconstructor(name: string, options: BrowserPasswordProviderOptions) {\n\t\tthis.#name = name;\n\t\tthis.#options = options;\n\t}\n\n\tasync create(): Promise<RegistrationCredential> {\n\t\treturn (await navigator.credentials.create({\n\t\t\tpublicKey: {\n\t\t\t\ttimeout: this.#options.timeout ?? 60000,\n\t\t\t\t...this.#options,\n\t\t\t\trp: {\n\t\t\t\t\tname: this.#name,\n\t\t\t\t\t...this.#options.rp,\n\t\t\t\t},\n\t\t\t\tuser: {\n\t\t\t\t\tname: this.#name,\n\t\t\t\t\tdisplayName: this.#name,\n\t\t\t\t\t...this.#options.user,\n\t\t\t\t\tid: randomBytes(10),\n\t\t\t\t},\n\t\t\t\tchallenge: new TextEncoder().encode('Create passkey wallet on Sui'),\n\t\t\t\tpubKeyCredParams: [{ alg: -7, type: 'public-key' }],\n\t\t\t\tauthenticatorSelection: {\n\t\t\t\t\tauthenticatorAttachment: 'cross-platform',\n\t\t\t\t\tresidentKey: 'required',\n\t\t\t\t\trequireResidentKey: true,\n\t\t\t\t\tuserVerification: 'required',\n\t\t\t\t\t...this.#options.authenticatorSelection,\n\t\t\t\t},\n\t\t\t},\n\t\t})) as RegistrationCredential;\n\t}\n\n\tasync get(challenge: Uint8Array): Promise<AuthenticationCredential> {\n\t\treturn (await navigator.credentials.get({\n\t\t\tpublicKey: {\n\t\t\t\tchallenge,\n\t\t\t\tuserVerification: this.#options.authenticatorSelection?.userVerification || 'required',\n\t\t\t\ttimeout: this.#options.timeout ?? 60000,\n\t\t\t},\n\t\t})) as AuthenticationCredential;\n\t}\n}\n\n/**\n * @experimental\n * A passkey signer used for signing transactions. This is a client side implementation for [SIP-9](https://github.com/sui-foundation/sips/blob/main/sips/sip-9.md).\n */\nexport class PasskeyKeypair extends Signer {\n\tprivate publicKey: Uint8Array;\n\tprivate provider: PasskeyProvider;\n\n\t/**\n\t * Get the key scheme of passkey,\n\t */\n\tgetKeyScheme(): SignatureScheme {\n\t\treturn 'Passkey';\n\t}\n\n\t/**\n\t * Creates an instance of Passkey signer. It's expected to call the static `getPasskeyInstance` method to create an instance.\n\t * For example:\n\t * ```\n\t * const signer = await PasskeyKeypair.getPasskeyInstance();\n\t * ```\n\t */\n\tconstructor(publicKey: Uint8Array, provider: PasskeyProvider) {\n\t\tsuper();\n\t\tthis.publicKey = publicKey;\n\t\tthis.provider = provider;\n\t}\n\n\t/**\n\t * Creates an instance of Passkey signer invoking the passkey from navigator.\n\t */\n\tstatic async getPasskeyInstance(provider: PasskeyProvider): Promise<PasskeyKeypair> {\n\t\t// create a passkey secp256r1 with the provider.\n\t\tconst credential = await provider.create();\n\n\t\tif (!credential.response.getPublicKey()) {\n\t\t\tthrow new Error('Invalid credential create response');\n\t\t} else {\n\t\t\tconst derSPKI = credential.response.getPublicKey()!;\n\t\t\tconst pubkeyUncompressed = parseDerSPKI(new Uint8Array(derSPKI));\n\t\t\tconst pubkey = secp256r1.ProjectivePoint.fromHex(pubkeyUncompressed);\n\t\t\tconst pubkeyCompressed = pubkey.toRawBytes(true);\n\t\t\treturn new PasskeyKeypair(pubkeyCompressed, provider);\n\t\t}\n\t}\n\n\t/**\n\t * Return the public key for this passkey.\n\t */\n\tgetPublicKey(): PublicKey {\n\t\treturn new PasskeyPublicKey(this.publicKey);\n\t}\n\n\t/**\n\t * Return the signature for the provided data (i.e. blake2b(intent_message)).\n\t * This is sent to passkey as the challenge field.\n\t */\n\tasync sign(data: Uint8Array) {\n\t\t// sendss the passkey to sign over challenge as the data.\n\t\tconst credential = await this.provider.get(data);\n\n\t\t// parse authenticatorData (as bytes), clientDataJSON (decoded as string).\n\t\tconst authenticatorData = new Uint8Array(credential.response.authenticatorData);\n\t\tconst clientDataJSON = new Uint8Array(credential.response.clientDataJSON); // response.clientDataJSON is already UTF-8 encoded JSON\n\t\tconst decoder = new TextDecoder();\n\t\tconst clientDataJSONString: string = decoder.decode(clientDataJSON);\n\n\t\t// parse the signature from DER format, normalize and convert to compressed format (33 bytes).\n\t\tconst sig = secp256r1.Signature.fromDER(new Uint8Array(credential.response.signature));\n\t\tconst normalized = sig.normalizeS().toCompactRawBytes();\n\n\t\tif (\n\t\t\tnormalized.length !== PASSKEY_SIGNATURE_SIZE ||\n\t\t\tthis.publicKey.length !== PASSKEY_PUBLIC_KEY_SIZE\n\t\t) {\n\t\t\tthrow new Error('Invalid signature or public key length');\n\t\t}\n\n\t\t// construct userSignature as flag || sig || pubkey for the secp256r1 signature.\n\t\tconst arr = new Uint8Array(1 + normalized.length + this.publicKey.length);\n\t\tarr.set([SIGNATURE_SCHEME_TO_FLAG['Secp256r1']]);\n\t\tarr.set(normalized, 1);\n\t\tarr.set(this.publicKey, 1 + normalized.length);\n\n\t\t// serialize all fields into a passkey signature according to https://github.com/sui-foundation/sips/blob/main/sips/sip-9.md#signature-encoding\n\t\treturn PasskeyAuthenticator.serialize({\n\t\t\tauthenticatorData: authenticatorData,\n\t\t\tclientDataJson: clientDataJSONString,\n\t\t\tuserSignature: arr,\n\t\t}).toBytes();\n\t}\n\n\t/**\n\t * This overrides the base class implementation that accepts the raw bytes and signs its\n\t * digest of the intent message, then serialize it with the passkey flag.\n\t */\n\tasync signWithIntent(bytes: Uint8Array, intent: IntentScope): Promise<SignatureWithBytes> {\n\t\t// prepend it into an intent message and computes the digest.\n\t\tconst intentMessage = messageWithIntent(intent, bytes);\n\t\tconst digest = blake2b(intentMessage, { dkLen: 32 });\n\n\t\t// sign the digest.\n\t\tconst signature = await this.sign(digest);\n\n\t\t// prepend with the passkey flag.\n\t\tconst serializedSignature = new Uint8Array(1 + signature.length);\n\t\tserializedSignature.set([SIGNATURE_SCHEME_TO_FLAG[this.getKeyScheme()]]);\n\t\tserializedSignature.set(signature, 1);\n\t\treturn {\n\t\t\tsignature: toBase64(serializedSignature),\n\t\t\tbytes: toBase64(bytes),\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAyB;AACzB,kBAA0B;AAC1B,qBAAwB;AACxB,mBAA4B;AAM5B,IAAAA,cAAqC;AAErC,0BAAoE;AAGpE,uBAKO;AAtBP;AA+CO,MAAM,uBAAkD;AAAA,EAI9D,YAAY,MAAc,SAAyC;AAHnE;AACA;AAGC,uBAAK,OAAQ;AACb,uBAAK,UAAW;AAAA,EACjB;AAAA,EAEA,MAAM,SAA0C;AAC/C,WAAQ,MAAM,UAAU,YAAY,OAAO;AAAA,MAC1C,WAAW;AAAA,QACV,SAAS,mBAAK,UAAS,WAAW;AAAA,QAClC,GAAG,mBAAK;AAAA,QACR,IAAI;AAAA,UACH,MAAM,mBAAK;AAAA,UACX,GAAG,mBAAK,UAAS;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,UACL,MAAM,mBAAK;AAAA,UACX,aAAa,mBAAK;AAAA,UAClB,GAAG,mBAAK,UAAS;AAAA,UACjB,QAAI,0BAAY,EAAE;AAAA,QACnB;AAAA,QACA,WAAW,IAAI,YAAY,EAAE,OAAO,8BAA8B;AAAA,QAClE,kBAAkB,CAAC,EAAE,KAAK,IAAI,MAAM,aAAa,CAAC;AAAA,QAClD,wBAAwB;AAAA,UACvB,yBAAyB;AAAA,UACzB,aAAa;AAAA,UACb,oBAAoB;AAAA,UACpB,kBAAkB;AAAA,UAClB,GAAG,mBAAK,UAAS;AAAA,QAClB;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,IAAI,WAA0D;AACnE,WAAQ,MAAM,UAAU,YAAY,IAAI;AAAA,MACvC,WAAW;AAAA,QACV;AAAA,QACA,kBAAkB,mBAAK,UAAS,wBAAwB,oBAAoB;AAAA,QAC5E,SAAS,mBAAK,UAAS,WAAW;AAAA,MACnC;AAAA,IACD,CAAC;AAAA,EACF;AACD;AA7CC;AACA;AAkDM,MAAM,uBAAuB,2BAAO;AAAA;AAAA;AAAA;AAAA,EAO1C,eAAgC;AAC/B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,WAAuB,UAA2B;AAC7D,UAAM;AACN,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,mBAAmB,UAAoD;AAEnF,UAAM,aAAa,MAAM,SAAS,OAAO;AAEzC,QAAI,CAAC,WAAW,SAAS,aAAa,GAAG;AACxC,YAAM,IAAI,MAAM,oCAAoC;AAAA,IACrD,OAAO;AACN,YAAM,UAAU,WAAW,SAAS,aAAa;AACjD,YAAM,yBAAqB,+BAAa,IAAI,WAAW,OAAO,CAAC;AAC/D,YAAM,SAAS,sBAAU,gBAAgB,QAAQ,kBAAkB;AACnE,YAAM,mBAAmB,OAAO,WAAW,IAAI;AAC/C,aAAO,IAAI,eAAe,kBAAkB,QAAQ;AAAA,IACrD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,eAA0B;AACzB,WAAO,IAAI,kCAAiB,KAAK,SAAS;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,KAAK,MAAkB;AAE5B,UAAM,aAAa,MAAM,KAAK,SAAS,IAAI,IAAI;AAG/C,UAAM,oBAAoB,IAAI,WAAW,WAAW,SAAS,iBAAiB;AAC9E,UAAM,iBAAiB,IAAI,WAAW,WAAW,SAAS,cAAc;AACxE,UAAM,UAAU,IAAI,YAAY;AAChC,UAAM,uBAA+B,QAAQ,OAAO,cAAc;AAGlE,UAAM,MAAM,sBAAU,UAAU,QAAQ,IAAI,WAAW,WAAW,SAAS,SAAS,CAAC;AACrF,UAAM,aAAa,IAAI,WAAW,EAAE,kBAAkB;AAEtD,QACC,WAAW,WAAW,2CACtB,KAAK,UAAU,WAAW,0CACzB;AACD,YAAM,IAAI,MAAM,wCAAwC;AAAA,IACzD;AAGA,UAAM,MAAM,IAAI,WAAW,IAAI,WAAW,SAAS,KAAK,UAAU,MAAM;AACxE,QAAI,IAAI,CAAC,6CAAyB,WAAW,CAAC,CAAC;AAC/C,QAAI,IAAI,YAAY,CAAC;AACrB,QAAI,IAAI,KAAK,WAAW,IAAI,WAAW,MAAM;AAG7C,WAAO,iCAAqB,UAAU;AAAA,MACrC;AAAA,MACA,gBAAgB;AAAA,MAChB,eAAe;AAAA,IAChB,CAAC,EAAE,QAAQ;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,eAAe,OAAmB,QAAkD;AAEzF,UAAM,oBAAgB,uCAAkB,QAAQ,KAAK;AACrD,UAAM,aAAS,wBAAQ,eAAe,EAAE,OAAO,GAAG,CAAC;AAGnD,UAAM,YAAY,MAAM,KAAK,KAAK,MAAM;AAGxC,UAAM,sBAAsB,IAAI,WAAW,IAAI,UAAU,MAAM;AAC/D,wBAAoB,IAAI,CAAC,6CAAyB,KAAK,aAAa,CAAC,CAAC,CAAC;AACvE,wBAAoB,IAAI,WAAW,CAAC;AACpC,WAAO;AAAA,MACN,eAAW,qBAAS,mBAAmB;AAAA,MACvC,WAAO,qBAAS,KAAK;AAAA,IACtB;AAAA,EACD;AACD;", "names": ["import_bcs"]}