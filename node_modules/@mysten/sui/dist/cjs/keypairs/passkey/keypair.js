"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __typeError = (msg) => {
  throw TypeError(msg);
};
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var __accessCheck = (obj, member, msg) => member.has(obj) || __typeError("Cannot " + msg);
var __privateGet = (obj, member, getter) => (__accessCheck(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd = (obj, member, value) => member.has(obj) ? __typeError("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);
var __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, "write to private field"), setter ? setter.call(obj, value) : member.set(obj, value), value);
var keypair_exports = {};
__export(keypair_exports, {
  BrowserPasskeyProvider: () => BrowserPasskeyProvider,
  PasskeyKeypair: () => PasskeyKeypair
});
module.exports = __toCommonJS(keypair_exports);
var import_bcs = require("@mysten/bcs");
var import_p256 = require("@noble/curves/p256");
var import_blake2b = require("@noble/hashes/blake2b");
var import_utils = require("@noble/hashes/utils");
var import_bcs2 = require("../../bcs/bcs.js");
var import_cryptography = require("../../cryptography/index.js");
var import_publickey = require("./publickey.js");
var _name, _options;
class BrowserPasskeyProvider {
  constructor(name, options) {
    __privateAdd(this, _name);
    __privateAdd(this, _options);
    __privateSet(this, _name, name);
    __privateSet(this, _options, options);
  }
  async create() {
    return await navigator.credentials.create({
      publicKey: {
        timeout: __privateGet(this, _options).timeout ?? 6e4,
        ...__privateGet(this, _options),
        rp: {
          name: __privateGet(this, _name),
          ...__privateGet(this, _options).rp
        },
        user: {
          name: __privateGet(this, _name),
          displayName: __privateGet(this, _name),
          ...__privateGet(this, _options).user,
          id: (0, import_utils.randomBytes)(10)
        },
        challenge: new TextEncoder().encode("Create passkey wallet on Sui"),
        pubKeyCredParams: [{ alg: -7, type: "public-key" }],
        authenticatorSelection: {
          authenticatorAttachment: "cross-platform",
          residentKey: "required",
          requireResidentKey: true,
          userVerification: "required",
          ...__privateGet(this, _options).authenticatorSelection
        }
      }
    });
  }
  async get(challenge) {
    return await navigator.credentials.get({
      publicKey: {
        challenge,
        userVerification: __privateGet(this, _options).authenticatorSelection?.userVerification || "required",
        timeout: __privateGet(this, _options).timeout ?? 6e4
      }
    });
  }
}
_name = new WeakMap();
_options = new WeakMap();
class PasskeyKeypair extends import_cryptography.Signer {
  /**
   * Get the key scheme of passkey,
   */
  getKeyScheme() {
    return "Passkey";
  }
  /**
   * Creates an instance of Passkey signer. It's expected to call the static `getPasskeyInstance` method to create an instance.
   * For example:
   * ```
   * const signer = await PasskeyKeypair.getPasskeyInstance();
   * ```
   */
  constructor(publicKey, provider) {
    super();
    this.publicKey = publicKey;
    this.provider = provider;
  }
  /**
   * Creates an instance of Passkey signer invoking the passkey from navigator.
   */
  static async getPasskeyInstance(provider) {
    const credential = await provider.create();
    if (!credential.response.getPublicKey()) {
      throw new Error("Invalid credential create response");
    } else {
      const derSPKI = credential.response.getPublicKey();
      const pubkeyUncompressed = (0, import_publickey.parseDerSPKI)(new Uint8Array(derSPKI));
      const pubkey = import_p256.secp256r1.ProjectivePoint.fromHex(pubkeyUncompressed);
      const pubkeyCompressed = pubkey.toRawBytes(true);
      return new PasskeyKeypair(pubkeyCompressed, provider);
    }
  }
  /**
   * Return the public key for this passkey.
   */
  getPublicKey() {
    return new import_publickey.PasskeyPublicKey(this.publicKey);
  }
  /**
   * Return the signature for the provided data (i.e. blake2b(intent_message)).
   * This is sent to passkey as the challenge field.
   */
  async sign(data) {
    const credential = await this.provider.get(data);
    const authenticatorData = new Uint8Array(credential.response.authenticatorData);
    const clientDataJSON = new Uint8Array(credential.response.clientDataJSON);
    const decoder = new TextDecoder();
    const clientDataJSONString = decoder.decode(clientDataJSON);
    const sig = import_p256.secp256r1.Signature.fromDER(new Uint8Array(credential.response.signature));
    const normalized = sig.normalizeS().toCompactRawBytes();
    if (normalized.length !== import_publickey.PASSKEY_SIGNATURE_SIZE || this.publicKey.length !== import_publickey.PASSKEY_PUBLIC_KEY_SIZE) {
      throw new Error("Invalid signature or public key length");
    }
    const arr = new Uint8Array(1 + normalized.length + this.publicKey.length);
    arr.set([import_cryptography.SIGNATURE_SCHEME_TO_FLAG["Secp256r1"]]);
    arr.set(normalized, 1);
    arr.set(this.publicKey, 1 + normalized.length);
    return import_bcs2.PasskeyAuthenticator.serialize({
      authenticatorData,
      clientDataJson: clientDataJSONString,
      userSignature: arr
    }).toBytes();
  }
  /**
   * This overrides the base class implementation that accepts the raw bytes and signs its
   * digest of the intent message, then serialize it with the passkey flag.
   */
  async signWithIntent(bytes, intent) {
    const intentMessage = (0, import_cryptography.messageWithIntent)(intent, bytes);
    const digest = (0, import_blake2b.blake2b)(intentMessage, { dkLen: 32 });
    const signature = await this.sign(digest);
    const serializedSignature = new Uint8Array(1 + signature.length);
    serializedSignature.set([import_cryptography.SIGNATURE_SCHEME_TO_FLAG[this.getKeyScheme()]]);
    serializedSignature.set(signature, 1);
    return {
      signature: (0, import_bcs.toBase64)(serializedSignature),
      bytes: (0, import_bcs.toBase64)(bytes)
    };
  }
}
//# sourceMappingURL=keypair.js.map
