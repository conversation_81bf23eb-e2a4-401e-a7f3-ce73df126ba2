{"version": 3, "sources": ["../../../../src/keypairs/secp256r1/keypair.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { secp256r1 } from '@noble/curves/p256';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { sha256 } from '@noble/hashes/sha256';\nimport { bytesToHex } from '@noble/hashes/utils';\nimport { HDKey } from '@scure/bip32';\n\nimport { decodeSuiPrivateKey, encodeSuiPrivate<PERSON><PERSON>, Keypair } from '../../cryptography/keypair.js';\nimport { isValidBIP32Path, mnemonicToSeed } from '../../cryptography/mnemonics.js';\nimport type { PublicKey } from '../../cryptography/publickey.js';\nimport type { SignatureScheme } from '../../cryptography/signature-scheme.js';\nimport { Secp256r1PublicKey } from './publickey.js';\n\nexport const DEFAULT_SECP256R1_DERIVATION_PATH = \"m/74'/784'/0'/0/0\";\n\n/**\n * Secp256r1 Keypair data\n */\nexport interface Secp256r1KeypairData {\n\tpublicKey: Uint8Array;\n\tsecretKey: Uint8Array;\n}\n\n/**\n * An Secp256r1 Keypair used for signing transactions.\n */\nexport class Secp256r1Keypair extends Keypair {\n\tprivate keypair: Secp256r1KeypairData;\n\n\t/**\n\t * Create a new keypair instance.\n\t * Generate random keypair if no {@link Secp256r1Keypair} is provided.\n\t *\n\t * @param keypair Secp256r1 keypair\n\t */\n\tconstructor(keypair?: Secp256r1KeypairData) {\n\t\tsuper();\n\t\tif (keypair) {\n\t\t\tthis.keypair = keypair;\n\t\t} else {\n\t\t\tconst secretKey: Uint8Array = secp256r1.utils.randomPrivateKey();\n\t\t\tconst publicKey: Uint8Array = secp256r1.getPublicKey(secretKey, true);\n\n\t\t\tthis.keypair = { publicKey, secretKey };\n\t\t}\n\t}\n\n\t/**\n\t * Get the key scheme of the keypair Secp256r1\n\t */\n\tgetKeyScheme(): SignatureScheme {\n\t\treturn 'Secp256r1';\n\t}\n\n\t/**\n\t * Generate a new random keypair\n\t */\n\tstatic generate(): Secp256r1Keypair {\n\t\treturn new Secp256r1Keypair();\n\t}\n\n\t/**\n\t * Create a keypair from a raw secret key byte array.\n\t *\n\t * This method should only be used to recreate a keypair from a previously\n\t * generated secret key. Generating keypairs from a random seed should be done\n\t * with the {@link Keypair.fromSeed} method.\n\t *\n\t * @throws error if the provided secret key is invalid and validation is not skipped.\n\t *\n\t * @param secretKey secret key byte array or Bech32 secret key string\n\t * @param options: skip secret key validation\n\t */\n\n\tstatic fromSecretKey(\n\t\tsecretKey: Uint8Array | string,\n\t\toptions?: { skipValidation?: boolean },\n\t): Secp256r1Keypair {\n\t\tif (typeof secretKey === 'string') {\n\t\t\tconst decoded = decodeSuiPrivateKey(secretKey);\n\n\t\t\tif (decoded.schema !== 'Secp256r1') {\n\t\t\t\tthrow new Error(`Expected a Secp256r1 keypair, got ${decoded.schema}`);\n\t\t\t}\n\n\t\t\treturn this.fromSecretKey(decoded.secretKey, options);\n\t\t}\n\n\t\tconst publicKey: Uint8Array = secp256r1.getPublicKey(secretKey, true);\n\t\tif (!options || !options.skipValidation) {\n\t\t\tconst encoder = new TextEncoder();\n\t\t\tconst signData = encoder.encode('sui validation');\n\t\t\tconst msgHash = bytesToHex(blake2b(signData, { dkLen: 32 }));\n\t\t\tconst signature = secp256r1.sign(msgHash, secretKey, { lowS: true });\n\t\t\tif (!secp256r1.verify(signature, msgHash, publicKey, { lowS: true })) {\n\t\t\t\tthrow new Error('Provided secretKey is invalid');\n\t\t\t}\n\t\t}\n\t\treturn new Secp256r1Keypair({ publicKey, secretKey });\n\t}\n\n\t/**\n\t * Generate a keypair from a 32 byte seed.\n\t *\n\t * @param seed seed byte array\n\t */\n\tstatic fromSeed(seed: Uint8Array): Secp256r1Keypair {\n\t\tlet publicKey = secp256r1.getPublicKey(seed, true);\n\t\treturn new Secp256r1Keypair({ publicKey, secretKey: seed });\n\t}\n\n\t/**\n\t * The public key for this keypair\n\t */\n\tgetPublicKey(): PublicKey {\n\t\treturn new Secp256r1PublicKey(this.keypair.publicKey);\n\t}\n\n\t/**\n\t * The Bech32 secret key string for this Secp256r1 keypair\n\t */\n\tgetSecretKey(): string {\n\t\treturn encodeSuiPrivateKey(this.keypair.secretKey, this.getKeyScheme());\n\t}\n\n\t/**\n\t * Return the signature for the provided data.\n\t */\n\tasync sign(data: Uint8Array) {\n\t\tconst msgHash = sha256(data);\n\t\tconst sig = secp256r1.sign(msgHash, this.keypair.secretKey, {\n\t\t\tlowS: true,\n\t\t});\n\t\treturn sig.toCompactRawBytes();\n\t}\n\n\t/**\n\t * Derive Secp256r1 keypair from mnemonics and path. The mnemonics must be normalized\n\t * and validated against the english wordlist.\n\t *\n\t * If path is none, it will default to m/74'/784'/0'/0/0, otherwise the path must\n\t * be compliant to BIP-32 in form m/74'/784'/{account_index}'/{change_index}/{address_index}.\n\t */\n\tstatic deriveKeypair(mnemonics: string, path?: string): Secp256r1Keypair {\n\t\tif (path == null) {\n\t\t\tpath = DEFAULT_SECP256R1_DERIVATION_PATH;\n\t\t}\n\t\tif (!isValidBIP32Path(path)) {\n\t\t\tthrow new Error('Invalid derivation path');\n\t\t}\n\t\t// We use HDKey which is hardcoded to use Secp256k1 but since we only need the 32 bytes for the private key it's okay to use here as well.\n\t\tconst privateKey = HDKey.fromMasterSeed(mnemonicToSeed(mnemonics)).derive(path).privateKey;\n\t\treturn Secp256r1Keypair.fromSecretKey(privateKey!);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,kBAA0B;AAC1B,qBAAwB;AACxB,oBAAuB;AACvB,mBAA2B;AAC3B,mBAAsB;AAEtB,qBAAkE;AAClE,uBAAiD;AAGjD,uBAAmC;AAE5B,MAAM,oCAAoC;AAa1C,MAAM,yBAAyB,uBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS7C,YAAY,SAAgC;AAC3C,UAAM;AACN,QAAI,SAAS;AACZ,WAAK,UAAU;AAAA,IAChB,OAAO;AACN,YAAM,YAAwB,sBAAU,MAAM,iBAAiB;AAC/D,YAAM,YAAwB,sBAAU,aAAa,WAAW,IAAI;AAEpE,WAAK,UAAU,EAAE,WAAW,UAAU;AAAA,IACvC;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAgC;AAC/B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAA6B;AACnC,WAAO,IAAI,iBAAiB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,OAAO,cACN,WACA,SACmB;AACnB,QAAI,OAAO,cAAc,UAAU;AAClC,YAAM,cAAU,oCAAoB,SAAS;AAE7C,UAAI,QAAQ,WAAW,aAAa;AACnC,cAAM,IAAI,MAAM,qCAAqC,QAAQ,MAAM,EAAE;AAAA,MACtE;AAEA,aAAO,KAAK,cAAc,QAAQ,WAAW,OAAO;AAAA,IACrD;AAEA,UAAM,YAAwB,sBAAU,aAAa,WAAW,IAAI;AACpE,QAAI,CAAC,WAAW,CAAC,QAAQ,gBAAgB;AACxC,YAAM,UAAU,IAAI,YAAY;AAChC,YAAM,WAAW,QAAQ,OAAO,gBAAgB;AAChD,YAAM,cAAU,6BAAW,wBAAQ,UAAU,EAAE,OAAO,GAAG,CAAC,CAAC;AAC3D,YAAM,YAAY,sBAAU,KAAK,SAAS,WAAW,EAAE,MAAM,KAAK,CAAC;AACnE,UAAI,CAAC,sBAAU,OAAO,WAAW,SAAS,WAAW,EAAE,MAAM,KAAK,CAAC,GAAG;AACrE,cAAM,IAAI,MAAM,+BAA+B;AAAA,MAChD;AAAA,IACD;AACA,WAAO,IAAI,iBAAiB,EAAE,WAAW,UAAU,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,MAAoC;AACnD,QAAI,YAAY,sBAAU,aAAa,MAAM,IAAI;AACjD,WAAO,IAAI,iBAAiB,EAAE,WAAW,WAAW,KAAK,CAAC;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAKA,eAA0B;AACzB,WAAO,IAAI,oCAAmB,KAAK,QAAQ,SAAS;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAuB;AACtB,eAAO,oCAAoB,KAAK,QAAQ,WAAW,KAAK,aAAa,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,KAAK,MAAkB;AAC5B,UAAM,cAAU,sBAAO,IAAI;AAC3B,UAAM,MAAM,sBAAU,KAAK,SAAS,KAAK,QAAQ,WAAW;AAAA,MAC3D,MAAM;AAAA,IACP,CAAC;AACD,WAAO,IAAI,kBAAkB;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,cAAc,WAAmB,MAAiC;AACxE,QAAI,QAAQ,MAAM;AACjB,aAAO;AAAA,IACR;AACA,QAAI,KAAC,mCAAiB,IAAI,GAAG;AAC5B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC1C;AAEA,UAAM,aAAa,mBAAM,mBAAe,iCAAe,SAAS,CAAC,EAAE,OAAO,IAAI,EAAE;AAChF,WAAO,iBAAiB,cAAc,UAAW;AAAA,EAClD;AACD;", "names": []}