{"version": 3, "sources": ["../../../../src/client/types/params.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n *  ######################################\n *  ### DO NOT EDIT THIS FILE DIRECTLY ###\n *  ######################################\n *\n * This file is generated from:\n * /crates/sui-open-rpc/spec/openrpc.json\n */\n\nimport type { Transaction } from '../../transactions/index.js';\nimport type * as RpcTypes from './generated.js';\n\n/**\n * Runs the transaction in dev-inspect mode. Which allows for nearly any transaction (or Move call)\n * with any arguments. Detailed results are provided, including both the transaction effects and any\n * return values.\n */\nexport interface DevInspectTransactionBlockParams {\n\tsender: string;\n\t/** BCS encoded TransactionKind(as opposed to TransactionData, which include gasBudget and gasPrice) */\n\ttransactionBlock: Transaction | Uint8Array | string;\n\t/** Gas is not charged, but gas usage is still calculated. Default to use reference gas price */\n\tgasPrice?: bigint | number | null | undefined;\n\t/** The epoch to perform the call. Will be set from the system state object if not provided */\n\tepoch?: string | null | undefined;\n\t/** Additional arguments including gas_budget, gas_objects, gas_sponsor and skip_checks. */\n\tadditionalArgs?: RpcTypes.DevInspectArgs | null | undefined;\n}\n/**\n * Return transaction execution effects including the gas cost summary, while the effects are not\n * committed to the chain.\n */\nexport interface DryRunTransactionBlockParams {\n\ttransactionBlock: Uint8Array | string;\n}\n/**\n * Execute the transaction and wait for results if desired. Request types: 1. WaitForEffectsCert: waits\n * for TransactionEffectsCert and then return to client. This mode is a proxy for transaction\n * finality. 2. WaitForLocalExecution: waits for TransactionEffectsCert and make sure the node executed\n * the transaction locally before returning the client. The local execution makes sure this node is\n * aware of this transaction when client fires subsequent queries. However if the node fails to execute\n * the transaction locally in a timely manner, a bool type in the response is set to false to indicated\n * the case. request_type is default to be `WaitForEffectsCert` unless options.show_events or\n * options.show_effects is true\n */\nexport interface ExecuteTransactionBlockParams {\n\t/** BCS serialized transaction data bytes without its type tag, as base-64 encoded string. */\n\ttransactionBlock: Uint8Array | string;\n\t/**\n\t * A list of signatures (`flag || signature || pubkey` bytes, as base-64 encoded string). Signature is\n\t * committed to the intent message of the transaction data, as base-64 encoded string.\n\t */\n\tsignature: string | string[];\n\t/** options for specifying the content to be returned */\n\toptions?: RpcTypes.SuiTransactionBlockResponseOptions | null | undefined;\n\t/** @deprecated requestType will be ignored by JSON RPC in the future */\n\trequestType?: RpcTypes.ExecuteTransactionRequestType | null | undefined;\n}\n/** Return the first four bytes of the chain's genesis checkpoint digest. */\nexport interface GetChainIdentifierParams {}\n/** Return a checkpoint */\nexport interface GetCheckpointParams {\n\t/** Checkpoint identifier, can use either checkpoint digest, or checkpoint sequence number as input. */\n\tid: RpcTypes.CheckpointId;\n}\n/** Return paginated list of checkpoints */\nexport interface GetCheckpointsParams {\n\t/**\n\t * An optional paging cursor. If provided, the query will start from the next item after the specified\n\t * cursor. Default to start from the first item if not specified.\n\t */\n\tcursor?: string | null | undefined;\n\t/** Maximum item returned per page, default to [QUERY_MAX_RESULT_LIMIT_CHECKPOINTS] if not specified. */\n\tlimit?: number | null | undefined;\n\t/** query result ordering, default to false (ascending order), oldest record first. */\n\tdescendingOrder: boolean;\n}\n/** Return transaction events. */\nexport interface GetEventsParams {\n\t/** the event query criteria. */\n\ttransactionDigest: string;\n}\n/** Return the sequence number of the latest checkpoint that has been executed */\nexport interface GetLatestCheckpointSequenceNumberParams {}\n/** Return the argument types of a Move function, based on normalized Type. */\nexport interface GetMoveFunctionArgTypesParams {\n\tpackage: string;\n\tmodule: string;\n\tfunction: string;\n}\n/** Return a structured representation of Move function */\nexport interface GetNormalizedMoveFunctionParams {\n\tpackage: string;\n\tmodule: string;\n\tfunction: string;\n}\n/** Return a structured representation of Move module */\nexport interface GetNormalizedMoveModuleParams {\n\tpackage: string;\n\tmodule: string;\n}\n/** Return structured representations of all modules in the given package */\nexport interface GetNormalizedMoveModulesByPackageParams {\n\tpackage: string;\n}\n/** Return a structured representation of Move struct */\nexport interface GetNormalizedMoveStructParams {\n\tpackage: string;\n\tmodule: string;\n\tstruct: string;\n}\n/** Return the object information for a specified object */\nexport interface GetObjectParams {\n\t/** the ID of the queried object */\n\tid: string;\n\t/** options for specifying the content to be returned */\n\toptions?: RpcTypes.SuiObjectDataOptions | null | undefined;\n}\n/**\n * Return the protocol config table for the given version number. If the version number is not\n * specified, If none is specified, the node uses the version of the latest epoch it has processed.\n */\nexport interface GetProtocolConfigParams {\n\t/**\n\t * An optional protocol version specifier. If omitted, the latest protocol config table for the node\n\t * will be returned.\n\t */\n\tversion?: string | null | undefined;\n}\n/** Return the total number of transaction blocks known to the server. */\nexport interface GetTotalTransactionBlocksParams {}\n/** Return the transaction response object. */\nexport interface GetTransactionBlockParams {\n\t/** the digest of the queried transaction */\n\tdigest: string;\n\t/** options for specifying the content to be returned */\n\toptions?: RpcTypes.SuiTransactionBlockResponseOptions | null | undefined;\n}\n/** Return the object data for a list of objects */\nexport interface MultiGetObjectsParams {\n\t/** the IDs of the queried objects */\n\tids: string[];\n\t/** options for specifying the content to be returned */\n\toptions?: RpcTypes.SuiObjectDataOptions | null | undefined;\n}\n/**\n * Returns an ordered list of transaction responses The method will throw an error if the input\n * contains any duplicate or the input size exceeds QUERY_MAX_RESULT_LIMIT\n */\nexport interface MultiGetTransactionBlocksParams {\n\t/** A list of transaction digests. */\n\tdigests: string[];\n\t/** config options to control which fields to fetch */\n\toptions?: RpcTypes.SuiTransactionBlockResponseOptions | null | undefined;\n}\n/**\n * Note there is no software-level guarantee/SLA that objects with past versions can be retrieved by\n * this API, even if the object and version exists/existed. The result may vary across nodes depending\n * on their pruning policies. Return the object information for a specified version\n */\nexport interface TryGetPastObjectParams {\n\t/** the ID of the queried object */\n\tid: string;\n\t/** the version of the queried object. If None, default to the latest known version */\n\tversion: number;\n\t/** options for specifying the content to be returned */\n\toptions?: RpcTypes.SuiObjectDataOptions | null | undefined;\n}\n/**\n * Note there is no software-level guarantee/SLA that objects with past versions can be retrieved by\n * this API, even if the object and version exists/existed. The result may vary across nodes depending\n * on their pruning policies. Return the object information for a specified version\n */\nexport interface TryMultiGetPastObjectsParams {\n\t/** a vector of object and versions to be queried */\n\tpastObjects: RpcTypes.GetPastObjectRequest[];\n\t/** options for specifying the content to be returned */\n\toptions?: RpcTypes.SuiObjectDataOptions | null | undefined;\n}\n/** Return the total coin balance for all coin type, owned by the address owner. */\nexport interface GetAllBalancesParams {\n\t/** the owner's Sui address */\n\towner: string;\n}\n/** Return all Coin objects owned by an address. */\nexport interface GetAllCoinsParams {\n\t/** the owner's Sui address */\n\towner: string;\n\t/** optional paging cursor */\n\tcursor?: string | null | undefined;\n\t/** maximum number of items per page */\n\tlimit?: number | null | undefined;\n}\n/** Return the total coin balance for one coin type, owned by the address owner. */\nexport interface GetBalanceParams {\n\t/** the owner's Sui address */\n\towner: string;\n\t/**\n\t * optional type names for the coin (e.g., 0x168da5bf1f48dafc111b0a488fa454aca95e0b5e::usdc::USDC),\n\t * default to 0x2::sui::SUI if not specified.\n\t */\n\tcoinType?: string | null | undefined;\n}\n/**\n * Return metadata (e.g., symbol, decimals) for a coin. Note that if the coin's metadata was wrapped in\n * the transaction that published its marker type, or the latest version of the metadata object is\n * wrapped or deleted, it will not be found.\n */\nexport interface GetCoinMetadataParams {\n\t/** type name for the coin (e.g., 0x168da5bf1f48dafc111b0a488fa454aca95e0b5e::usdc::USDC) */\n\tcoinType: string;\n}\n/** Return all Coin<`coin_type`> objects owned by an address. */\nexport interface GetCoinsParams {\n\t/** the owner's Sui address */\n\towner: string;\n\t/**\n\t * optional type name for the coin (e.g., 0x168da5bf1f48dafc111b0a488fa454aca95e0b5e::usdc::USDC),\n\t * default to 0x2::sui::SUI if not specified.\n\t */\n\tcoinType?: string | null | undefined;\n\t/** optional paging cursor */\n\tcursor?: string | null | undefined;\n\t/** maximum number of items per page */\n\tlimit?: number | null | undefined;\n}\n/** Return the committee information for the asked `epoch`. */\nexport interface GetCommitteeInfoParams {\n\t/** The epoch of interest. If None, default to the latest epoch */\n\tepoch?: string | null | undefined;\n}\n/** Return the dynamic field object information for a specified object */\nexport interface GetDynamicFieldObjectParams {\n\t/** The ID of the queried parent object */\n\tparentId: string;\n\t/** The Name of the dynamic field */\n\tname: RpcTypes.DynamicFieldName;\n}\n/** Return the list of dynamic field objects owned by an object. */\nexport interface GetDynamicFieldsParams {\n\t/** The ID of the parent object */\n\tparentId: string;\n\t/**\n\t * An optional paging cursor. If provided, the query will start from the next item after the specified\n\t * cursor. Default to start from the first item if not specified.\n\t */\n\tcursor?: string | null | undefined;\n\t/** Maximum item returned per page, default to [QUERY_MAX_RESULT_LIMIT] if not specified. */\n\tlimit?: number | null | undefined;\n}\n/** Return the latest SUI system state object on-chain. */\nexport interface GetLatestSuiSystemStateParams {}\n/**\n * Return the list of objects owned by an address. Note that if the address owns more than\n * `QUERY_MAX_RESULT_LIMIT` objects, the pagination is not accurate, because previous page may have\n * been updated when the next page is fetched. Please use suix_queryObjects if this is a concern.\n */\nexport type GetOwnedObjectsParams = {\n\t/** the owner's Sui address */\n\towner: string;\n\t/**\n\t * An optional paging cursor. If provided, the query will start from the next item after the specified\n\t * cursor. Default to start from the first item if not specified.\n\t */\n\tcursor?: string | null | undefined;\n\t/** Max number of items returned per page, default to [QUERY_MAX_RESULT_LIMIT] if not specified. */\n\tlimit?: number | null | undefined;\n} & RpcTypes.SuiObjectResponseQuery;\n/** Return the reference gas price for the network */\nexport interface GetReferenceGasPriceParams {}\n/** Return all [DelegatedStake]. */\nexport interface GetStakesParams {\n\towner: string;\n}\n/** Return one or more [DelegatedStake]. If a Stake was withdrawn its status will be Unstaked. */\nexport interface GetStakesByIdsParams {\n\tstakedSuiIds: string[];\n}\n/** Return total supply for a coin */\nexport interface GetTotalSupplyParams {\n\t/** type name for the coin (e.g., 0x168da5bf1f48dafc111b0a488fa454aca95e0b5e::usdc::USDC) */\n\tcoinType: string;\n}\n/** Return the validator APY */\nexport interface GetValidatorsApyParams {}\n/** Return list of events for a specified query criteria. */\nexport interface QueryEventsParams {\n\t/**\n\t * The event query criteria. See [Event filter](https://docs.sui.io/build/event_api#event-filters)\n\t * documentation for examples.\n\t */\n\tquery: RpcTypes.SuiEventFilter;\n\t/** optional paging cursor */\n\tcursor?: RpcTypes.EventId | null | undefined;\n\t/** maximum number of items per page, default to [QUERY_MAX_RESULT_LIMIT] if not specified. */\n\tlimit?: number | null | undefined;\n\t/** query result ordering, default to false (ascending order), oldest record first. */\n\torder?: 'ascending' | 'descending' | null | undefined;\n}\n/** Return list of transactions for a specified query criteria. */\nexport type QueryTransactionBlocksParams = {\n\t/**\n\t * An optional paging cursor. If provided, the query will start from the next item after the specified\n\t * cursor. Default to start from the first item if not specified.\n\t */\n\tcursor?: string | null | undefined;\n\t/** Maximum item returned per page, default to QUERY_MAX_RESULT_LIMIT if not specified. */\n\tlimit?: number | null | undefined;\n\t/** query result ordering, default to false (ascending order), oldest record first. */\n\torder?: 'ascending' | 'descending' | null | undefined;\n} & RpcTypes.SuiTransactionBlockResponseQuery;\n/** Return the resolved address given resolver and name */\nexport interface ResolveNameServiceAddressParams {\n\t/** The name to resolve */\n\tname: string;\n}\n/**\n * Return the resolved names given address, if multiple names are resolved, the first one is the\n * primary name.\n */\nexport interface ResolveNameServiceNamesParams {\n\t/** The address to resolve */\n\taddress: string;\n\tcursor?: string | null | undefined;\n\tlimit?: number | null | undefined;\n}\n/** Subscribe to a stream of Sui event */\nexport interface SubscribeEventParams {\n\t/**\n\t * The filter criteria of the event stream. See\n\t * [Event filter](https://docs.sui.io/build/event_api#event-filters) documentation for examples.\n\t */\n\tfilter: RpcTypes.SuiEventFilter;\n}\n/** Subscribe to a stream of Sui transaction effects */\nexport interface SubscribeTransactionParams {\n\tfilter: RpcTypes.TransactionFilter;\n}\n/** Create an unsigned batched transaction. */\nexport interface UnsafeBatchTransactionParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** list of transaction request parameters */\n\tsingleTransactionParams: RpcTypes.RPCTransactionRequestParams[];\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n\t/** Whether this is a regular transaction or a Dev Inspect Transaction */\n\ttxnBuilderMode?: RpcTypes.SuiTransactionBlockBuilderMode | null | undefined;\n}\n/** Create an unsigned transaction to merge multiple coins into one coin. */\nexport interface UnsafeMergeCoinsParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the coin object to merge into, this coin will remain after the transaction */\n\tprimaryCoin: string;\n\t/**\n\t * the coin object to be merged, this coin will be destroyed, the balance will be added to\n\t * `primary_coin`\n\t */\n\tcoinToMerge: string;\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/**\n * Create an unsigned transaction to execute a Move call on the network, by calling the specified\n * function in the module of a given package.\n */\nexport interface UnsafeMoveCallParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the Move package ID, e.g. `0x2` */\n\tpackageObjectId: string;\n\t/** the Move module name, e.g. `pay` */\n\tmodule: string;\n\t/** the move function name, e.g. `split` */\n\tfunction: string;\n\t/** the type arguments of the Move function */\n\ttypeArguments: string[];\n\t/**\n\t * the arguments to be passed into the Move function, in [SuiJson](https://docs.sui.io/build/sui-json)\n\t * format\n\t */\n\targuments: unknown[];\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n\t/**\n\t * Whether this is a Normal transaction or a Dev Inspect Transaction. Default to be\n\t * `SuiTransactionBlockBuilderMode::Commit` when it's None.\n\t */\n\texecutionMode?: RpcTypes.SuiTransactionBlockBuilderMode | null | undefined;\n}\n/**\n * Send `Coin<T>` to a list of addresses, where `T` can be any coin type, following a list of amounts,\n * The object specified in the `gas` field will be used to pay the gas fee for the transaction. The gas\n * object can not appear in `input_coins`. If the gas object is not specified, the RPC server will\n * auto-select one.\n */\nexport interface UnsafePayParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the Sui coins to be used in this transaction */\n\tinputCoins: string[];\n\t/** the recipients' addresses, the length of this vector must be the same as amounts. */\n\trecipients: string[];\n\t/** the amounts to be transferred to recipients, following the same order */\n\tamounts: string[];\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/**\n * Send all SUI coins to one recipient. This is for SUI coin only and does not require a separate gas\n * coin object. Specifically, what pay_all_sui does are: 1. accumulate all SUI from input coins and\n * deposit all SUI to the first input coin 2. transfer the updated first coin to the recipient and also\n * use this first coin as gas coin object. 3. the balance of the first input coin after tx is\n * sum(input_coins) - actual_gas_cost. 4. all other input coins other than the first are deleted.\n */\nexport interface UnsafePayAllSuiParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the Sui coins to be used in this transaction, including the coin for gas payment. */\n\tinputCoins: string[];\n\t/** the recipient address, */\n\trecipient: string;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/**\n * Send SUI coins to a list of addresses, following a list of amounts. This is for SUI coin only and\n * does not require a separate gas coin object. Specifically, what pay_sui does are: 1. debit each\n * input_coin to create new coin following the order of amounts and assign it to the corresponding\n * recipient. 2. accumulate all residual SUI from input coins left and deposit all SUI to the first\n * input coin, then use the first input coin as the gas coin object. 3. the balance of the first input\n * coin after tx is sum(input_coins) - sum(amounts) - actual_gas_cost 4. all other input coints other\n * than the first one are deleted.\n */\nexport interface UnsafePaySuiParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the Sui coins to be used in this transaction, including the coin for gas payment. */\n\tinputCoins: string[];\n\t/** the recipients' addresses, the length of this vector must be the same as amounts. */\n\trecipients: string[];\n\t/** the amounts to be transferred to recipients, following the same order */\n\tamounts: string[];\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/** Create an unsigned transaction to publish a Move package. */\nexport interface UnsafePublishParams {\n\t/** the transaction signer's Sui address */\n\tsender: string;\n\t/** the compiled bytes of a Move package */\n\tcompiledModules: string[];\n\t/** a list of transitive dependency addresses that this set of modules depends on. */\n\tdependencies: string[];\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/** Add stake to a validator's staking pool using multiple coins and amount. */\nexport interface UnsafeRequestAddStakeParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** Coin<SUI> object to stake */\n\tcoins: string[];\n\t/** stake amount */\n\tamount?: string | null | undefined;\n\t/** the validator's Sui address */\n\tvalidator: string;\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/** Withdraw stake from a validator's staking pool. */\nexport interface UnsafeRequestWithdrawStakeParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** StakedSui object ID */\n\tstakedSui: string;\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/** Create an unsigned transaction to split a coin object into multiple coins. */\nexport interface UnsafeSplitCoinParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the coin object to be spilt */\n\tcoinObjectId: string;\n\t/** the amounts to split out from the coin */\n\tsplitAmounts: string[];\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/** Create an unsigned transaction to split a coin object into multiple equal-size coins. */\nexport interface UnsafeSplitCoinEqualParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the coin object to be spilt */\n\tcoinObjectId: string;\n\t/** the number of coins to split into */\n\tsplitCount: string;\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n}\n/**\n * Create an unsigned transaction to transfer an object from one address to another. The object's type\n * must allow public transfers\n */\nexport interface UnsafeTransferObjectParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the ID of the object to be transferred */\n\tobjectId: string;\n\t/**\n\t * gas object to be used in this transaction, node will pick one from the signer's possession if not\n\t * provided\n\t */\n\tgas?: string | null | undefined;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n\t/** the recipient's Sui address */\n\trecipient: string;\n}\n/**\n * Create an unsigned transaction to send SUI coin object to a Sui address. The SUI object is also used\n * as the gas object.\n */\nexport interface UnsafeTransferSuiParams {\n\t/** the transaction signer's Sui address */\n\tsigner: string;\n\t/** the Sui coin object to be used in this transaction */\n\tsuiObjectId: string;\n\t/** the gas budget, the transaction will fail if the gas cost exceed the budget */\n\tgasBudget: string;\n\t/** the recipient's Sui address */\n\trecipient: string;\n\t/** the amount to be split out and transferred */\n\tamount?: string | null | undefined;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}