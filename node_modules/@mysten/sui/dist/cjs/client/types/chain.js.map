{"version": 3, "sources": ["../../../../src/client/types/chain.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tCheckpoint,\n\tDynamicFieldInfo,\n\tSuiCallArg,\n\tSuiMoveNormalizedModule,\n\tSuiParsedData,\n\tSuiTransaction,\n\tSuiValidatorSummary,\n} from './generated.js';\n\nexport type ResolvedNameServiceNames = {\n\tdata: string[];\n\thasNextPage: boolean;\n\tnextCursor: string | null;\n};\n\nexport type EpochInfo = {\n\tepoch: string;\n\tvalidators: SuiValidatorSummary[];\n\tepochTotalTransactions: string;\n\tfirstCheckpointId: string;\n\tepochStartTimestamp: string;\n\tendOfEpochInfo: EndOfEpochInfo | null;\n\treferenceGasPrice: number | null;\n};\n\nexport type EpochMetrics = {\n\tepoch: string;\n\tepochTotalTransactions: string;\n\tfirstCheckpointId: string;\n\tepochStartTimestamp: string;\n\tendOfEpochInfo: EndOfEpochInfo | null;\n};\n\nexport type EpochPage = {\n\tdata: EpochInfo[];\n\tnextCursor: string | null;\n\thasNextPage: boolean;\n};\n\nexport type EpochMetricsPage = {\n\tdata: EpochMetrics[];\n\tnextCursor: string | null;\n\thasNextPage: boolean;\n};\n\nexport type EndOfEpochInfo = {\n\tlastCheckpointId: string;\n\tepochEndTimestamp: string;\n\tprotocolVersion: string;\n\treferenceGasPrice: string;\n\ttotalStake: string;\n\tstorageFundReinvestment: string;\n\tstorageCharge: string;\n\tstorageRebate: string;\n\tstorageFundBalance: string;\n\tstakeSubsidyAmount: string;\n\ttotalGasFees: string;\n\ttotalStakeRewardsDistributed: string;\n\tleftoverStorageFundInflow: string;\n};\n\nexport type CheckpointPage = {\n\tdata: Checkpoint[];\n\tnextCursor: string | null;\n\thasNextPage: boolean;\n};\n\nexport type NetworkMetrics = {\n\tcurrentTps: number;\n\ttps30Days: number;\n\tcurrentCheckpoint: string;\n\tcurrentEpoch: string;\n\ttotalAddresses: string;\n\ttotalObjects: string;\n\ttotalPackages: string;\n};\n\nexport type AddressMetrics = {\n\tcheckpoint: number;\n\tepoch: number;\n\ttimestampMs: number;\n\tcumulativeAddresses: number;\n\tcumulativeActiveAddresses: number;\n\tdailyActiveAddresses: number;\n};\n\nexport type AllEpochsAddressMetrics = AddressMetrics[];\n\nexport type MoveCallMetrics = {\n\trank3Days: MoveCallMetric[];\n\trank7Days: MoveCallMetric[];\n\trank30Days: MoveCallMetric[];\n};\n\nexport type MoveCallMetric = [\n\t{\n\t\tmodule: string;\n\t\tpackage: string;\n\t\tfunction: string;\n\t},\n\tstring,\n];\n\nexport type DynamicFieldPage = {\n\tdata: DynamicFieldInfo[];\n\tnextCursor: string | null;\n\thasNextPage: boolean;\n};\n\nexport type SuiMoveNormalizedModules = Record<string, SuiMoveNormalizedModule>;\n\nexport type SuiMoveObject = Extract<SuiParsedData, { dataType: 'moveObject' }>;\nexport type SuiMovePackage = Extract<SuiParsedData, { dataType: 'package' }>;\n\nexport type ProgrammableTransaction = {\n\ttransactions: SuiTransaction[];\n\tinputs: SuiCallArg[];\n};\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}