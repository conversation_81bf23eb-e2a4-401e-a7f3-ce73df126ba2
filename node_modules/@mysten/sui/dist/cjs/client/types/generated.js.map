{"version": 3, "sources": ["../../../../src/client/types/generated.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n *  ######################################\n *  ### DO NOT EDIT THIS FILE DIRECTLY ###\n *  ######################################\n *\n * This file is generated from:\n * /crates/sui-open-rpc/spec/openrpc.json\n */\n\nexport type Authenticator =\n\t/** The contained SuiAddress exclusively has all permissions: read, write, delete, transfer */\n\t{\n\t\tSingleOwner: string;\n\t};\nexport interface Balance {\n\tcoinObjectCount: number;\n\tcoinType: string;\n\tlockedBalance: {\n\t\t[key: string]: string;\n\t};\n\ttotalBalance: string;\n}\nexport interface BalanceChange {\n\t/**\n\t * The amount indicate the balance value changes, negative amount means spending coin value and\n\t * positive means receiving coin value.\n\t */\n\tamount: string;\n\tcoinType: string;\n\t/** Owner of the balance change */\n\towner: ObjectOwner;\n}\nexport interface Checkpoint {\n\t/** Commitments to checkpoint state */\n\tcheckpointCommitments: CheckpointCommitment[];\n\t/** Checkpoint digest */\n\tdigest: string;\n\t/** Present only on the final checkpoint of the epoch. */\n\tendOfEpochData?: EndOfEpochData | null;\n\t/** Checkpoint's epoch ID */\n\tepoch: string;\n\t/**\n\t * The running total gas costs of all transactions included in the current epoch so far until this\n\t * checkpoint.\n\t */\n\tepochRollingGasCostSummary: GasCostSummary;\n\t/** Total number of transactions committed since genesis, including those in this checkpoint. */\n\tnetworkTotalTransactions: string;\n\t/** Digest of the previous checkpoint */\n\tpreviousDigest?: string | null;\n\t/** Checkpoint sequence number */\n\tsequenceNumber: string;\n\t/**\n\t * Timestamp of the checkpoint - number of milliseconds from the Unix epoch Checkpoint timestamps are\n\t * monotonic, but not strongly monotonic - subsequent checkpoints can have same timestamp if they\n\t * originate from the same underlining consensus commit\n\t */\n\ttimestampMs: string;\n\t/** Transaction digests */\n\ttransactions: string[];\n\t/** Validator Signature */\n\tvalidatorSignature: string;\n}\nexport type CheckpointCommitment = {\n\tECMHLiveObjectSetDigest: ECMHLiveObjectSetDigest;\n};\nexport type CheckpointId = string | string;\n/** A claim consists of value and index_mod_4. */\nexport interface Claim {\n\tindexMod4: number;\n\tvalue: string;\n}\nexport interface CoinStruct {\n\tbalance: string;\n\tcoinObjectId: string;\n\tcoinType: string;\n\tdigest: string;\n\tpreviousTransaction: string;\n\tversion: string;\n}\n/** RPC representation of the [Committee] type. */\nexport interface CommitteeInfo {\n\tepoch: string;\n\tvalidators: [string, string][];\n}\n/** Unlike [enum Signature], [enum CompressedSignature] does not contain public key. */\nexport type CompressedSignature =\n\t| {\n\t\t\tEd25519: string;\n\t  }\n\t| {\n\t\t\tSecp256k1: string;\n\t  }\n\t| {\n\t\t\tSecp256r1: string;\n\t  }\n\t| {\n\t\t\tZkLogin: string;\n\t  };\n/** Uses an enum to allow for future expansion of the ConsensusDeterminedVersionAssignments. */\nexport type ConsensusDeterminedVersionAssignments = {\n\tCancelledTransactions: [string, [string, string][]][];\n};\nexport type SuiParsedData =\n\t| {\n\t\t\tdataType: 'moveObject';\n\t\t\tfields: MoveStruct;\n\t\t\thasPublicTransfer: boolean;\n\t\t\ttype: string;\n\t  }\n\t| {\n\t\t\tdataType: 'package';\n\t\t\tdisassembled: {\n\t\t\t\t[key: string]: unknown;\n\t\t\t};\n\t  };\nexport interface DelegatedStake {\n\tstakes: StakeObject[];\n\t/** Staking pool object id. */\n\tstakingPool: string;\n\t/** Validator's Address. */\n\tvalidatorAddress: string;\n}\n/** Additional rguments supplied to dev inspect beyond what is allowed in today's API. */\nexport interface DevInspectArgs {\n\t/** The gas budget for the transaction. */\n\tgasBudget?: string | null;\n\t/** The gas objects used to pay for the transaction. */\n\tgasObjects?: [string, string, string][] | null;\n\t/** The sponsor of the gas for the transaction, might be different from the sender. */\n\tgasSponsor?: string | null;\n\t/** Whether to return the raw transaction data and effects. */\n\tshowRawTxnDataAndEffects?: boolean | null;\n\t/** Whether to skip transaction checks for the transaction. */\n\tskipChecks?: boolean | null;\n}\n/** The response from processing a dev inspect transaction */\nexport interface DevInspectResults {\n\t/**\n\t * Summary of effects that likely would be generated if the transaction is actually run. Note however,\n\t * that not all dev-inspect transactions are actually usable as transactions so it might not be\n\t * possible actually generate these effects from a normal transaction.\n\t */\n\teffects: TransactionEffects;\n\t/** Execution error from executing the transactions */\n\terror?: string | null;\n\t/** Events that likely would be generated if the transaction is actually run. */\n\tevents: SuiEvent[];\n\t/** The raw effects of the transaction that was dev inspected. */\n\trawEffects?: number[];\n\t/** The raw transaction data that was dev inspected. */\n\trawTxnData?: number[];\n\t/** Execution results (including return values) from executing the transactions */\n\tresults?: SuiExecutionResult[] | null;\n}\nexport interface DisplayFieldsResponse {\n\tdata?: {\n\t\t[key: string]: string;\n\t} | null;\n\terror?: ObjectResponseError | null;\n}\nexport interface DryRunTransactionBlockResponse {\n\tbalanceChanges: BalanceChange[];\n\teffects: TransactionEffects;\n\tevents: SuiEvent[];\n\tinput: TransactionBlockData;\n\tobjectChanges: SuiObjectChange[];\n}\nexport type DynamicFieldInfo =\n\t| {\n\t\t\tdigest: string;\n\t\t\tname: DynamicFieldName;\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\ttype: DynamicFieldType;\n\t\t\tversion: string;\n\t\t\tbcsEncoding: 'base64';\n\t\t\tbcsName: string;\n\t  }\n\t| {\n\t\t\tdigest: string;\n\t\t\tname: DynamicFieldName;\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\ttype: DynamicFieldType;\n\t\t\tversion: string;\n\t\t\tbcsEncoding: 'base58';\n\t\t\tbcsName: string;\n\t  };\nexport interface DynamicFieldName {\n\ttype: string;\n\tvalue: unknown;\n}\nexport type DynamicFieldType = 'DynamicField' | 'DynamicObject';\n/** The Sha256 digest of an EllipticCurveMultisetHash committing to the live object set. */\nexport interface ECMHLiveObjectSetDigest {\n\tdigest: number[];\n}\nexport interface EndOfEpochData {\n\t/** Commitments to epoch specific state (e.g. live object set) */\n\tepochCommitments: CheckpointCommitment[];\n\t/**\n\t * next_epoch_committee is `Some` if and only if the current checkpoint is the last checkpoint of an\n\t * epoch. Therefore next_epoch_committee can be used to pick the last checkpoint of an epoch, which is\n\t * often useful to get epoch level summary stats like total gas cost of an epoch, or the total number\n\t * of transactions from genesis to the end of an epoch. The committee is stored as a vector of\n\t * validator pub key and stake pairs. The vector should be sorted based on the Committee data\n\t * structure.\n\t */\n\tnextEpochCommittee: [string, string][];\n\t/**\n\t * The protocol version that is in effect during the epoch that starts immediately after this\n\t * checkpoint.\n\t */\n\tnextEpochProtocolVersion: string;\n}\nexport type SuiEvent =\n\t| {\n\t\t\t/**\n\t\t\t * Sequential event ID, ie (transaction seq number, event seq number). 1) Serves as a unique event ID\n\t\t\t * for each fullnode 2) Also serves to sequence events for the purposes of pagination and querying. A\n\t\t\t * higher id is an event seen later by that fullnode. This ID is the \"cursor\" for event querying.\n\t\t\t */\n\t\t\tid: EventId;\n\t\t\t/** Move package where this event was emitted. */\n\t\t\tpackageId: string;\n\t\t\t/** Parsed json value of the event */\n\t\t\tparsedJson: unknown;\n\t\t\t/** Sender's Sui address. */\n\t\t\tsender: string;\n\t\t\t/** UTC timestamp in milliseconds since epoch (1/1/1970) */\n\t\t\ttimestampMs?: string | null;\n\t\t\t/** Move module where this event was emitted. */\n\t\t\ttransactionModule: string;\n\t\t\t/** Move event type. */\n\t\t\ttype: string;\n\t\t\tbcs: string;\n\t\t\tbcsEncoding: 'base64';\n\t  }\n\t| {\n\t\t\t/**\n\t\t\t * Sequential event ID, ie (transaction seq number, event seq number). 1) Serves as a unique event ID\n\t\t\t * for each fullnode 2) Also serves to sequence events for the purposes of pagination and querying. A\n\t\t\t * higher id is an event seen later by that fullnode. This ID is the \"cursor\" for event querying.\n\t\t\t */\n\t\t\tid: EventId;\n\t\t\t/** Move package where this event was emitted. */\n\t\t\tpackageId: string;\n\t\t\t/** Parsed json value of the event */\n\t\t\tparsedJson: unknown;\n\t\t\t/** Sender's Sui address. */\n\t\t\tsender: string;\n\t\t\t/** UTC timestamp in milliseconds since epoch (1/1/1970) */\n\t\t\ttimestampMs?: string | null;\n\t\t\t/** Move module where this event was emitted. */\n\t\t\ttransactionModule: string;\n\t\t\t/** Move event type. */\n\t\t\ttype: string;\n\t\t\tbcs: string;\n\t\t\tbcsEncoding: 'base58';\n\t  };\nexport type SuiEventFilter =\n\t/** Return all events. */\n\t| {\n\t\t\tAll: [];\n\t  } /** Return events that match any of the given filters. Only supported on event subscriptions. */\n\t| {\n\t\t\tAny: SuiEventFilter[];\n\t  } /** Query by sender address. */\n\t| {\n\t\t\tSender: string;\n\t  } /** Return events emitted by the given transaction. */\n\t| {\n\t\t\tTransaction: string;\n\t  } /**\n\t * Return events emitted in a specified Move module. If the event is defined in Module A but emitted in\n\t * a tx with Module B, query `MoveModule` by module B returns the event. Query `MoveEventModule` by\n\t * module A returns the event too.\n\t */\n\t| {\n\t\t\tMoveModule: {\n\t\t\t\t/** the module name */\n\t\t\t\tmodule: string;\n\t\t\t\t/** the Move package ID */\n\t\t\t\tpackage: string;\n\t\t\t};\n\t  } /**\n\t * Return events with the given Move event struct name (struct tag). For example, if the event is\n\t * defined in `0xabcd::MyModule`, and named `Foo`, then the struct tag is `0xabcd::MyModule::Foo`.\n\t */\n\t| {\n\t\t\tMoveEventType: string;\n\t  } /**\n\t * Return events with the given Move module name where the event struct is defined. If the event is\n\t * defined in Module A but emitted in a tx with Module B, query `MoveEventModule` by module A returns\n\t * the event. Query `MoveModule` by module B returns the event too.\n\t */\n\t| {\n\t\t\tMoveEventModule: {\n\t\t\t\t/** the module name */\n\t\t\t\tmodule: string;\n\t\t\t\t/** the Move package ID */\n\t\t\t\tpackage: string;\n\t\t\t};\n\t  } /** Return events emitted in [start_time, end_time] interval */\n\t| {\n\t\t\tTimeRange: {\n\t\t\t\t/** right endpoint of time interval, milliseconds since epoch, exclusive */\n\t\t\t\tendTime: string;\n\t\t\t\t/** left endpoint of time interval, milliseconds since epoch, inclusive */\n\t\t\t\tstartTime: string;\n\t\t\t};\n\t  };\n/** Unique ID of a Sui Event, the ID is a combination of transaction digest and event seq number. */\nexport interface EventId {\n\teventSeq: string;\n\ttxDigest: string;\n}\nexport type ExecuteTransactionRequestType = 'WaitForEffectsCert' | 'WaitForLocalExecution';\nexport type ExecutionStatus = {\n\tstatus: 'success' | 'failure';\n\terror?: string;\n};\n/**\n * Summary of the charges in a transaction. Storage is charged independently of computation. There are\n * 3 parts to the storage charges: `storage_cost`: it is the charge of storage at the time the\n * transaction is executed. The cost of storage is the number of bytes of the objects being mutated\n * multiplied by a variable storage cost per byte `storage_rebate`: this is the amount a user gets back\n * when manipulating an object. The `storage_rebate` is the `storage_cost` for an object minus fees.\n * `non_refundable_storage_fee`: not all the value of the object storage cost is given back to user and\n * there is a small fraction that is kept by the system. This value tracks that charge.\n *\n * When looking at a gas cost summary the amount charged to the user is\n * `computation_cost + storage_cost - storage_rebate` and that is the amount that is deducted from the\n * gas coins. `non_refundable_storage_fee` is collected from the objects being mutated/deleted and it\n * is tracked by the system in storage funds.\n *\n * Objects deleted, including the older versions of objects mutated, have the storage field on the\n * objects added up to a pool of \"potential rebate\". This rebate then is reduced by the \"nonrefundable\n * rate\" such that:\n * `potential_rebate(storage cost of deleted/mutated objects) = storage_rebate + non_refundable_storage_fee`\n */\nexport interface GasCostSummary {\n\t/** Cost of computation/execution */\n\tcomputationCost: string;\n\t/** The fee for the rebate. The portion of the storage rebate kept by the system. */\n\tnonRefundableStorageFee: string;\n\t/** Storage cost, it's the sum of all storage cost for all objects created or mutated. */\n\tstorageCost: string;\n\t/**\n\t * The amount of storage cost refunded to the user for all objects deleted or mutated in the\n\t * transaction.\n\t */\n\tstorageRebate: string;\n}\nexport interface SuiGasData {\n\tbudget: string;\n\towner: string;\n\tpayment: SuiObjectRef[];\n\tprice: string;\n}\nexport interface GetPastObjectRequest {\n\t/** the ID of the queried object */\n\tobjectId: string;\n\t/** the version of the queried object. */\n\tversion: string;\n}\nexport type InputObjectKind =\n\t| {\n\t\t\tMovePackage: string;\n\t  }\n\t| {\n\t\t\tImmOrOwnedMoveObject: SuiObjectRef;\n\t  }\n\t| {\n\t\t\tSharedMoveObject: {\n\t\t\t\tid: string;\n\t\t\t\tinitial_shared_version: string;\n\t\t\t\tmutable?: boolean;\n\t\t\t};\n\t  };\nexport interface MoveCallParams {\n\targuments: unknown[];\n\tfunction: string;\n\tmodule: string;\n\tpackageObjectId: string;\n\ttypeArguments?: string[];\n}\nexport type SuiMoveFunctionArgType =\n\t| 'Pure'\n\t| {\n\t\t\tObject: ObjectValueKind;\n\t  };\nexport type MoveStruct =\n\t| MoveValue[]\n\t| {\n\t\t\tfields: {\n\t\t\t\t[key: string]: MoveValue;\n\t\t\t};\n\t\t\ttype: string;\n\t  }\n\t| {\n\t\t\t[key: string]: MoveValue;\n\t  };\nexport type MoveValue =\n\t| number\n\t| boolean\n\t| string\n\t| MoveValue[]\n\t| string\n\t| {\n\t\t\tid: string;\n\t  }\n\t| MoveStruct\n\t| null\n\t| MoveVariant;\nexport interface MoveVariant {\n\tfields: {\n\t\t[key: string]: MoveValue;\n\t};\n\ttype: string;\n\tvariant: string;\n}\n/** The struct that contains signatures and public keys necessary for authenticating a MultiSig. */\nexport interface MultiSig {\n\t/** A bitmap that indicates the position of which public key the signature should be authenticated with. */\n\tbitmap: number;\n\t/**\n\t * The public key encoded with each public key with its signature scheme used along with the\n\t * corresponding weight.\n\t */\n\tmultisig_pk: MultiSigPublicKey;\n\t/** The plain signature encoded with signature scheme. */\n\tsigs: CompressedSignature[];\n}\n/**\n * Deprecated, use [struct MultiSig] instead. The struct that contains signatures and public keys necessary\n * for authenticating a MultiSigLegacy.\n */\nexport interface MultiSigLegacy {\n\t/** A bitmap that indicates the position of which public key the signature should be authenticated with. */\n\tbitmap: string;\n\t/**\n\t * The public key encoded with each public key with its signature scheme used along with the\n\t * corresponding weight.\n\t */\n\tmultisig_pk: MultiSigPublicKeyLegacy;\n\t/** The plain signature encoded with signature scheme. */\n\tsigs: CompressedSignature[];\n}\n/** The struct that contains the public key used for authenticating a MultiSig. */\nexport interface MultiSigPublicKey {\n\t/** A list of public key and its corresponding weight. */\n\tpk_map: [PublicKey, number][];\n\t/**\n\t * If the total weight of the public keys corresponding to verified signatures is larger than\n\t * threshold, the MultiSig is verified.\n\t */\n\tthreshold: number;\n}\n/**\n * Deprecated, use [struct MultiSigPublicKey] instead. The struct that contains the public key used for\n * authenticating a MultiSig.\n */\nexport interface MultiSigPublicKeyLegacy {\n\t/** A list of public key and its corresponding weight. */\n\tpk_map: [PublicKey, number][];\n\t/**\n\t * If the total weight of the public keys corresponding to verified signatures is larger than\n\t * threshold, the MultiSig is verified.\n\t */\n\tthreshold: number;\n}\n/**\n * ObjectChange are derived from the object mutations in the TransactionEffect to provide richer object\n * information.\n */\nexport type SuiObjectChange =\n\t/** Module published */\n\t| {\n\t\t\tdigest: string;\n\t\t\tmodules: string[];\n\t\t\tpackageId: string;\n\t\t\ttype: 'published';\n\t\t\tversion: string;\n\t  } /** Transfer objects to new address / wrap in another object */\n\t| {\n\t\t\tdigest: string;\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\trecipient: ObjectOwner;\n\t\t\tsender: string;\n\t\t\ttype: 'transferred';\n\t\t\tversion: string;\n\t  } /** Object mutated. */\n\t| {\n\t\t\tdigest: string;\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\towner: ObjectOwner;\n\t\t\tpreviousVersion: string;\n\t\t\tsender: string;\n\t\t\ttype: 'mutated';\n\t\t\tversion: string;\n\t  } /** Delete object */\n\t| {\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\tsender: string;\n\t\t\ttype: 'deleted';\n\t\t\tversion: string;\n\t  } /** Wrapped object */\n\t| {\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\tsender: string;\n\t\t\ttype: 'wrapped';\n\t\t\tversion: string;\n\t  } /** New object creation */\n\t| {\n\t\t\tdigest: string;\n\t\t\tobjectId: string;\n\t\t\tobjectType: string;\n\t\t\towner: ObjectOwner;\n\t\t\tsender: string;\n\t\t\ttype: 'created';\n\t\t\tversion: string;\n\t  };\nexport interface SuiObjectData {\n\t/**\n\t * Move object content or package content in BCS, default to be None unless\n\t * SuiObjectDataOptions.showBcs is set to true\n\t */\n\tbcs?: RawData | null;\n\t/**\n\t * Move object content or package content, default to be None unless SuiObjectDataOptions.showContent\n\t * is set to true\n\t */\n\tcontent?: SuiParsedData | null;\n\t/** Base64 string representing the object digest */\n\tdigest: string;\n\t/**\n\t * The Display metadata for frontend UI rendering, default to be None unless\n\t * SuiObjectDataOptions.showContent is set to true This can also be None if the struct type does not\n\t * have Display defined See more details in <https://forums.sui.io/t/nft-object-display-proposal/4872>\n\t */\n\tdisplay?: DisplayFieldsResponse | null;\n\tobjectId: string;\n\t/** The owner of this object. Default to be None unless SuiObjectDataOptions.showOwner is set to true */\n\towner?: ObjectOwner | null;\n\t/**\n\t * The digest of the transaction that created or last mutated this object. Default to be None unless\n\t * SuiObjectDataOptions.showPreviousTransaction is set to true\n\t */\n\tpreviousTransaction?: string | null;\n\t/**\n\t * The amount of SUI we would rebate if this object gets deleted. This number is re-calculated each\n\t * time the object is mutated based on the present storage gas price.\n\t */\n\tstorageRebate?: string | null;\n\t/** The type of the object. Default to be None unless SuiObjectDataOptions.showType is set to true */\n\ttype?: string | null;\n\t/** Object version. */\n\tversion: string;\n}\nexport interface SuiObjectDataOptions {\n\t/** Whether to show the content in BCS format. Default to be False */\n\tshowBcs?: boolean;\n\t/**\n\t * Whether to show the content(i.e., package content or Move struct content) of the object. Default to\n\t * be False\n\t */\n\tshowContent?: boolean;\n\t/** Whether to show the Display metadata of the object for frontend rendering. Default to be False */\n\tshowDisplay?: boolean;\n\t/** Whether to show the owner of the object. Default to be False */\n\tshowOwner?: boolean;\n\t/** Whether to show the previous transaction digest of the object. Default to be False */\n\tshowPreviousTransaction?: boolean;\n\t/** Whether to show the storage rebate of the object. Default to be False */\n\tshowStorageRebate?: boolean;\n\t/** Whether to show the type of the object. Default to be False */\n\tshowType?: boolean;\n}\nexport type ObjectRead =\n\t/** The object exists and is found with this version */\n\t| {\n\t\t\tdetails: SuiObjectData;\n\t\t\tstatus: 'VersionFound';\n\t  } /** The object does not exist */\n\t| {\n\t\t\tdetails: string;\n\t\t\tstatus: 'ObjectNotExists';\n\t  } /** The object is found to be deleted with this version */\n\t| {\n\t\t\tdetails: SuiObjectRef;\n\t\t\tstatus: 'ObjectDeleted';\n\t  } /** The object exists but not found with this version */\n\t| {\n\t\t\tdetails: [string, string];\n\t\t\tstatus: 'VersionNotFound';\n\t  } /** The asked object version is higher than the latest */\n\t| {\n\t\t\tdetails: {\n\t\t\t\tasked_version: string;\n\t\t\t\tlatest_version: string;\n\t\t\t\tobject_id: string;\n\t\t\t};\n\t\t\tstatus: 'VersionTooHigh';\n\t  };\nexport interface SuiObjectRef {\n\t/** Base64 string representing the object digest */\n\tdigest: string;\n\t/** Hex code as string representing the object id */\n\tobjectId: string;\n\t/** Object version. */\n\tversion: string;\n}\nexport type ObjectResponseError =\n\t| {\n\t\t\tcode: 'notExists';\n\t\t\tobject_id: string;\n\t  }\n\t| {\n\t\t\tcode: 'dynamicFieldNotFound';\n\t\t\tparent_object_id: string;\n\t  }\n\t| {\n\t\t\tcode: 'deleted';\n\t\t\t/** Base64 string representing the object digest */\n\t\t\tdigest: string;\n\t\t\tobject_id: string;\n\t\t\t/** Object version. */\n\t\t\tversion: string;\n\t  }\n\t| {\n\t\t\tcode: 'unknown';\n\t  }\n\t| {\n\t\t\tcode: 'displayError';\n\t\t\terror: string;\n\t  };\nexport interface SuiObjectResponseQuery {\n\t/** If None, no filter will be applied */\n\tfilter?: SuiObjectDataFilter | null;\n\t/** config which fields to include in the response, by default only digest is included */\n\toptions?: SuiObjectDataOptions | null;\n}\nexport type ObjectValueKind = 'ByImmutableReference' | 'ByMutableReference' | 'ByValue';\nexport interface OwnedObjectRef {\n\towner: ObjectOwner;\n\treference: SuiObjectRef;\n}\nexport type ObjectOwner =\n\t/** Object is exclusively owned by a single address, and is mutable. */\n\t| {\n\t\t\tAddressOwner: string;\n\t  } /**\n\t * Object is exclusively owned by a single object, and is mutable. The object ID is converted to\n\t * SuiAddress as SuiAddress is universal.\n\t */\n\t| {\n\t\t\tObjectOwner: string;\n\t  } /** Object is shared, can be used by any address, and is mutable. */\n\t| {\n\t\t\tShared: {\n\t\t\t\t/** The version at which the object became shared */\n\t\t\t\tinitial_shared_version: string;\n\t\t\t};\n\t  }\n\t| 'Immutable' /**\n\t * Object is sequenced via consensus. Ownership is managed by the configured authenticator.\n\t *\n\t * Note: wondering what happened to `V1`? `Shared` above was the V1 of consensus objects.\n\t */\n\t| {\n\t\t\tConsensusV2: {\n\t\t\t\t/** The authentication mode of the object */\n\t\t\t\tauthenticator: Authenticator;\n\t\t\t\t/**\n\t\t\t\t * The version at which the object most recently became a consensus object. This serves the same\n\t\t\t\t * function as `initial_shared_version`, except it may change if the object's Owner type changes.\n\t\t\t\t */\n\t\t\t\tstart_version: string;\n\t\t\t};\n\t  };\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedCheckpoints {\n\tdata: Checkpoint[];\n\thasNextPage: boolean;\n\tnextCursor?: string | null;\n}\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedCoins {\n\tdata: CoinStruct[];\n\thasNextPage: boolean;\n\tnextCursor?: string | null;\n}\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedDynamicFieldInfos {\n\tdata: DynamicFieldInfo[];\n\thasNextPage: boolean;\n\tnextCursor?: string | null;\n}\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedEvents {\n\tdata: SuiEvent[];\n\thasNextPage: boolean;\n\tnextCursor?: EventId | null;\n}\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedStrings {\n\tdata: string[];\n\thasNextPage: boolean;\n\tnextCursor?: string | null;\n}\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedObjectsResponse {\n\tdata: SuiObjectResponse[];\n\thasNextPage: boolean;\n\tnextCursor?: string | null;\n}\n/**\n * `next_cursor` points to the last item in the page; Reading with `next_cursor` will start from the\n * next item after `next_cursor` if `next_cursor` is `Some`, otherwise it will start from the first\n * item.\n */\nexport interface PaginatedTransactionResponse {\n\tdata: SuiTransactionBlockResponse[];\n\thasNextPage: boolean;\n\tnextCursor?: string | null;\n}\n/**\n * An passkey authenticator with parsed fields. See field defition below. Can be initialized from\n * [struct RawPasskeyAuthenticator].\n */\nexport interface PasskeyAuthenticator {\n\t/**\n\t * `authenticatorData` is a bytearray that encodes\n\t * [Authenticator Data](https://www.w3.org/TR/webauthn-2/#sctn-authenticator-data) structure returned\n\t * by the authenticator attestation response as is.\n\t */\n\tauthenticator_data: number[];\n\t/**\n\t * `clientDataJSON` contains a JSON-compatible UTF-8 encoded string of the client data which is passed\n\t * to the authenticator by the client during the authentication request (see\n\t * [CollectedClientData](https://www.w3.org/TR/webauthn-2/#dictdef-collectedclientdata))\n\t */\n\tclient_data_json: string;\n}\nexport interface ProtocolConfig {\n\tattributes: {\n\t\t[key: string]: ProtocolConfigValue | null;\n\t};\n\tfeatureFlags: {\n\t\t[key: string]: boolean;\n\t};\n\tmaxSupportedProtocolVersion: string;\n\tminSupportedProtocolVersion: string;\n\tprotocolVersion: string;\n}\nexport type ProtocolConfigValue =\n\t| {\n\t\t\tu16: string;\n\t  }\n\t| {\n\t\t\tu32: string;\n\t  }\n\t| {\n\t\t\tu64: string;\n\t  }\n\t| {\n\t\t\tf64: string;\n\t  }\n\t| {\n\t\t\tbool: string;\n\t  };\nexport type PublicKey =\n\t| {\n\t\t\tEd25519: string;\n\t  }\n\t| {\n\t\t\tSecp256k1: string;\n\t  }\n\t| {\n\t\t\tSecp256r1: string;\n\t  }\n\t| {\n\t\t\tZkLogin: string;\n\t  }\n\t| {\n\t\t\tPasskey: string;\n\t  };\nexport type RPCTransactionRequestParams =\n\t| {\n\t\t\ttransferObjectRequestParams: TransferObjectParams;\n\t  }\n\t| {\n\t\t\tmoveCallRequestParams: MoveCallParams;\n\t  };\nexport type RawData =\n\t| {\n\t\t\tbcsBytes: string;\n\t\t\tdataType: 'moveObject';\n\t\t\thasPublicTransfer: boolean;\n\t\t\ttype: string;\n\t\t\tversion: string;\n\t  }\n\t| {\n\t\t\tdataType: 'package';\n\t\t\tid: string;\n\t\t\tlinkageTable: {\n\t\t\t\t[key: string]: UpgradeInfo;\n\t\t\t};\n\t\t\tmoduleMap: {\n\t\t\t\t[key: string]: string;\n\t\t\t};\n\t\t\ttypeOriginTable: TypeOrigin[];\n\t\t\tversion: string;\n\t  };\nexport type Signature =\n\t| {\n\t\t\tEd25519SuiSignature: string;\n\t  }\n\t| {\n\t\t\tSecp256k1SuiSignature: string;\n\t  }\n\t| {\n\t\t\tSecp256r1SuiSignature: string;\n\t  };\nexport type StakeObject =\n\t| {\n\t\t\tprincipal: string;\n\t\t\tstakeActiveEpoch: string;\n\t\t\tstakeRequestEpoch: string;\n\t\t\t/** ID of the StakedSui receipt object. */\n\t\t\tstakedSuiId: string;\n\t\t\tstatus: 'Pending';\n\t  }\n\t| {\n\t\t\tprincipal: string;\n\t\t\tstakeActiveEpoch: string;\n\t\t\tstakeRequestEpoch: string;\n\t\t\t/** ID of the StakedSui receipt object. */\n\t\t\tstakedSuiId: string;\n\t\t\testimatedReward: string;\n\t\t\tstatus: 'Active';\n\t  }\n\t| {\n\t\t\tprincipal: string;\n\t\t\tstakeActiveEpoch: string;\n\t\t\tstakeRequestEpoch: string;\n\t\t\t/** ID of the StakedSui receipt object. */\n\t\t\tstakedSuiId: string;\n\t\t\tstatus: 'Unstaked';\n\t  };\nexport interface SuiActiveJwk {\n\tepoch: string;\n\tjwk: SuiJWK;\n\tjwk_id: SuiJwkId;\n}\n/** An argument to a transaction in a programmable transaction block */\nexport type SuiArgument =\n\t| 'GasCoin' /** One of the input objects or primitive values (from `ProgrammableTransactionBlock` inputs) */\n\t| {\n\t\t\tInput: number;\n\t  } /** The result of another transaction (from `ProgrammableTransactionBlock` transactions) */\n\t| {\n\t\t\tResult: number;\n\t  } /**\n\t * Like a `Result` but it accesses a nested result. Currently, the only usage of this is to access a\n\t * value from a Move call with multiple return values.\n\t */\n\t| {\n\t\t\tNestedResult: [number, number];\n\t  };\nexport interface SuiAuthenticatorStateExpire {\n\tmin_epoch: string;\n}\nexport type SuiCallArg =\n\t| {\n\t\t\ttype: 'object';\n\t\t\tdigest: string;\n\t\t\tobjectId: string;\n\t\t\tobjectType: 'immOrOwnedObject';\n\t\t\tversion: string;\n\t  }\n\t| {\n\t\t\ttype: 'object';\n\t\t\tinitialSharedVersion: string;\n\t\t\tmutable: boolean;\n\t\t\tobjectId: string;\n\t\t\tobjectType: 'sharedObject';\n\t  }\n\t| {\n\t\t\ttype: 'object';\n\t\t\tdigest: string;\n\t\t\tobjectId: string;\n\t\t\tobjectType: 'receiving';\n\t\t\tversion: string;\n\t  }\n\t| {\n\t\t\ttype: 'pure';\n\t\t\tvalue: unknown;\n\t\t\tvalueType?: string | null;\n\t  };\nexport interface SuiChangeEpoch {\n\tcomputation_charge: string;\n\tepoch: string;\n\tepoch_start_timestamp_ms: string;\n\tstorage_charge: string;\n\tstorage_rebate: string;\n}\nexport interface CoinMetadata {\n\t/** Number of decimal places the coin uses. */\n\tdecimals: number;\n\t/** Description of the token */\n\tdescription: string;\n\t/** URL for the token logo */\n\ticonUrl?: string | null;\n\t/** Object id for the CoinMetadata object */\n\tid?: string | null;\n\t/** Name for the token */\n\tname: string;\n\t/** Symbol for the token */\n\tsymbol: string;\n}\nexport type SuiEndOfEpochTransactionKind =\n\t| 'AuthenticatorStateCreate'\n\t| 'RandomnessStateCreate'\n\t| 'CoinDenyListStateCreate'\n\t| {\n\t\t\tChangeEpoch: SuiChangeEpoch;\n\t  }\n\t| {\n\t\t\tAuthenticatorStateExpire: SuiAuthenticatorStateExpire;\n\t  }\n\t| {\n\t\t\tBridgeStateCreate: string;\n\t  }\n\t| {\n\t\t\tBridgeCommitteeUpdate: string;\n\t  };\nexport interface SuiExecutionResult {\n\t/** The value of any arguments that were mutably borrowed. Non-mut borrowed values are not included */\n\tmutableReferenceOutputs?: [SuiArgument, number[], string][];\n\t/** The return values from the transaction */\n\treturnValues?: [number[], string][];\n}\nexport interface SuiJWK {\n\talg: string;\n\te: string;\n\tkty: string;\n\tn: string;\n}\nexport interface SuiJwkId {\n\tiss: string;\n\tkid: string;\n}\nexport type SuiMoveAbility = 'Copy' | 'Drop' | 'Store' | 'Key';\nexport interface SuiMoveAbilitySet {\n\tabilities: SuiMoveAbility[];\n}\nexport interface SuiMoveModuleId {\n\taddress: string;\n\tname: string;\n}\nexport interface SuiMoveNormalizedEnum {\n\tabilities: SuiMoveAbilitySet;\n\ttypeParameters: SuiMoveStructTypeParameter[];\n\tvariants: {\n\t\t[key: string]: SuiMoveNormalizedField[];\n\t};\n}\nexport interface SuiMoveNormalizedField {\n\tname: string;\n\ttype: SuiMoveNormalizedType;\n}\nexport interface SuiMoveNormalizedFunction {\n\tisEntry: boolean;\n\tparameters: SuiMoveNormalizedType[];\n\treturn: SuiMoveNormalizedType[];\n\ttypeParameters: SuiMoveAbilitySet[];\n\tvisibility: SuiMoveVisibility;\n}\nexport interface SuiMoveNormalizedModule {\n\taddress: string;\n\tenums?: {\n\t\t[key: string]: SuiMoveNormalizedEnum;\n\t};\n\texposedFunctions: {\n\t\t[key: string]: SuiMoveNormalizedFunction;\n\t};\n\tfileFormatVersion: number;\n\tfriends: SuiMoveModuleId[];\n\tname: string;\n\tstructs: {\n\t\t[key: string]: SuiMoveNormalizedStruct;\n\t};\n}\nexport interface SuiMoveNormalizedStruct {\n\tabilities: SuiMoveAbilitySet;\n\tfields: SuiMoveNormalizedField[];\n\ttypeParameters: SuiMoveStructTypeParameter[];\n}\nexport type SuiMoveNormalizedType =\n\t| 'Bool'\n\t| 'U8'\n\t| 'U16'\n\t| 'U32'\n\t| 'U64'\n\t| 'U128'\n\t| 'U256'\n\t| 'Address'\n\t| 'Signer'\n\t| {\n\t\t\tStruct: {\n\t\t\t\taddress: string;\n\t\t\t\tmodule: string;\n\t\t\t\tname: string;\n\t\t\t\ttypeArguments: SuiMoveNormalizedType[];\n\t\t\t};\n\t  }\n\t| {\n\t\t\tVector: SuiMoveNormalizedType;\n\t  }\n\t| {\n\t\t\tTypeParameter: number;\n\t  }\n\t| {\n\t\t\tReference: SuiMoveNormalizedType;\n\t  }\n\t| {\n\t\t\tMutableReference: SuiMoveNormalizedType;\n\t  };\nexport interface SuiMoveStructTypeParameter {\n\tconstraints: SuiMoveAbilitySet;\n\tisPhantom: boolean;\n}\nexport type SuiMoveVisibility = 'Private' | 'Public' | 'Friend';\nexport type SuiObjectDataFilter =\n\t| {\n\t\t\tMatchAll: SuiObjectDataFilter[];\n\t  }\n\t| {\n\t\t\tMatchAny: SuiObjectDataFilter[];\n\t  }\n\t| {\n\t\t\tMatchNone: SuiObjectDataFilter[];\n\t  } /** Query by type a specified Package. */\n\t| {\n\t\t\tPackage: string;\n\t  } /** Query by type a specified Move module. */\n\t| {\n\t\t\tMoveModule: {\n\t\t\t\t/** the module name */\n\t\t\t\tmodule: string;\n\t\t\t\t/** the Move package ID */\n\t\t\t\tpackage: string;\n\t\t\t};\n\t  } /** Query by type */\n\t| {\n\t\t\tStructType: string;\n\t  }\n\t| {\n\t\t\tAddressOwner: string;\n\t  }\n\t| {\n\t\t\tObjectOwner: string;\n\t  }\n\t| {\n\t\t\tObjectId: string;\n\t  }\n\t| {\n\t\t\tObjectIds: string[];\n\t  }\n\t| {\n\t\t\tVersion: string;\n\t  };\nexport interface SuiObjectResponse {\n\tdata?: SuiObjectData | null;\n\terror?: ObjectResponseError | null;\n}\n/**\n * The transaction for calling a Move function, either an entry function or a public function (which\n * cannot return references).\n */\nexport interface MoveCallSuiTransaction {\n\t/** The arguments to the function. */\n\targuments?: SuiArgument[];\n\t/** The function to be called. */\n\tfunction: string;\n\t/** The specific module in the package containing the function. */\n\tmodule: string;\n\t/** The package containing the module and function. */\n\tpackage: string;\n\t/** The type arguments to the function. */\n\ttype_arguments?: string[];\n}\n/**\n * This is the JSON-RPC type for the SUI system state object. It flattens all fields to make them\n * top-level fields such that it as minimum dependencies to the internal data structures of the SUI\n * system state type.\n */\nexport interface SuiSystemStateSummary {\n\t/** The list of active validators in the current epoch. */\n\tactiveValidators: SuiValidatorSummary[];\n\t/** Map storing the number of epochs for which each validator has been below the low stake threshold. */\n\tatRiskValidators: [string, string][];\n\t/** The current epoch ID, starting from 0. */\n\tepoch: string;\n\t/** The duration of an epoch, in milliseconds. */\n\tepochDurationMs: string;\n\t/** Unix timestamp of the current epoch start */\n\tepochStartTimestampMs: string;\n\t/**\n\t * ID of the object that maps from a staking pool ID to the inactive validator that has that pool as\n\t * its staking pool.\n\t */\n\tinactivePoolsId: string;\n\t/** Number of inactive staking pools. */\n\tinactivePoolsSize: string;\n\t/**\n\t * Maximum number of active validators at any moment. We do not allow the number of validators in any\n\t * epoch to go above this.\n\t */\n\tmaxValidatorCount: string;\n\t/** Lower-bound on the amount of stake required to become a validator. */\n\tminValidatorJoiningStake: string;\n\t/** ID of the object that contains the list of new validators that will join at the end of the epoch. */\n\tpendingActiveValidatorsId: string;\n\t/** Number of new validators that will join at the end of the epoch. */\n\tpendingActiveValidatorsSize: string;\n\t/** Removal requests from the validators. Each element is an index pointing to `active_validators`. */\n\tpendingRemovals: string[];\n\t/** The current protocol version, starting from 1. */\n\tprotocolVersion: string;\n\t/** The reference gas price for the current epoch. */\n\treferenceGasPrice: string;\n\t/**\n\t * Whether the system is running in a downgraded safe mode due to a non-recoverable bug. This is set\n\t * whenever we failed to execute advance_epoch, and ended up executing advance_epoch_safe_mode. It can\n\t * be reset once we are able to successfully execute advance_epoch.\n\t */\n\tsafeMode: boolean;\n\t/** Amount of computation rewards accumulated (and not yet distributed) during safe mode. */\n\tsafeModeComputationRewards: string;\n\t/** Amount of non-refundable storage fee accumulated during safe mode. */\n\tsafeModeNonRefundableStorageFee: string;\n\t/** Amount of storage rebates accumulated (and not yet burned) during safe mode. */\n\tsafeModeStorageRebates: string;\n\t/** Amount of storage rewards accumulated (and not yet distributed) during safe mode. */\n\tsafeModeStorageRewards: string;\n\t/** Balance of SUI set aside for stake subsidies that will be drawn down over time. */\n\tstakeSubsidyBalance: string;\n\t/** The amount of stake subsidy to be drawn down per epoch. This amount decays and decreases over time. */\n\tstakeSubsidyCurrentDistributionAmount: string;\n\t/**\n\t * The rate at which the distribution amount decays at the end of each period. Expressed in basis\n\t * points.\n\t */\n\tstakeSubsidyDecreaseRate: number;\n\t/**\n\t * This counter may be different from the current epoch number if in some epochs we decide to skip the\n\t * subsidy.\n\t */\n\tstakeSubsidyDistributionCounter: string;\n\t/** Number of distributions to occur before the distribution amount decays. */\n\tstakeSubsidyPeriodLength: string;\n\t/** The starting epoch in which stake subsidies start being paid out */\n\tstakeSubsidyStartEpoch: string;\n\t/** ID of the object that maps from staking pool's ID to the sui address of a validator. */\n\tstakingPoolMappingsId: string;\n\t/** Number of staking pool mappings. */\n\tstakingPoolMappingsSize: string;\n\t/**\n\t * The non-refundable portion of the storage fund coming from storage reinvestment, non-refundable\n\t * storage rebates and any leftover staking rewards.\n\t */\n\tstorageFundNonRefundableBalance: string;\n\t/** The storage rebates of all the objects on-chain stored in the storage fund. */\n\tstorageFundTotalObjectStorageRebates: string;\n\t/** The current version of the system state data structure type. */\n\tsystemStateVersion: string;\n\t/** Total amount of stake from all active validators at the beginning of the epoch. */\n\ttotalStake: string;\n\t/**\n\t * ID of the object that stores preactive validators, mapping their addresses to their `Validator`\n\t * structs.\n\t */\n\tvalidatorCandidatesId: string;\n\t/** Number of preactive validators. */\n\tvalidatorCandidatesSize: string;\n\t/**\n\t * A validator can have stake below `validator_low_stake_threshold` for this many epochs before being\n\t * kicked out.\n\t */\n\tvalidatorLowStakeGracePeriod: string;\n\t/**\n\t * Validators with stake amount below `validator_low_stake_threshold` are considered to have low stake\n\t * and will be escorted out of the validator set after being below this threshold for more than\n\t * `validator_low_stake_grace_period` number of epochs.\n\t */\n\tvalidatorLowStakeThreshold: string;\n\t/** A map storing the records of validator reporting each other. */\n\tvalidatorReportRecords: [string, string[]][];\n\t/**\n\t * Validators with stake below `validator_very_low_stake_threshold` will be removed immediately at\n\t * epoch change, no grace period.\n\t */\n\tvalidatorVeryLowStakeThreshold: string;\n}\n/** A single transaction in a programmable transaction block. */\nexport type SuiTransaction =\n\t/** A call to either an entry or a public Move function */\n\t| {\n\t\t\tMoveCall: MoveCallSuiTransaction;\n\t  } /**\n\t * `(Vec<forall T:key+store. T>, address)` It sends n-objects to the specified address. These objects\n\t * must have store (public transfer) and either the previous owner must be an address or the object\n\t * must be newly created.\n\t */\n\t| {\n\t\t\tTransferObjects: [SuiArgument[], SuiArgument];\n\t  } /**\n\t * `(&mut Coin<T>, Vec<u64>)` -> `Vec<Coin<T>>` It splits off some amounts into a new coins with those\n\t * amounts\n\t */\n\t| {\n\t\t\tSplitCoins: [SuiArgument, SuiArgument[]];\n\t  } /** `(&mut Coin<T>, Vec<Coin<T>>)` It merges n-coins into the first coin */\n\t| {\n\t\t\tMergeCoins: [SuiArgument, SuiArgument[]];\n\t  } /**\n\t * Publishes a Move package. It takes the package bytes and a list of the package's transitive\n\t * dependencies to link against on-chain.\n\t */\n\t| {\n\t\t\tPublish: string[];\n\t  } /** Upgrades a Move package */\n\t| {\n\t\t\tUpgrade: [string[], string, SuiArgument];\n\t  } /**\n\t * `forall T: Vec<T> -> vector<T>` Given n-values of the same type, it constructs a vector. For non\n\t * objects or an empty vector, the type tag must be specified.\n\t */\n\t| {\n\t\t\tMakeMoveVec: [string | null, SuiArgument[]];\n\t  };\nexport type SuiTransactionBlockBuilderMode = 'Commit' | 'DevInspect';\n/**\n * This is the JSON-RPC type for the SUI validator. It flattens all inner structures to top-level\n * fields so that they are decoupled from the internal definitions.\n */\nexport interface SuiValidatorSummary {\n\tcommissionRate: string;\n\tdescription: string;\n\t/** ID of the exchange rate table object. */\n\texchangeRatesId: string;\n\t/** Number of exchange rates in the table. */\n\texchangeRatesSize: string;\n\tgasPrice: string;\n\timageUrl: string;\n\tname: string;\n\tnetAddress: string;\n\tnetworkPubkeyBytes: string;\n\tnextEpochCommissionRate: string;\n\tnextEpochGasPrice: string;\n\tnextEpochNetAddress?: string | null;\n\tnextEpochNetworkPubkeyBytes?: string | null;\n\tnextEpochP2pAddress?: string | null;\n\tnextEpochPrimaryAddress?: string | null;\n\tnextEpochProofOfPossession?: string | null;\n\tnextEpochProtocolPubkeyBytes?: string | null;\n\tnextEpochStake: string;\n\tnextEpochWorkerAddress?: string | null;\n\tnextEpochWorkerPubkeyBytes?: string | null;\n\toperationCapId: string;\n\tp2pAddress: string;\n\t/** Pending pool token withdrawn during the current epoch, emptied at epoch boundaries. */\n\tpendingPoolTokenWithdraw: string;\n\t/** Pending stake amount for this epoch. */\n\tpendingStake: string;\n\t/** Pending stake withdrawn during the current epoch, emptied at epoch boundaries. */\n\tpendingTotalSuiWithdraw: string;\n\t/** Total number of pool tokens issued by the pool. */\n\tpoolTokenBalance: string;\n\tprimaryAddress: string;\n\tprojectUrl: string;\n\tproofOfPossessionBytes: string;\n\tprotocolPubkeyBytes: string;\n\t/** The epoch stake rewards will be added here at the end of each epoch. */\n\trewardsPool: string;\n\t/** The epoch at which this pool became active. */\n\tstakingPoolActivationEpoch?: string | null;\n\t/** The epoch at which this staking pool ceased to be active. `None` = {pre-active, active}, */\n\tstakingPoolDeactivationEpoch?: string | null;\n\t/** ID of the staking pool object. */\n\tstakingPoolId: string;\n\t/** The total number of SUI tokens in this pool. */\n\tstakingPoolSuiBalance: string;\n\tsuiAddress: string;\n\tvotingPower: string;\n\tworkerAddress: string;\n\tworkerPubkeyBytes: string;\n}\nexport interface CoinSupply {\n\tvalue: string;\n}\nexport interface SuiTransactionBlock {\n\tdata: TransactionBlockData;\n\ttxSignatures: string[];\n}\nexport interface TransactionBlockBytes {\n\t/** the gas objects to be used */\n\tgas: SuiObjectRef[];\n\t/** objects to be used in this transaction */\n\tinputObjects: InputObjectKind[];\n\t/** BCS serialized transaction data bytes without its type tag, as base-64 encoded string. */\n\ttxBytes: string;\n}\nexport type TransactionBlockData = {\n\tgasData: SuiGasData;\n\tmessageVersion: 'v1';\n\tsender: string;\n\ttransaction: SuiTransactionBlockKind;\n};\nexport type TransactionEffects =\n\t/** The response from processing a transaction or a certified transaction */\n\t{\n\t\t/** ObjectRef and owner of new objects created. */\n\t\tcreated?: OwnedObjectRef[];\n\t\t/** Object Refs of objects now deleted (the old refs). */\n\t\tdeleted?: SuiObjectRef[];\n\t\t/** The set of transaction digests this transaction depends on. */\n\t\tdependencies?: string[];\n\t\t/**\n\t\t * The digest of the events emitted during execution, can be None if the transaction does not emit any\n\t\t * event.\n\t\t */\n\t\teventsDigest?: string | null;\n\t\t/** The epoch when this transaction was executed. */\n\t\texecutedEpoch: string;\n\t\t/**\n\t\t * The updated gas object reference. Have a dedicated field for convenient access. It's also included\n\t\t * in mutated.\n\t\t */\n\t\tgasObject: OwnedObjectRef;\n\t\tgasUsed: GasCostSummary;\n\t\tmessageVersion: 'v1';\n\t\t/**\n\t\t * The version that every modified (mutated or deleted) object had before it was modified by this\n\t\t * transaction.\n\t\t */\n\t\tmodifiedAtVersions?: TransactionBlockEffectsModifiedAtVersions[];\n\t\t/** ObjectRef and owner of mutated objects, including gas object. */\n\t\tmutated?: OwnedObjectRef[];\n\t\t/**\n\t\t * The object references of the shared objects used in this transaction. Empty if no shared objects\n\t\t * were used.\n\t\t */\n\t\tsharedObjects?: SuiObjectRef[];\n\t\t/** The status of the execution */\n\t\tstatus: ExecutionStatus;\n\t\t/** The transaction digest */\n\t\ttransactionDigest: string;\n\t\t/**\n\t\t * ObjectRef and owner of objects that are unwrapped in this transaction. Unwrapped objects are objects\n\t\t * that were wrapped into other objects in the past, and just got extracted out.\n\t\t */\n\t\tunwrapped?: OwnedObjectRef[];\n\t\t/** Object refs of objects previously wrapped in other objects but now deleted. */\n\t\tunwrappedThenDeleted?: SuiObjectRef[];\n\t\t/** Object refs of objects now wrapped in other objects. */\n\t\twrapped?: SuiObjectRef[];\n\t};\nexport interface TransactionBlockEffectsModifiedAtVersions {\n\tobjectId: string;\n\tsequenceNumber: string;\n}\nexport type SuiTransactionBlockKind =\n\t/** A system transaction that will update epoch information on-chain. */\n\t| {\n\t\t\tcomputation_charge: string;\n\t\t\tepoch: string;\n\t\t\tepoch_start_timestamp_ms: string;\n\t\t\tkind: 'ChangeEpoch';\n\t\t\tstorage_charge: string;\n\t\t\tstorage_rebate: string;\n\t  } /** A system transaction used for initializing the initial state of the chain. */\n\t| {\n\t\t\tkind: 'Genesis';\n\t\t\tobjects: string[];\n\t  } /** A system transaction marking the start of a series of transactions scheduled as part of a checkpoint */\n\t| {\n\t\t\tcommit_timestamp_ms: string;\n\t\t\tepoch: string;\n\t\t\tkind: 'ConsensusCommitPrologue';\n\t\t\tround: string;\n\t  } /** A series of transactions where the results of one transaction can be used in future transactions */\n\t| {\n\t\t\t/** Input objects or primitive values */\n\t\t\tinputs: SuiCallArg[];\n\t\t\tkind: 'ProgrammableTransaction';\n\t\t\t/**\n\t\t\t * The transactions to be executed sequentially. A failure in any transaction will result in the\n\t\t\t * failure of the entire programmable transaction block.\n\t\t\t */\n\t\t\ttransactions: SuiTransaction[];\n\t  } /** A transaction which updates global authenticator state */\n\t| {\n\t\t\tepoch: string;\n\t\t\tkind: 'AuthenticatorStateUpdate';\n\t\t\tnew_active_jwks: SuiActiveJwk[];\n\t\t\tround: string;\n\t  } /** A transaction which updates global randomness state */\n\t| {\n\t\t\tepoch: string;\n\t\t\tkind: 'RandomnessStateUpdate';\n\t\t\trandom_bytes: number[];\n\t\t\trandomness_round: string;\n\t  } /** The transaction which occurs only at the end of the epoch */\n\t| {\n\t\t\tkind: 'EndOfEpochTransaction';\n\t\t\ttransactions: SuiEndOfEpochTransactionKind[];\n\t  }\n\t| {\n\t\t\tcommit_timestamp_ms: string;\n\t\t\tconsensus_commit_digest: string;\n\t\t\tepoch: string;\n\t\t\tkind: 'ConsensusCommitPrologueV2';\n\t\t\tround: string;\n\t  }\n\t| {\n\t\t\tcommit_timestamp_ms: string;\n\t\t\tconsensus_commit_digest: string;\n\t\t\tconsensus_determined_version_assignments: ConsensusDeterminedVersionAssignments;\n\t\t\tepoch: string;\n\t\t\tkind: 'ConsensusCommitPrologueV3';\n\t\t\tround: string;\n\t\t\tsub_dag_index?: string | null;\n\t  };\nexport interface SuiTransactionBlockResponse {\n\tbalanceChanges?: BalanceChange[] | null;\n\t/**\n\t * The checkpoint number when this transaction was included and hence finalized. This is only returned\n\t * in the read api, not in the transaction execution api.\n\t */\n\tcheckpoint?: string | null;\n\tconfirmedLocalExecution?: boolean | null;\n\tdigest: string;\n\teffects?: TransactionEffects | null;\n\terrors?: string[];\n\tevents?: SuiEvent[] | null;\n\tobjectChanges?: SuiObjectChange[] | null;\n\trawEffects?: number[];\n\t/**\n\t * BCS encoded [SenderSignedData] that includes input object references returns empty array if\n\t * `show_raw_transaction` is false\n\t */\n\trawTransaction?: string;\n\ttimestampMs?: string | null;\n\t/** Transaction input data */\n\ttransaction?: SuiTransactionBlock | null;\n}\nexport interface SuiTransactionBlockResponseOptions {\n\t/** Whether to show balance_changes. Default to be False */\n\tshowBalanceChanges?: boolean;\n\t/** Whether to show transaction effects. Default to be False */\n\tshowEffects?: boolean;\n\t/** Whether to show transaction events. Default to be False */\n\tshowEvents?: boolean;\n\t/** Whether to show transaction input data. Default to be False */\n\tshowInput?: boolean;\n\t/** Whether to show object_changes. Default to be False */\n\tshowObjectChanges?: boolean;\n\t/** Whether to show raw transaction effects. Default to be False */\n\tshowRawEffects?: boolean;\n\t/** Whether to show bcs-encoded transaction input data */\n\tshowRawInput?: boolean;\n}\nexport interface SuiTransactionBlockResponseQuery {\n\t/** If None, no filter will be applied */\n\tfilter?: TransactionFilter | null;\n\t/** config which fields to include in the response, by default only digest is included */\n\toptions?: SuiTransactionBlockResponseOptions | null;\n}\nexport type TransactionFilter =\n\t/** CURRENTLY NOT SUPPORTED. Query by checkpoint. */\n\t| {\n\t\t\tCheckpoint: string;\n\t  } /** Query by move function. */\n\t| {\n\t\t\tMoveFunction: {\n\t\t\t\tfunction?: string | null;\n\t\t\t\tmodule?: string | null;\n\t\t\t\tpackage: string;\n\t\t\t};\n\t  } /** Query by input object. */\n\t| {\n\t\t\tInputObject: string;\n\t  } /** Query by changed object, including created, mutated and unwrapped objects. */\n\t| {\n\t\t\tChangedObject: string;\n\t  } /** Query for transactions that touch this object. */\n\t| {\n\t\t\tAffectedObject: string;\n\t  } /** Query by sender address. */\n\t| {\n\t\t\tFromAddress: string;\n\t  } /** Query by recipient address. */\n\t| {\n\t\t\tToAddress: string;\n\t  } /** Query by sender and recipient address. */\n\t| {\n\t\t\tFromAndToAddress: {\n\t\t\t\tfrom: string;\n\t\t\t\tto: string;\n\t\t\t};\n\t  } /** CURRENTLY NOT SUPPORTED. Query txs that have a given address as sender or recipient. */\n\t| {\n\t\t\tFromOrToAddress: {\n\t\t\t\taddr: string;\n\t\t\t};\n\t  } /** Query by transaction kind */\n\t| {\n\t\t\tTransactionKind: string;\n\t  } /** Query transactions of any given kind in the input. */\n\t| {\n\t\t\tTransactionKindIn: string[];\n\t  };\nexport interface TransferObjectParams {\n\tobjectId: string;\n\trecipient: string;\n}\n/** Identifies a struct and the module it was defined in */\nexport interface TypeOrigin {\n\tdatatype_name: string;\n\tmodule_name: string;\n\tpackage: string;\n}\n/** Upgraded package info for the linkage table */\nexport interface UpgradeInfo {\n\t/** ID of the upgraded packages */\n\tupgraded_id: string;\n\t/** Version of the upgraded package */\n\tupgraded_version: string;\n}\nexport interface ValidatorApy {\n\taddress: string;\n\tapy: number;\n}\nexport interface ValidatorsApy {\n\tapys: ValidatorApy[];\n\tepoch: string;\n}\n/** An zk login authenticator with all the necessary fields. */\nexport interface ZkLoginAuthenticator {\n\tinputs: ZkLoginInputs;\n\tmaxEpoch: string;\n\tuserSignature: Signature;\n}\n/** All inputs required for the zk login proof verification and other public inputs. */\nexport interface ZkLoginInputs {\n\taddressSeed: string;\n\theaderBase64: string;\n\tissBase64Details: Claim;\n\tproofPoints: ZkLoginProof;\n}\n/** The struct for zk login proof. */\nexport interface ZkLoginProof {\n\ta: string[];\n\tb: string[][];\n\tc: string[];\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}