{"version": 3, "sources": ["../../../src/client/network.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport function getFullnodeUrl(network: 'mainnet' | 'testnet' | 'devnet' | 'localnet') {\n\tswitch (network) {\n\t\tcase 'mainnet':\n\t\t\treturn 'https://fullnode.mainnet.sui.io:443';\n\t\tcase 'testnet':\n\t\t\treturn 'https://fullnode.testnet.sui.io:443';\n\t\tcase 'devnet':\n\t\t\treturn 'https://fullnode.devnet.sui.io:443';\n\t\tcase 'localnet':\n\t\t\treturn 'http://127.0.0.1:9000';\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown network: ${network}`);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,eAAe,SAAwD;AACtF,UAAQ,SAAS;AAAA,IAChB,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR;AACC,YAAM,IAAI,MAAM,oBAAoB,OAAO,EAAE;AAAA,EAC/C;AACD;", "names": []}