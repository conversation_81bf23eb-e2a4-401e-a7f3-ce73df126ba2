{"version": 3, "sources": ["../../../src/client/client.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\nimport { fromBase58, toBase64, toHex } from '@mysten/bcs';\n\nimport type { Signer } from '../cryptography/index.js';\nimport type { Transaction } from '../transactions/index.js';\nimport { isTransaction } from '../transactions/index.js';\nimport {\n\tisValidSuiAddress,\n\tisValidSuiObjectId,\n\tisValidTransactionDigest,\n\tnormalizeSuiAddress,\n\tnormalizeSuiObjectId,\n} from '../utils/sui-types.js';\nimport { normalizeSuiNSName } from '../utils/suins.js';\nimport { SuiHTTPTransport } from './http-transport.js';\nimport type { SuiTransport } from './http-transport.js';\nimport type {\n\tAddressMetrics,\n\tAllEpochsAddressMetrics,\n\tCheckpoint,\n\tCheckpointPage,\n\tCoinBalance,\n\tCoinMetadata,\n\tCoinSupply,\n\tCommitteeInfo,\n\tDelegatedStake,\n\tDevInspectResults,\n\tDevInspectTransactionBlockParams,\n\tDryRunTransactionBlockParams,\n\tDryRunTransactionBlockResponse,\n\tDynamicFieldPage,\n\tEpochInfo,\n\tEpochMetricsPage,\n\tEpochPage,\n\tExecuteTransactionBlockParams,\n\tGetAllBalancesParams,\n\tGetAllCoinsParams,\n\tGetBalanceParams,\n\tGetCheckpointParams,\n\tGetCheckpointsParams,\n\tGetCoinMetadataParams,\n\tGetCoinsParams,\n\tGetCommitteeInfoParams,\n\tGetDynamicFieldObjectParams,\n\tGetDynamicFieldsParams,\n\tGetMoveFunctionArgTypesParams,\n\tGetNormalizedMoveFunctionParams,\n\tGetNormalizedMoveModuleParams,\n\tGetNormalizedMoveModulesByPackageParams,\n\tGetNormalizedMoveStructParams,\n\tGetObjectParams,\n\tGetOwnedObjectsParams,\n\tGetProtocolConfigParams,\n\tGetStakesByIdsParams,\n\tGetStakesParams,\n\tGetTotalSupplyParams,\n\tGetTransactionBlockParams,\n\tMoveCallMetrics,\n\tMultiGetObjectsParams,\n\tMultiGetTransactionBlocksParams,\n\tNetworkMetrics,\n\tObjectRead,\n\tOrder,\n\tPaginatedCoins,\n\tPaginatedEvents,\n\tPaginatedObjectsResponse,\n\tPaginatedTransactionResponse,\n\tProtocolConfig,\n\tQueryEventsParams,\n\tQueryTransactionBlocksParams,\n\tResolvedNameServiceNames,\n\tResolveNameServiceAddressParams,\n\tResolveNameServiceNamesParams,\n\tSubscribeEventParams,\n\tSubscribeTransactionParams,\n\tSuiEvent,\n\tSuiMoveFunctionArgType,\n\tSuiMoveNormalizedFunction,\n\tSuiMoveNormalizedModule,\n\tSuiMoveNormalizedModules,\n\tSuiMoveNormalizedStruct,\n\tSuiObjectResponse,\n\tSuiObjectResponseQuery,\n\tSuiSystemStateSummary,\n\tSuiTransactionBlockResponse,\n\tSuiTransactionBlockResponseQuery,\n\tTransactionEffects,\n\tTryGetPastObjectParams,\n\tUnsubscribe,\n\tValidatorsApy,\n} from './types/index.js';\n\nexport interface PaginationArguments<Cursor> {\n\t/** Optional paging cursor */\n\tcursor?: Cursor;\n\t/** Maximum item returned per page */\n\tlimit?: number | null;\n}\n\nexport interface OrderArguments {\n\torder?: Order | null;\n}\n\n/**\n * Configuration options for the SuiClient\n * You must provide either a `url` or a `transport`\n */\nexport type SuiClientOptions = NetworkOrTransport;\n\ntype NetworkOrTransport =\n\t| {\n\t\t\turl: string;\n\t\t\ttransport?: never;\n\t  }\n\t| {\n\t\t\ttransport: SuiTransport;\n\t\t\turl?: never;\n\t  };\n\nconst SUI_CLIENT_BRAND = Symbol.for('@mysten/SuiClient') as never;\n\nexport function isSuiClient(client: unknown): client is SuiClient {\n\treturn (\n\t\ttypeof client === 'object' && client !== null && (client as any)[SUI_CLIENT_BRAND] === true\n\t);\n}\n\nexport class SuiClient {\n\tprotected transport: SuiTransport;\n\n\tget [SUI_CLIENT_BRAND]() {\n\t\treturn true;\n\t}\n\n\t/**\n\t * Establish a connection to a Sui RPC endpoint\n\t *\n\t * @param options configuration options for the API Client\n\t */\n\tconstructor(options: SuiClientOptions) {\n\t\tthis.transport = options.transport ?? new SuiHTTPTransport({ url: options.url });\n\t}\n\n\tasync getRpcApiVersion(): Promise<string | undefined> {\n\t\tconst resp = await this.transport.request<{ info: { version: string } }>({\n\t\t\tmethod: 'rpc.discover',\n\t\t\tparams: [],\n\t\t});\n\n\t\treturn resp.info.version;\n\t}\n\n\t/**\n\t * Get all Coin<`coin_type`> objects owned by an address.\n\t */\n\tasync getCoins(input: GetCoinsParams): Promise<PaginatedCoins> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCoins',\n\t\t\tparams: [input.owner, input.coinType, input.cursor, input.limit],\n\t\t});\n\t}\n\n\t/**\n\t * Get all Coin objects owned by an address.\n\t */\n\tasync getAllCoins(input: GetAllCoinsParams): Promise<PaginatedCoins> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getAllCoins',\n\t\t\tparams: [input.owner, input.cursor, input.limit],\n\t\t});\n\t}\n\n\t/**\n\t * Get the total coin balance for one coin type, owned by the address owner.\n\t */\n\tasync getBalance(input: GetBalanceParams): Promise<CoinBalance> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getBalance',\n\t\t\tparams: [input.owner, input.coinType],\n\t\t});\n\t}\n\n\t/**\n\t * Get the total coin balance for all coin types, owned by the address owner.\n\t */\n\tasync getAllBalances(input: GetAllBalancesParams): Promise<CoinBalance[]> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\t\treturn await this.transport.request({ method: 'suix_getAllBalances', params: [input.owner] });\n\t}\n\n\t/**\n\t * Fetch CoinMetadata for a given coin type\n\t */\n\tasync getCoinMetadata(input: GetCoinMetadataParams): Promise<CoinMetadata | null> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCoinMetadata',\n\t\t\tparams: [input.coinType],\n\t\t});\n\t}\n\n\t/**\n\t *  Fetch total supply for a coin\n\t */\n\tasync getTotalSupply(input: GetTotalSupplyParams): Promise<CoinSupply> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getTotalSupply',\n\t\t\tparams: [input.coinType],\n\t\t});\n\t}\n\n\t/**\n\t * Invoke any RPC method\n\t * @param method the method to be invoked\n\t * @param args the arguments to be passed to the RPC request\n\t */\n\tasync call<T = unknown>(method: string, params: unknown[]): Promise<T> {\n\t\treturn await this.transport.request({ method, params });\n\t}\n\n\t/**\n\t * Get Move function argument types like read, write and full access\n\t */\n\tasync getMoveFunctionArgTypes(\n\t\tinput: GetMoveFunctionArgTypesParams,\n\t): Promise<SuiMoveFunctionArgType[]> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getMoveFunctionArgTypes',\n\t\t\tparams: [input.package, input.module, input.function],\n\t\t});\n\t}\n\n\t/**\n\t * Get a map from module name to\n\t * structured representations of Move modules\n\t */\n\tasync getNormalizedMoveModulesByPackage(\n\t\tinput: GetNormalizedMoveModulesByPackageParams,\n\t): Promise<SuiMoveNormalizedModules> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveModulesByPackage',\n\t\t\tparams: [input.package],\n\t\t});\n\t}\n\n\t/**\n\t * Get a structured representation of Move module\n\t */\n\tasync getNormalizedMoveModule(\n\t\tinput: GetNormalizedMoveModuleParams,\n\t): Promise<SuiMoveNormalizedModule> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveModule',\n\t\t\tparams: [input.package, input.module],\n\t\t});\n\t}\n\n\t/**\n\t * Get a structured representation of Move function\n\t */\n\tasync getNormalizedMoveFunction(\n\t\tinput: GetNormalizedMoveFunctionParams,\n\t): Promise<SuiMoveNormalizedFunction> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveFunction',\n\t\t\tparams: [input.package, input.module, input.function],\n\t\t});\n\t}\n\n\t/**\n\t * Get a structured representation of Move struct\n\t */\n\tasync getNormalizedMoveStruct(\n\t\tinput: GetNormalizedMoveStructParams,\n\t): Promise<SuiMoveNormalizedStruct> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveStruct',\n\t\t\tparams: [input.package, input.module, input.struct],\n\t\t});\n\t}\n\n\t/**\n\t * Get all objects owned by an address\n\t */\n\tasync getOwnedObjects(input: GetOwnedObjectsParams): Promise<PaginatedObjectsResponse> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getOwnedObjects',\n\t\t\tparams: [\n\t\t\t\tinput.owner,\n\t\t\t\t{\n\t\t\t\t\tfilter: input.filter,\n\t\t\t\t\toptions: input.options,\n\t\t\t\t} as SuiObjectResponseQuery,\n\t\t\t\tinput.cursor,\n\t\t\t\tinput.limit,\n\t\t\t],\n\t\t});\n\t}\n\n\t/**\n\t * Get details about an object\n\t */\n\tasync getObject(input: GetObjectParams): Promise<SuiObjectResponse> {\n\t\tif (!input.id || !isValidSuiObjectId(normalizeSuiObjectId(input.id))) {\n\t\t\tthrow new Error('Invalid Sui Object id');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getObject',\n\t\t\tparams: [input.id, input.options],\n\t\t});\n\t}\n\n\tasync tryGetPastObject(input: TryGetPastObjectParams): Promise<ObjectRead> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_tryGetPastObject',\n\t\t\tparams: [input.id, input.version, input.options],\n\t\t});\n\t}\n\n\t/**\n\t * Batch get details about a list of objects. If any of the object ids are duplicates the call will fail\n\t */\n\tasync multiGetObjects(input: MultiGetObjectsParams): Promise<SuiObjectResponse[]> {\n\t\tinput.ids.forEach((id) => {\n\t\t\tif (!id || !isValidSuiObjectId(normalizeSuiObjectId(id))) {\n\t\t\t\tthrow new Error(`Invalid Sui Object id ${id}`);\n\t\t\t}\n\t\t});\n\t\tconst hasDuplicates = input.ids.length !== new Set(input.ids).size;\n\t\tif (hasDuplicates) {\n\t\t\tthrow new Error(`Duplicate object ids in batch call ${input.ids}`);\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_multiGetObjects',\n\t\t\tparams: [input.ids, input.options],\n\t\t});\n\t}\n\n\t/**\n\t * Get transaction blocks for a given query criteria\n\t */\n\tasync queryTransactionBlocks(\n\t\tinput: QueryTransactionBlocksParams,\n\t): Promise<PaginatedTransactionResponse> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_queryTransactionBlocks',\n\t\t\tparams: [\n\t\t\t\t{\n\t\t\t\t\tfilter: input.filter,\n\t\t\t\t\toptions: input.options,\n\t\t\t\t} as SuiTransactionBlockResponseQuery,\n\t\t\t\tinput.cursor,\n\t\t\t\tinput.limit,\n\t\t\t\t(input.order || 'descending') === 'descending',\n\t\t\t],\n\t\t});\n\t}\n\n\tasync getTransactionBlock(\n\t\tinput: GetTransactionBlockParams,\n\t): Promise<SuiTransactionBlockResponse> {\n\t\tif (!isValidTransactionDigest(input.digest)) {\n\t\t\tthrow new Error('Invalid Transaction digest');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getTransactionBlock',\n\t\t\tparams: [input.digest, input.options],\n\t\t});\n\t}\n\n\tasync multiGetTransactionBlocks(\n\t\tinput: MultiGetTransactionBlocksParams,\n\t): Promise<SuiTransactionBlockResponse[]> {\n\t\tinput.digests.forEach((d) => {\n\t\t\tif (!isValidTransactionDigest(d)) {\n\t\t\t\tthrow new Error(`Invalid Transaction digest ${d}`);\n\t\t\t}\n\t\t});\n\n\t\tconst hasDuplicates = input.digests.length !== new Set(input.digests).size;\n\t\tif (hasDuplicates) {\n\t\t\tthrow new Error(`Duplicate digests in batch call ${input.digests}`);\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_multiGetTransactionBlocks',\n\t\t\tparams: [input.digests, input.options],\n\t\t});\n\t}\n\n\tasync executeTransactionBlock({\n\t\ttransactionBlock,\n\t\tsignature,\n\t\toptions,\n\t\trequestType,\n\t}: ExecuteTransactionBlockParams): Promise<SuiTransactionBlockResponse> {\n\t\tconst result: SuiTransactionBlockResponse = await this.transport.request({\n\t\t\tmethod: 'sui_executeTransactionBlock',\n\t\t\tparams: [\n\t\t\t\ttypeof transactionBlock === 'string' ? transactionBlock : toBase64(transactionBlock),\n\t\t\t\tArray.isArray(signature) ? signature : [signature],\n\t\t\t\toptions,\n\t\t\t],\n\t\t});\n\n\t\tif (requestType === 'WaitForLocalExecution') {\n\t\t\ttry {\n\t\t\t\tawait this.waitForTransaction({\n\t\t\t\t\tdigest: result.digest,\n\t\t\t\t});\n\t\t\t} catch (_) {\n\t\t\t\t// Ignore error while waiting for transaction\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tasync signAndExecuteTransaction({\n\t\ttransaction,\n\t\tsigner,\n\t\t...input\n\t}: {\n\t\ttransaction: Uint8Array | Transaction;\n\t\tsigner: Signer;\n\t} & Omit<\n\t\tExecuteTransactionBlockParams,\n\t\t'transactionBlock' | 'signature'\n\t>): Promise<SuiTransactionBlockResponse> {\n\t\tlet transactionBytes;\n\n\t\tif (transaction instanceof Uint8Array) {\n\t\t\ttransactionBytes = transaction;\n\t\t} else {\n\t\t\ttransaction.setSenderIfNotSet(signer.toSuiAddress());\n\t\t\ttransactionBytes = await transaction.build({ client: this });\n\t\t}\n\n\t\tconst { signature, bytes } = await signer.signTransaction(transactionBytes);\n\n\t\treturn this.executeTransactionBlock({\n\t\t\ttransactionBlock: bytes,\n\t\t\tsignature,\n\t\t\t...input,\n\t\t});\n\t}\n\n\t/**\n\t * Get total number of transactions\n\t */\n\n\tasync getTotalTransactionBlocks(): Promise<bigint> {\n\t\tconst resp = await this.transport.request<string>({\n\t\t\tmethod: 'sui_getTotalTransactionBlocks',\n\t\t\tparams: [],\n\t\t});\n\t\treturn BigInt(resp);\n\t}\n\n\t/**\n\t * Getting the reference gas price for the network\n\t */\n\tasync getReferenceGasPrice(): Promise<bigint> {\n\t\tconst resp = await this.transport.request<string>({\n\t\t\tmethod: 'suix_getReferenceGasPrice',\n\t\t\tparams: [],\n\t\t});\n\t\treturn BigInt(resp);\n\t}\n\n\t/**\n\t * Return the delegated stakes for an address\n\t */\n\tasync getStakes(input: GetStakesParams): Promise<DelegatedStake[]> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\t\treturn await this.transport.request({ method: 'suix_getStakes', params: [input.owner] });\n\t}\n\n\t/**\n\t * Return the delegated stakes queried by id.\n\t */\n\tasync getStakesByIds(input: GetStakesByIdsParams): Promise<DelegatedStake[]> {\n\t\tinput.stakedSuiIds.forEach((id) => {\n\t\t\tif (!id || !isValidSuiObjectId(normalizeSuiObjectId(id))) {\n\t\t\t\tthrow new Error(`Invalid Sui Stake id ${id}`);\n\t\t\t}\n\t\t});\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getStakesByIds',\n\t\t\tparams: [input.stakedSuiIds],\n\t\t});\n\t}\n\n\t/**\n\t * Return the latest system state content.\n\t */\n\tasync getLatestSuiSystemState(): Promise<SuiSystemStateSummary> {\n\t\treturn await this.transport.request({ method: 'suix_getLatestSuiSystemState', params: [] });\n\t}\n\n\t/**\n\t * Get events for a given query criteria\n\t */\n\tasync queryEvents(input: QueryEventsParams): Promise<PaginatedEvents> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_queryEvents',\n\t\t\tparams: [\n\t\t\t\tinput.query,\n\t\t\t\tinput.cursor,\n\t\t\t\tinput.limit,\n\t\t\t\t(input.order || 'descending') === 'descending',\n\t\t\t],\n\t\t});\n\t}\n\n\t/**\n\t * Subscribe to get notifications whenever an event matching the filter occurs\n\t *\n\t * @deprecated\n\t */\n\tasync subscribeEvent(\n\t\tinput: SubscribeEventParams & {\n\t\t\t/** function to run when we receive a notification of a new event matching the filter */\n\t\t\tonMessage: (event: SuiEvent) => void;\n\t\t},\n\t): Promise<Unsubscribe> {\n\t\treturn this.transport.subscribe({\n\t\t\tmethod: 'suix_subscribeEvent',\n\t\t\tunsubscribe: 'suix_unsubscribeEvent',\n\t\t\tparams: [input.filter],\n\t\t\tonMessage: input.onMessage,\n\t\t});\n\t}\n\n\t/**\n\t * @deprecated\n\t */\n\tasync subscribeTransaction(\n\t\tinput: SubscribeTransactionParams & {\n\t\t\t/** function to run when we receive a notification of a new event matching the filter */\n\t\t\tonMessage: (event: TransactionEffects) => void;\n\t\t},\n\t): Promise<Unsubscribe> {\n\t\treturn this.transport.subscribe({\n\t\t\tmethod: 'suix_subscribeTransaction',\n\t\t\tunsubscribe: 'suix_unsubscribeTransaction',\n\t\t\tparams: [input.filter],\n\t\t\tonMessage: input.onMessage,\n\t\t});\n\t}\n\n\t/**\n\t * Runs the transaction block in dev-inspect mode. Which allows for nearly any\n\t * transaction (or Move call) with any arguments. Detailed results are\n\t * provided, including both the transaction effects and any return values.\n\t */\n\tasync devInspectTransactionBlock(\n\t\tinput: DevInspectTransactionBlockParams,\n\t): Promise<DevInspectResults> {\n\t\tlet devInspectTxBytes;\n\t\tif (isTransaction(input.transactionBlock)) {\n\t\t\tinput.transactionBlock.setSenderIfNotSet(input.sender);\n\t\t\tdevInspectTxBytes = toBase64(\n\t\t\t\tawait input.transactionBlock.build({\n\t\t\t\t\tclient: this,\n\t\t\t\t\tonlyTransactionKind: true,\n\t\t\t\t}),\n\t\t\t);\n\t\t} else if (typeof input.transactionBlock === 'string') {\n\t\t\tdevInspectTxBytes = input.transactionBlock;\n\t\t} else if (input.transactionBlock instanceof Uint8Array) {\n\t\t\tdevInspectTxBytes = toBase64(input.transactionBlock);\n\t\t} else {\n\t\t\tthrow new Error('Unknown transaction block format.');\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_devInspectTransactionBlock',\n\t\t\tparams: [input.sender, devInspectTxBytes, input.gasPrice?.toString(), input.epoch],\n\t\t});\n\t}\n\n\t/**\n\t * Dry run a transaction block and return the result.\n\t */\n\tasync dryRunTransactionBlock(\n\t\tinput: DryRunTransactionBlockParams,\n\t): Promise<DryRunTransactionBlockResponse> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_dryRunTransactionBlock',\n\t\t\tparams: [\n\t\t\t\ttypeof input.transactionBlock === 'string'\n\t\t\t\t\t? input.transactionBlock\n\t\t\t\t\t: toBase64(input.transactionBlock),\n\t\t\t],\n\t\t});\n\t}\n\n\t/**\n\t * Return the list of dynamic field objects owned by an object\n\t */\n\tasync getDynamicFields(input: GetDynamicFieldsParams): Promise<DynamicFieldPage> {\n\t\tif (!input.parentId || !isValidSuiObjectId(normalizeSuiObjectId(input.parentId))) {\n\t\t\tthrow new Error('Invalid Sui Object id');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getDynamicFields',\n\t\t\tparams: [input.parentId, input.cursor, input.limit],\n\t\t});\n\t}\n\n\t/**\n\t * Return the dynamic field object information for a specified object\n\t */\n\tasync getDynamicFieldObject(input: GetDynamicFieldObjectParams): Promise<SuiObjectResponse> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getDynamicFieldObject',\n\t\t\tparams: [input.parentId, input.name],\n\t\t});\n\t}\n\n\t/**\n\t * Get the sequence number of the latest checkpoint that has been executed\n\t */\n\tasync getLatestCheckpointSequenceNumber(): Promise<string> {\n\t\tconst resp = await this.transport.request({\n\t\t\tmethod: 'sui_getLatestCheckpointSequenceNumber',\n\t\t\tparams: [],\n\t\t});\n\t\treturn String(resp);\n\t}\n\n\t/**\n\t * Returns information about a given checkpoint\n\t */\n\tasync getCheckpoint(input: GetCheckpointParams): Promise<Checkpoint> {\n\t\treturn await this.transport.request({ method: 'sui_getCheckpoint', params: [input.id] });\n\t}\n\n\t/**\n\t * Returns historical checkpoints paginated\n\t */\n\tasync getCheckpoints(\n\t\tinput: PaginationArguments<CheckpointPage['nextCursor']> & GetCheckpointsParams,\n\t): Promise<CheckpointPage> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getCheckpoints',\n\t\t\tparams: [input.cursor, input?.limit, input.descendingOrder],\n\t\t});\n\t}\n\n\t/**\n\t * Return the committee information for the asked epoch\n\t */\n\tasync getCommitteeInfo(input?: GetCommitteeInfoParams): Promise<CommitteeInfo> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCommitteeInfo',\n\t\t\tparams: [input?.epoch],\n\t\t});\n\t}\n\n\tasync getNetworkMetrics(): Promise<NetworkMetrics> {\n\t\treturn await this.transport.request({ method: 'suix_getNetworkMetrics', params: [] });\n\t}\n\n\tasync getAddressMetrics(): Promise<AddressMetrics> {\n\t\treturn await this.transport.request({ method: 'suix_getLatestAddressMetrics', params: [] });\n\t}\n\n\tasync getEpochMetrics(\n\t\tinput?: { descendingOrder?: boolean } & PaginationArguments<EpochMetricsPage['nextCursor']>,\n\t): Promise<EpochMetricsPage> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getEpochMetrics',\n\t\t\tparams: [input?.cursor, input?.limit, input?.descendingOrder],\n\t\t});\n\t}\n\n\tasync getAllEpochAddressMetrics(input?: {\n\t\tdescendingOrder?: boolean;\n\t}): Promise<AllEpochsAddressMetrics> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getAllEpochAddressMetrics',\n\t\t\tparams: [input?.descendingOrder],\n\t\t});\n\t}\n\n\t/**\n\t * Return the committee information for the asked epoch\n\t */\n\tasync getEpochs(\n\t\tinput?: {\n\t\t\tdescendingOrder?: boolean;\n\t\t} & PaginationArguments<EpochPage['nextCursor']>,\n\t): Promise<EpochPage> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getEpochs',\n\t\t\tparams: [input?.cursor, input?.limit, input?.descendingOrder],\n\t\t});\n\t}\n\n\t/**\n\t * Returns list of top move calls by usage\n\t */\n\tasync getMoveCallMetrics(): Promise<MoveCallMetrics> {\n\t\treturn await this.transport.request({ method: 'suix_getMoveCallMetrics', params: [] });\n\t}\n\n\t/**\n\t * Return the committee information for the asked epoch\n\t */\n\tasync getCurrentEpoch(): Promise<EpochInfo> {\n\t\treturn await this.transport.request({ method: 'suix_getCurrentEpoch', params: [] });\n\t}\n\n\t/**\n\t * Return the Validators APYs\n\t */\n\tasync getValidatorsApy(): Promise<ValidatorsApy> {\n\t\treturn await this.transport.request({ method: 'suix_getValidatorsApy', params: [] });\n\t}\n\n\t// TODO: Migrate this to `sui_getChainIdentifier` once it is widely available.\n\tasync getChainIdentifier(): Promise<string> {\n\t\tconst checkpoint = await this.getCheckpoint({ id: '0' });\n\t\tconst bytes = fromBase58(checkpoint.digest);\n\t\treturn toHex(bytes.slice(0, 4));\n\t}\n\n\tasync resolveNameServiceAddress(input: ResolveNameServiceAddressParams): Promise<string | null> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_resolveNameServiceAddress',\n\t\t\tparams: [input.name],\n\t\t});\n\t}\n\n\tasync resolveNameServiceNames({\n\t\tformat = 'dot',\n\t\t...input\n\t}: ResolveNameServiceNamesParams & {\n\t\tformat?: 'at' | 'dot';\n\t}): Promise<ResolvedNameServiceNames> {\n\t\tconst { nextCursor, hasNextPage, data }: ResolvedNameServiceNames =\n\t\t\tawait this.transport.request({\n\t\t\t\tmethod: 'suix_resolveNameServiceNames',\n\t\t\t\tparams: [input.address, input.cursor, input.limit],\n\t\t\t});\n\n\t\treturn {\n\t\t\thasNextPage,\n\t\t\tnextCursor,\n\t\t\tdata: data.map((name) => normalizeSuiNSName(name, format)),\n\t\t};\n\t}\n\n\tasync getProtocolConfig(input?: GetProtocolConfigParams): Promise<ProtocolConfig> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getProtocolConfig',\n\t\t\tparams: [input?.version],\n\t\t});\n\t}\n\n\t/**\n\t * Wait for a transaction block result to be available over the API.\n\t * This can be used in conjunction with `executeTransactionBlock` to wait for the transaction to\n\t * be available via the API.\n\t * This currently polls the `getTransactionBlock` API to check for the transaction.\n\t */\n\tasync waitForTransaction({\n\t\tsignal,\n\t\ttimeout = 60 * 1000,\n\t\tpollInterval = 2 * 1000,\n\t\t...input\n\t}: {\n\t\t/** An optional abort signal that can be used to cancel */\n\t\tsignal?: AbortSignal;\n\t\t/** The amount of time to wait for a transaction block. Defaults to one minute. */\n\t\ttimeout?: number;\n\t\t/** The amount of time to wait between checks for the transaction block. Defaults to 2 seconds. */\n\t\tpollInterval?: number;\n\t} & Parameters<SuiClient['getTransactionBlock']>[0]): Promise<SuiTransactionBlockResponse> {\n\t\tconst timeoutSignal = AbortSignal.timeout(timeout);\n\t\tconst timeoutPromise = new Promise((_, reject) => {\n\t\t\ttimeoutSignal.addEventListener('abort', () => reject(timeoutSignal.reason));\n\t\t});\n\n\t\ttimeoutPromise.catch(() => {\n\t\t\t// Swallow unhandled rejections that might be thrown after early return\n\t\t});\n\n\t\twhile (!timeoutSignal.aborted) {\n\t\t\tsignal?.throwIfAborted();\n\t\t\ttry {\n\t\t\t\treturn await this.getTransactionBlock(input);\n\t\t\t} catch (e) {\n\t\t\t\t// Wait for either the next poll interval, or the timeout.\n\t\t\t\tawait Promise.race([\n\t\t\t\t\tnew Promise((resolve) => setTimeout(resolve, pollInterval)),\n\t\t\t\t\ttimeoutPromise,\n\t\t\t\t]);\n\t\t\t}\n\t\t}\n\n\t\ttimeoutSignal.throwIfAborted();\n\n\t\t// This should never happen, because the above case should always throw, but just adding it in the event that something goes horribly wrong.\n\t\tthrow new Error('Unexpected error while waiting for transaction block.');\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,iBAA4C;AAI5C,0BAA8B;AAC9B,uBAMO;AACP,mBAAmC;AACnC,4BAAiC;AAyGjC,MAAM,mBAAmB,OAAO,IAAI,mBAAmB;AAEhD,SAAS,YAAY,QAAsC;AACjE,SACC,OAAO,WAAW,YAAY,WAAW,QAAS,OAAe,gBAAgB,MAAM;AAEzF;AAEO,MAAM,UAAU;AAAA,EAGtB,KAAK,gBAAgB,IAAI;AACxB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,SAA2B;AACtC,SAAK,YAAY,QAAQ,aAAa,IAAI,uCAAiB,EAAE,KAAK,QAAQ,IAAI,CAAC;AAAA,EAChF;AAAA,EAEA,MAAM,mBAAgD;AACrD,UAAM,OAAO,MAAM,KAAK,UAAU,QAAuC;AAAA,MACxE,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,IACV,CAAC;AAED,WAAO,KAAK,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS,OAAgD;AAC9D,QAAI,CAAC,MAAM,SAAS,KAAC,wCAAkB,sCAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,OAAO,MAAM,UAAU,MAAM,QAAQ,MAAM,KAAK;AAAA,IAChE,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,OAAmD;AACpE,QAAI,CAAC,MAAM,SAAS,KAAC,wCAAkB,sCAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,OAAO,MAAM,QAAQ,MAAM,KAAK;AAAA,IAChD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,OAA+C;AAC/D,QAAI,CAAC,MAAM,SAAS,KAAC,wCAAkB,sCAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,OAAO,MAAM,QAAQ;AAAA,IACrC,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,OAAqD;AACzE,QAAI,CAAC,MAAM,SAAS,KAAC,wCAAkB,sCAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,uBAAuB,QAAQ,CAAC,MAAM,KAAK,EAAE,CAAC;AAAA,EAC7F;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,OAA4D;AACjF,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ;AAAA,IACxB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,OAAkD;AACtE,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ;AAAA,IACxB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KAAkB,QAAgB,QAA+B;AACtE,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,OAAO,CAAC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBACL,OACoC;AACpC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,IACrD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kCACL,OACoC;AACpC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,OAAO;AAAA,IACvB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBACL,OACmC;AACnC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,MAAM;AAAA,IACrC,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,0BACL,OACqC;AACrC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAAA,IACrD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBACL,OACmC;AACnC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM,MAAM;AAAA,IACnD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,OAAiE;AACtF,QAAI,CAAC,MAAM,SAAS,KAAC,wCAAkB,sCAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,MAAM;AAAA,QACN;AAAA,UACC,QAAQ,MAAM;AAAA,UACd,SAAS,MAAM;AAAA,QAChB;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU,OAAoD;AACnE,QAAI,CAAC,MAAM,MAAM,KAAC,yCAAmB,uCAAqB,MAAM,EAAE,CAAC,GAAG;AACrE,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI,MAAM,OAAO;AAAA,IACjC,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,OAAoD;AAC1E,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,IAChD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,OAA4D;AACjF,UAAM,IAAI,QAAQ,CAAC,OAAO;AACzB,UAAI,CAAC,MAAM,KAAC,yCAAmB,uCAAqB,EAAE,CAAC,GAAG;AACzD,cAAM,IAAI,MAAM,yBAAyB,EAAE,EAAE;AAAA,MAC9C;AAAA,IACD,CAAC;AACD,UAAM,gBAAgB,MAAM,IAAI,WAAW,IAAI,IAAI,MAAM,GAAG,EAAE;AAC9D,QAAI,eAAe;AAClB,YAAM,IAAI,MAAM,sCAAsC,MAAM,GAAG,EAAE;AAAA,IAClE;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,KAAK,MAAM,OAAO;AAAA,IAClC,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBACL,OACwC;AACxC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP;AAAA,UACC,QAAQ,MAAM;AAAA,UACd,SAAS,MAAM;AAAA,QAChB;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,SACL,MAAM,SAAS,kBAAkB;AAAA,MACnC;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,oBACL,OACuC;AACvC,QAAI,KAAC,2CAAyB,MAAM,MAAM,GAAG;AAC5C,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAC7C;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ,MAAM,OAAO;AAAA,IACrC,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,0BACL,OACyC;AACzC,UAAM,QAAQ,QAAQ,CAAC,MAAM;AAC5B,UAAI,KAAC,2CAAyB,CAAC,GAAG;AACjC,cAAM,IAAI,MAAM,8BAA8B,CAAC,EAAE;AAAA,MAClD;AAAA,IACD,CAAC;AAED,UAAM,gBAAgB,MAAM,QAAQ,WAAW,IAAI,IAAI,MAAM,OAAO,EAAE;AACtE,QAAI,eAAe;AAClB,YAAM,IAAI,MAAM,mCAAmC,MAAM,OAAO,EAAE;AAAA,IACnE;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,OAAO;AAAA,IACtC,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,wBAAwB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAwE;AACvE,UAAM,SAAsC,MAAM,KAAK,UAAU,QAAQ;AAAA,MACxE,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,OAAO,qBAAqB,WAAW,uBAAmB,qBAAS,gBAAgB;AAAA,QACnF,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAAA,QACjD;AAAA,MACD;AAAA,IACD,CAAC;AAED,QAAI,gBAAgB,yBAAyB;AAC5C,UAAI;AACH,cAAM,KAAK,mBAAmB;AAAA,UAC7B,QAAQ,OAAO;AAAA,QAChB,CAAC;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,0BAA0B;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GAMyC;AACxC,QAAI;AAEJ,QAAI,uBAAuB,YAAY;AACtC,yBAAmB;AAAA,IACpB,OAAO;AACN,kBAAY,kBAAkB,OAAO,aAAa,CAAC;AACnD,yBAAmB,MAAM,YAAY,MAAM,EAAE,QAAQ,KAAK,CAAC;AAAA,IAC5D;AAEA,UAAM,EAAE,WAAW,MAAM,IAAI,MAAM,OAAO,gBAAgB,gBAAgB;AAE1E,WAAO,KAAK,wBAAwB;AAAA,MACnC,kBAAkB;AAAA,MAClB;AAAA,MACA,GAAG;AAAA,IACJ,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,4BAA6C;AAClD,UAAM,OAAO,MAAM,KAAK,UAAU,QAAgB;AAAA,MACjD,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,IACV,CAAC;AACD,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBAAwC;AAC7C,UAAM,OAAO,MAAM,KAAK,UAAU,QAAgB;AAAA,MACjD,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,IACV,CAAC;AACD,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU,OAAmD;AAClE,QAAI,CAAC,MAAM,SAAS,KAAC,wCAAkB,sCAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,kBAAkB,QAAQ,CAAC,MAAM,KAAK,EAAE,CAAC;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,OAAwD;AAC5E,UAAM,aAAa,QAAQ,CAAC,OAAO;AAClC,UAAI,CAAC,MAAM,KAAC,yCAAmB,uCAAqB,EAAE,CAAC,GAAG;AACzD,cAAM,IAAI,MAAM,wBAAwB,EAAE,EAAE;AAAA,MAC7C;AAAA,IACD,CAAC;AACD,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,YAAY;AAAA,IAC5B,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,0BAA0D;AAC/D,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,gCAAgC,QAAQ,CAAC,EAAE,CAAC;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,OAAoD;AACrE,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,SACL,MAAM,SAAS,kBAAkB;AAAA,MACnC;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eACL,OAIuB;AACvB,WAAO,KAAK,UAAU,UAAU;AAAA,MAC/B,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ,CAAC,MAAM,MAAM;AAAA,MACrB,WAAW,MAAM;AAAA,IAClB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBACL,OAIuB;AACvB,WAAO,KAAK,UAAU,UAAU;AAAA,MAC/B,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ,CAAC,MAAM,MAAM;AAAA,MACrB,WAAW,MAAM;AAAA,IAClB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,2BACL,OAC6B;AAC7B,QAAI;AACJ,YAAI,mCAAc,MAAM,gBAAgB,GAAG;AAC1C,YAAM,iBAAiB,kBAAkB,MAAM,MAAM;AACrD,8BAAoB;AAAA,QACnB,MAAM,MAAM,iBAAiB,MAAM;AAAA,UAClC,QAAQ;AAAA,UACR,qBAAqB;AAAA,QACtB,CAAC;AAAA,MACF;AAAA,IACD,WAAW,OAAO,MAAM,qBAAqB,UAAU;AACtD,0BAAoB,MAAM;AAAA,IAC3B,WAAW,MAAM,4BAA4B,YAAY;AACxD,8BAAoB,qBAAS,MAAM,gBAAgB;AAAA,IACpD,OAAO;AACN,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACpD;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ,mBAAmB,MAAM,UAAU,SAAS,GAAG,MAAM,KAAK;AAAA,IAClF,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBACL,OAC0C;AAC1C,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,OAAO,MAAM,qBAAqB,WAC/B,MAAM,uBACN,qBAAS,MAAM,gBAAgB;AAAA,MACnC;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,OAA0D;AAChF,QAAI,CAAC,MAAM,YAAY,KAAC,yCAAmB,uCAAqB,MAAM,QAAQ,CAAC,GAAG;AACjF,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,UAAU,MAAM,QAAQ,MAAM,KAAK;AAAA,IACnD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB,OAAgE;AAC3F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,UAAU,MAAM,IAAI;AAAA,IACpC,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oCAAqD;AAC1D,UAAM,OAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACzC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,IACV,CAAC;AACD,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,OAAiD;AACpE,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,qBAAqB,QAAQ,CAAC,MAAM,EAAE,EAAE,CAAC;AAAA,EACxF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eACL,OAC0B;AAC1B,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ,OAAO,OAAO,MAAM,eAAe;AAAA,IAC3D,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,OAAwD;AAC9E,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,KAAK;AAAA,IACtB,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,oBAA6C;AAClD,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,0BAA0B,QAAQ,CAAC,EAAE,CAAC;AAAA,EACrF;AAAA,EAEA,MAAM,oBAA6C;AAClD,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,gCAAgC,QAAQ,CAAC,EAAE,CAAC;AAAA,EAC3F;AAAA,EAEA,MAAM,gBACL,OAC4B;AAC5B,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,QAAQ,OAAO,OAAO,OAAO,eAAe;AAAA,IAC7D,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,0BAA0B,OAEK;AACpC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,eAAe;AAAA,IAChC,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UACL,OAGqB;AACrB,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,QAAQ,OAAO,OAAO,OAAO,eAAe;AAAA,IAC7D,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAA+C;AACpD,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,2BAA2B,QAAQ,CAAC,EAAE,CAAC;AAAA,EACtF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kBAAsC;AAC3C,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,wBAAwB,QAAQ,CAAC,EAAE,CAAC;AAAA,EACnF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAA2C;AAChD,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,yBAAyB,QAAQ,CAAC,EAAE,CAAC;AAAA,EACpF;AAAA;AAAA,EAGA,MAAM,qBAAsC;AAC3C,UAAM,aAAa,MAAM,KAAK,cAAc,EAAE,IAAI,IAAI,CAAC;AACvD,UAAM,YAAQ,uBAAW,WAAW,MAAM;AAC1C,eAAO,kBAAM,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,EAC/B;AAAA,EAEA,MAAM,0BAA0B,OAAgE;AAC/F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI;AAAA,IACpB,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,wBAAwB;AAAA,IAC7B,SAAS;AAAA,IACT,GAAG;AAAA,EACJ,GAEsC;AACrC,UAAM,EAAE,YAAY,aAAa,KAAK,IACrC,MAAM,KAAK,UAAU,QAAQ;AAAA,MAC5B,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM,KAAK;AAAA,IAClD,CAAC;AAEF,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA,MAAM,KAAK,IAAI,CAAC,aAAS,iCAAmB,MAAM,MAAM,CAAC;AAAA,IAC1D;AAAA,EACD;AAAA,EAEA,MAAM,kBAAkB,OAA0D;AACjF,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,OAAO;AAAA,IACxB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,mBAAmB;AAAA,IACxB;AAAA,IACA,UAAU,KAAK;AAAA,IACf,eAAe,IAAI;AAAA,IACnB,GAAG;AAAA,EACJ,GAO2F;AAC1F,UAAM,gBAAgB,YAAY,QAAQ,OAAO;AACjD,UAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AACjD,oBAAc,iBAAiB,SAAS,MAAM,OAAO,cAAc,MAAM,CAAC;AAAA,IAC3E,CAAC;AAED,mBAAe,MAAM,MAAM;AAAA,IAE3B,CAAC;AAED,WAAO,CAAC,cAAc,SAAS;AAC9B,cAAQ,eAAe;AACvB,UAAI;AACH,eAAO,MAAM,KAAK,oBAAoB,KAAK;AAAA,MAC5C,SAAS,GAAG;AAEX,cAAM,QAAQ,KAAK;AAAA,UAClB,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,YAAY,CAAC;AAAA,UAC1D;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAEA,kBAAc,eAAe;AAG7B,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACxE;AACD;", "names": []}