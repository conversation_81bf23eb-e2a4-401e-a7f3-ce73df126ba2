import type { Signer } from '../cryptography/keypair.js';
import { PublicKey } from '../cryptography/publickey.js';
import type { SignatureScheme } from '../cryptography/signature-scheme.js';
import type { SuiGraphQLClient } from '../graphql/client.js';
import { MultiSigSigner } from './signer.js';
type CompressedSignature = {
    ED25519: number[];
} | {
    Secp256k1: number[];
} | {
    Secp256r1: number[];
} | {
    ZkLogin: number[];
};
type PublicKeyEnum = {
    ED25519: number[];
} | {
    Secp256k1: number[];
} | {
    Secp256r1: number[];
} | {
    ZkLogin: number[];
};
type PubkeyEnumWeightPair = {
    pubKey: PublicKeyEnum;
    weight: number;
};
type MultiSigPublicKeyStruct = {
    pk_map: PubkeyEnumWeightPair[];
    threshold: number;
};
export type MultiSigStruct = {
    sigs: CompressedSignature[];
    bitmap: number;
    multisig_pk: MultiSigPublicKeyStruct;
};
type ParsedPartialMultiSigSignature = {
    signatureScheme: SignatureScheme;
    signature: Uint8Array;
    publicKey: PublicKey;
    weight: number;
};
export declare const MAX_SIGNER_IN_MULTISIG = 10;
export declare const MIN_SIGNER_IN_MULTISIG = 1;
/**
 * A MultiSig public key
 */
export declare class MultiSigPublicKey extends PublicKey {
    private rawBytes;
    private multisigPublicKey;
    private publicKeys;
    /**
     * Create a new MultiSigPublicKey object
     */
    constructor(
    /**
     *  MultiSig public key as buffer or base-64 encoded string
     */
    value: string | Uint8Array | MultiSigPublicKeyStruct, options?: {
        client?: SuiGraphQLClient;
    });
    /**
     * 	A static method to create a new MultiSig publickey instance from a set of public keys and their associated weights pairs and threshold.
     */
    static fromPublicKeys({ threshold, publicKeys, }: {
        threshold: number;
        publicKeys: {
            publicKey: PublicKey;
            weight: number;
        }[];
    }): MultiSigPublicKey;
    /**
     * Checks if two MultiSig public keys are equal
     */
    equals(publicKey: MultiSigPublicKey): boolean;
    /**
     * Return the byte array representation of the MultiSig public key
     */
    toRawBytes(): Uint8Array;
    getPublicKeys(): {
        weight: number;
        publicKey: PublicKey;
    }[];
    getThreshold(): number;
    getSigner(...signers: [signer: Signer]): MultiSigSigner;
    /**
     * Return the Sui address associated with this MultiSig public key
     */
    toSuiAddress(): string;
    /**
     * Return the Sui address associated with this MultiSig public key
     */
    flag(): number;
    /**
     * Verifies that the signature is valid for for the provided message
     */
    verify(message: Uint8Array, multisigSignature: string): Promise<boolean>;
    /**
     * Combines multiple partial signatures into a single multisig, ensuring that each public key signs only once
     * and that all the public keys involved are known and valid, and then serializes multisig into the standard format
     */
    combinePartialSignatures(signatures: string[]): string;
}
/**
 * Parse multisig structure into an array of individual signatures: signature scheme, the actual individual signature, public key and its weight.
 */
export declare function parsePartialSignatures(multisig: MultiSigStruct, options?: {
    client?: SuiGraphQLClient;
}): ParsedPartialMultiSigSignature[];
export {};
