{"version": 3, "sources": ["../../../src/multisig/signer.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\nimport { toBase64 } from '@mysten/bcs';\n\nimport type { SignatureScheme } from '../cryptography/index.js';\nimport { Signer } from '../cryptography/index.js';\nimport type { MultiSigPublicKey } from './publickey.js';\n\nexport class MultiSigSigner extends Signer {\n\t#pubkey: MultiSigPublicKey;\n\t#signers: Signer[];\n\n\tconstructor(pubkey: MultiSigPublicKey, signers: Signer[] = []) {\n\t\tsuper();\n\t\tthis.#pubkey = pubkey;\n\t\tthis.#signers = signers;\n\n\t\tlet uniqueKeys = new Set();\n\t\tlet combinedWeight = 0;\n\n\t\tconst weights = pubkey.getPublicKeys().map(({ weight, publicKey }) => ({\n\t\t\tweight,\n\t\t\taddress: publicKey.toSuiAddress(),\n\t\t}));\n\n\t\tfor (let signer of signers) {\n\t\t\tconst address = signer.toSuiAddress();\n\t\t\tif (uniqueKeys.has(address)) {\n\t\t\t\tthrow new Error(`Can't create MultiSigSigner with duplicate signers`);\n\t\t\t}\n\t\t\tuniqueKeys.add(address);\n\n\t\t\tconst weight = weights.find((w) => w.address === address)?.weight;\n\n\t\t\tif (!weight) {\n\t\t\t\tthrow new Error(`Signer ${address} is not part of the MultiSig public key`);\n\t\t\t}\n\n\t\t\tcombinedWeight += weight;\n\t\t}\n\n\t\tif (combinedWeight < pubkey.getThreshold()) {\n\t\t\tthrow new Error(`Combined weight of signers is less than threshold`);\n\t\t}\n\t}\n\n\tgetKeyScheme(): SignatureScheme {\n\t\treturn 'MultiSig';\n\t}\n\n\tgetPublicKey(): MultiSigPublicKey {\n\t\treturn this.#pubkey;\n\t}\n\n\tsign(_data: Uint8Array): never {\n\t\tthrow new Error(\n\t\t\t'MultiSigSigner does not support signing directly. Use signTransaction or signPersonalMessage instead',\n\t\t);\n\t}\n\n\tsignData(_data: Uint8Array): never {\n\t\tthrow new Error(\n\t\t\t'MultiSigSigner does not support signing directly. Use signTransaction or signPersonalMessage instead',\n\t\t);\n\t}\n\n\tasync signTransaction(bytes: Uint8Array) {\n\t\tconst signature = this.#pubkey.combinePartialSignatures(\n\t\t\tawait Promise.all(\n\t\t\t\tthis.#signers.map(async (signer) => (await signer.signTransaction(bytes)).signature),\n\t\t\t),\n\t\t);\n\n\t\treturn {\n\t\t\tsignature,\n\t\t\tbytes: toBase64(bytes),\n\t\t};\n\t}\n\n\tasync signPersonalMessage(bytes: Uint8Array) {\n\t\tconst signature = this.#pubkey.combinePartialSignatures(\n\t\t\tawait Promise.all(\n\t\t\t\tthis.#signers.map(async (signer) => (await signer.signPersonalMessage(bytes)).signature),\n\t\t\t),\n\t\t);\n\n\t\treturn {\n\t\t\tsignature,\n\t\t\tbytes: toBase64(bytes),\n\t\t};\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,iBAAyB;AAGzB,0BAAuB;AALvB;AAQO,MAAM,uBAAuB,2BAAO;AAAA,EAI1C,YAAY,QAA2B,UAAoB,CAAC,GAAG;AAC9D,UAAM;AAJP;AACA;AAIC,uBAAK,SAAU;AACf,uBAAK,UAAW;AAEhB,QAAI,aAAa,oBAAI,IAAI;AACzB,QAAI,iBAAiB;AAErB,UAAM,UAAU,OAAO,cAAc,EAAE,IAAI,CAAC,EAAE,QAAQ,UAAU,OAAO;AAAA,MACtE;AAAA,MACA,SAAS,UAAU,aAAa;AAAA,IACjC,EAAE;AAEF,aAAS,UAAU,SAAS;AAC3B,YAAM,UAAU,OAAO,aAAa;AACpC,UAAI,WAAW,IAAI,OAAO,GAAG;AAC5B,cAAM,IAAI,MAAM,oDAAoD;AAAA,MACrE;AACA,iBAAW,IAAI,OAAO;AAEtB,YAAM,SAAS,QAAQ,KAAK,CAAC,MAAM,EAAE,YAAY,OAAO,GAAG;AAE3D,UAAI,CAAC,QAAQ;AACZ,cAAM,IAAI,MAAM,UAAU,OAAO,yCAAyC;AAAA,MAC3E;AAEA,wBAAkB;AAAA,IACnB;AAEA,QAAI,iBAAiB,OAAO,aAAa,GAAG;AAC3C,YAAM,IAAI,MAAM,mDAAmD;AAAA,IACpE;AAAA,EACD;AAAA,EAEA,eAAgC;AAC/B,WAAO;AAAA,EACR;AAAA,EAEA,eAAkC;AACjC,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,KAAK,OAA0B;AAC9B,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAAA,EAEA,SAAS,OAA0B;AAClC,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,gBAAgB,OAAmB;AACxC,UAAM,YAAY,mBAAK,SAAQ;AAAA,MAC9B,MAAM,QAAQ;AAAA,QACb,mBAAK,UAAS,IAAI,OAAO,YAAY,MAAM,OAAO,gBAAgB,KAAK,GAAG,SAAS;AAAA,MACpF;AAAA,IACD;AAEA,WAAO;AAAA,MACN;AAAA,MACA,WAAO,qBAAS,KAAK;AAAA,IACtB;AAAA,EACD;AAAA,EAEA,MAAM,oBAAoB,OAAmB;AAC5C,UAAM,YAAY,mBAAK,SAAQ;AAAA,MAC9B,MAAM,QAAQ;AAAA,QACb,mBAAK,UAAS,IAAI,OAAO,YAAY,MAAM,OAAO,oBAAoB,KAAK,GAAG,SAAS;AAAA,MACxF;AAAA,IACD;AAEA,WAAO;AAAA,MACN;AAAA,MACA,WAAO,qBAAS,KAAK;AAAA,IACtB;AAAA,EACD;AACD;AAlFC;AACA;", "names": []}