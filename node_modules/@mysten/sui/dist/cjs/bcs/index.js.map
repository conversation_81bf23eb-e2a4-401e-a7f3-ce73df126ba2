{"version": 3, "sources": ["../../../src/bcs/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '@mysten/bcs';\n\nimport {\n\tAddress,\n\tAppId,\n\tArgument,\n\tCallArg,\n\tCommand,\n\tCompressedSignature,\n\tGasData,\n\tIntent,\n\tIntentMessage,\n\tIntentScope,\n\tIntentVersion,\n\tMultiSig,\n\tMultiSigPkMap,\n\tMultiSigPublicKey,\n\tObjectArg,\n\tObjectDigest,\n\tPasskeyAuthenticator,\n\tProgrammableMoveCall,\n\tProgrammableTransaction,\n\tPublicKey,\n\tSenderSignedData,\n\tSenderSignedTransaction,\n\tSharedObjectRef,\n\tStructTag,\n\tSuiObjectRef,\n\tTransactionData,\n\tTransactionDataV1,\n\tTransactionExpiration,\n\tTransactionKind,\n\tTypeTag,\n} from './bcs.js';\nimport { TransactionEffects } from './effects.js';\n\nexport type { TypeTag } from './types.js';\n\nexport { TypeTagSerializer } from './type-tag-serializer.js';\nexport { BcsType, type BcsTypeOptions } from '@mysten/bcs';\n\nconst suiBcs = {\n\t...bcs,\n\tU8: bcs.u8(),\n\tU16: bcs.u16(),\n\tU32: bcs.u32(),\n\tU64: bcs.u64(),\n\tU128: bcs.u128(),\n\tU256: bcs.u256(),\n\tULEB128: bcs.uleb128(),\n\tBool: bcs.bool(),\n\tString: bcs.string(),\n\tAddress,\n\tAppId,\n\tArgument,\n\tCallArg,\n\tCompressedSignature,\n\tGasData,\n\tIntent,\n\tIntentMessage,\n\tIntentScope,\n\tIntentVersion,\n\tMultiSig,\n\tMultiSigPkMap,\n\tMultiSigPublicKey,\n\tObjectArg,\n\tObjectDigest,\n\tProgrammableMoveCall,\n\tProgrammableTransaction,\n\tPublicKey,\n\tSenderSignedData,\n\tSenderSignedTransaction,\n\tSharedObjectRef,\n\tStructTag,\n\tSuiObjectRef,\n\tCommand,\n\tTransactionData,\n\tTransactionDataV1,\n\tTransactionExpiration,\n\tTransactionKind,\n\tTypeTag,\n\tTransactionEffects,\n\tPasskeyAuthenticator,\n};\n\nexport { suiBcs as bcs };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAoB;AAEpB,IAAAA,cA+BO;AACP,qBAAmC;AAInC,iCAAkC;AAClC,IAAAA,cAA6C;AAE7C,MAAM,SAAS;AAAA,EACd,GAAG;AAAA,EACH,IAAI,eAAI,GAAG;AAAA,EACX,KAAK,eAAI,IAAI;AAAA,EACb,KAAK,eAAI,IAAI;AAAA,EACb,KAAK,eAAI,IAAI;AAAA,EACb,MAAM,eAAI,KAAK;AAAA,EACf,MAAM,eAAI,KAAK;AAAA,EACf,SAAS,eAAI,QAAQ;AAAA,EACrB,MAAM,eAAI,KAAK;AAAA,EACf,QAAQ,eAAI,OAAO;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": ["import_bcs"]}