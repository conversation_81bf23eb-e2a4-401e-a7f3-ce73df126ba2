{"version": 3, "sources": ["../../../src/bcs/type-tag-serializer.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { splitGenericParameters } from '@mysten/bcs';\n\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { TypeTag } from './types.js';\n\nconst VECTOR_REGEX = /^vector<(.+)>$/;\nconst STRUCT_REGEX = /^([^:]+)::([^:]+)::([^<]+)(<(.+)>)?/;\n\nexport class TypeTagSerializer {\n\tstatic parseFromStr(str: string, normalizeAddress = false): TypeTag {\n\t\tif (str === 'address') {\n\t\t\treturn { address: null };\n\t\t} else if (str === 'bool') {\n\t\t\treturn { bool: null };\n\t\t} else if (str === 'u8') {\n\t\t\treturn { u8: null };\n\t\t} else if (str === 'u16') {\n\t\t\treturn { u16: null };\n\t\t} else if (str === 'u32') {\n\t\t\treturn { u32: null };\n\t\t} else if (str === 'u64') {\n\t\t\treturn { u64: null };\n\t\t} else if (str === 'u128') {\n\t\t\treturn { u128: null };\n\t\t} else if (str === 'u256') {\n\t\t\treturn { u256: null };\n\t\t} else if (str === 'signer') {\n\t\t\treturn { signer: null };\n\t\t}\n\n\t\tconst vectorMatch = str.match(VECTOR_REGEX);\n\t\tif (vectorMatch) {\n\t\t\treturn {\n\t\t\t\tvector: TypeTagSerializer.parseFromStr(vectorMatch[1], normalizeAddress),\n\t\t\t};\n\t\t}\n\n\t\tconst structMatch = str.match(STRUCT_REGEX);\n\t\tif (structMatch) {\n\t\t\tconst address = normalizeAddress ? normalizeSuiAddress(structMatch[1]) : structMatch[1];\n\t\t\treturn {\n\t\t\t\tstruct: {\n\t\t\t\t\taddress,\n\t\t\t\t\tmodule: structMatch[2],\n\t\t\t\t\tname: structMatch[3],\n\t\t\t\t\ttypeParams:\n\t\t\t\t\t\tstructMatch[5] === undefined\n\t\t\t\t\t\t\t? []\n\t\t\t\t\t\t\t: TypeTagSerializer.parseStructTypeArgs(structMatch[5], normalizeAddress),\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tthrow new Error(`Encountered unexpected token when parsing type args for ${str}`);\n\t}\n\n\tstatic parseStructTypeArgs(str: string, normalizeAddress = false): TypeTag[] {\n\t\treturn splitGenericParameters(str).map((tok) =>\n\t\t\tTypeTagSerializer.parseFromStr(tok, normalizeAddress),\n\t\t);\n\t}\n\n\tstatic tagToString(tag: TypeTag): string {\n\t\tif ('bool' in tag) {\n\t\t\treturn 'bool';\n\t\t}\n\t\tif ('u8' in tag) {\n\t\t\treturn 'u8';\n\t\t}\n\t\tif ('u16' in tag) {\n\t\t\treturn 'u16';\n\t\t}\n\t\tif ('u32' in tag) {\n\t\t\treturn 'u32';\n\t\t}\n\t\tif ('u64' in tag) {\n\t\t\treturn 'u64';\n\t\t}\n\t\tif ('u128' in tag) {\n\t\t\treturn 'u128';\n\t\t}\n\t\tif ('u256' in tag) {\n\t\t\treturn 'u256';\n\t\t}\n\t\tif ('address' in tag) {\n\t\t\treturn 'address';\n\t\t}\n\t\tif ('signer' in tag) {\n\t\t\treturn 'signer';\n\t\t}\n\t\tif ('vector' in tag) {\n\t\t\treturn `vector<${TypeTagSerializer.tagToString(tag.vector)}>`;\n\t\t}\n\t\tif ('struct' in tag) {\n\t\t\tconst struct = tag.struct;\n\t\t\tconst typeParams = struct.typeParams.map(TypeTagSerializer.tagToString).join(', ');\n\t\t\treturn `${struct.address}::${struct.module}::${struct.name}${\n\t\t\t\ttypeParams ? `<${typeParams}>` : ''\n\t\t\t}`;\n\t\t}\n\t\tthrow new Error('Invalid TypeTag');\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAuC;AAEvC,uBAAoC;AAGpC,MAAM,eAAe;AACrB,MAAM,eAAe;AAEd,MAAM,kBAAkB;AAAA,EAC9B,OAAO,aAAa,KAAa,mBAAmB,OAAgB;AACnE,QAAI,QAAQ,WAAW;AACtB,aAAO,EAAE,SAAS,KAAK;AAAA,IACxB,WAAW,QAAQ,QAAQ;AAC1B,aAAO,EAAE,MAAM,KAAK;AAAA,IACrB,WAAW,QAAQ,MAAM;AACxB,aAAO,EAAE,IAAI,KAAK;AAAA,IACnB,WAAW,QAAQ,OAAO;AACzB,aAAO,EAAE,KAAK,KAAK;AAAA,IACpB,WAAW,QAAQ,OAAO;AACzB,aAAO,EAAE,KAAK,KAAK;AAAA,IACpB,WAAW,QAAQ,OAAO;AACzB,aAAO,EAAE,KAAK,KAAK;AAAA,IACpB,WAAW,QAAQ,QAAQ;AAC1B,aAAO,EAAE,MAAM,KAAK;AAAA,IACrB,WAAW,QAAQ,QAAQ;AAC1B,aAAO,EAAE,MAAM,KAAK;AAAA,IACrB,WAAW,QAAQ,UAAU;AAC5B,aAAO,EAAE,QAAQ,KAAK;AAAA,IACvB;AAEA,UAAM,cAAc,IAAI,MAAM,YAAY;AAC1C,QAAI,aAAa;AAChB,aAAO;AAAA,QACN,QAAQ,kBAAkB,aAAa,YAAY,CAAC,GAAG,gBAAgB;AAAA,MACxE;AAAA,IACD;AAEA,UAAM,cAAc,IAAI,MAAM,YAAY;AAC1C,QAAI,aAAa;AAChB,YAAM,UAAU,uBAAmB,sCAAoB,YAAY,CAAC,CAAC,IAAI,YAAY,CAAC;AACtF,aAAO;AAAA,QACN,QAAQ;AAAA,UACP;AAAA,UACA,QAAQ,YAAY,CAAC;AAAA,UACrB,MAAM,YAAY,CAAC;AAAA,UACnB,YACC,YAAY,CAAC,MAAM,SAChB,CAAC,IACD,kBAAkB,oBAAoB,YAAY,CAAC,GAAG,gBAAgB;AAAA,QAC3E;AAAA,MACD;AAAA,IACD;AAEA,UAAM,IAAI,MAAM,2DAA2D,GAAG,EAAE;AAAA,EACjF;AAAA,EAEA,OAAO,oBAAoB,KAAa,mBAAmB,OAAkB;AAC5E,eAAO,mCAAuB,GAAG,EAAE;AAAA,MAAI,CAAC,QACvC,kBAAkB,aAAa,KAAK,gBAAgB;AAAA,IACrD;AAAA,EACD;AAAA,EAEA,OAAO,YAAY,KAAsB;AACxC,QAAI,UAAU,KAAK;AAClB,aAAO;AAAA,IACR;AACA,QAAI,QAAQ,KAAK;AAChB,aAAO;AAAA,IACR;AACA,QAAI,SAAS,KAAK;AACjB,aAAO;AAAA,IACR;AACA,QAAI,SAAS,KAAK;AACjB,aAAO;AAAA,IACR;AACA,QAAI,SAAS,KAAK;AACjB,aAAO;AAAA,IACR;AACA,QAAI,UAAU,KAAK;AAClB,aAAO;AAAA,IACR;AACA,QAAI,UAAU,KAAK;AAClB,aAAO;AAAA,IACR;AACA,QAAI,aAAa,KAAK;AACrB,aAAO;AAAA,IACR;AACA,QAAI,YAAY,KAAK;AACpB,aAAO;AAAA,IACR;AACA,QAAI,YAAY,KAAK;AACpB,aAAO,UAAU,kBAAkB,YAAY,IAAI,MAAM,CAAC;AAAA,IAC3D;AACA,QAAI,YAAY,KAAK;AACpB,YAAM,SAAS,IAAI;AACnB,YAAM,aAAa,OAAO,WAAW,IAAI,kBAAkB,WAAW,EAAE,KAAK,IAAI;AACjF,aAAO,GAAG,OAAO,OAAO,KAAK,OAAO,MAAM,KAAK,OAAO,IAAI,GACzD,aAAa,IAAI,UAAU,MAAM,EAClC;AAAA,IACD;AACA,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AACD;", "names": []}