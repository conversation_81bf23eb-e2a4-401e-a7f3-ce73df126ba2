"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var bcs_exports = {};
__export(bcs_exports, {
  Address: () => Address,
  AppId: () => AppId,
  Argument: () => Argument,
  CallArg: () => CallArg,
  Command: () => Command,
  CompressedSignature: () => CompressedSignature,
  GasData: () => GasData,
  Intent: () => Intent,
  IntentMessage: () => IntentMessage,
  IntentScope: () => IntentScope,
  IntentVersion: () => IntentVersion,
  MultiSig: () => MultiSig,
  MultiSigPkMap: () => MultiSigPkMap,
  MultiSigPublicKey: () => MultiSigPublicKey,
  ObjectArg: () => ObjectArg,
  ObjectDigest: () => ObjectDigest,
  PasskeyAuthenticator: () => PasskeyAuthenticator,
  ProgrammableMoveCall: () => ProgrammableMoveCall,
  ProgrammableTransaction: () => ProgrammableTransaction,
  PublicKey: () => PublicKey,
  SenderSignedData: () => SenderSignedData,
  SenderSignedTransaction: () => SenderSignedTransaction,
  SharedObjectRef: () => SharedObjectRef,
  StructTag: () => StructTag,
  SuiObjectRef: () => SuiObjectRef,
  TransactionData: () => TransactionData,
  TransactionDataV1: () => TransactionDataV1,
  TransactionExpiration: () => TransactionExpiration,
  TransactionKind: () => TransactionKind,
  TypeTag: () => TypeTag,
  base64String: () => base64String
});
module.exports = __toCommonJS(bcs_exports);
var import_bcs = require("@mysten/bcs");
var import_sui_types = require("../utils/sui-types.js");
var import_type_tag_serializer = require("./type-tag-serializer.js");
function unsafe_u64(options) {
  return import_bcs.bcs.u64({
    name: "unsafe_u64",
    ...options
  }).transform({
    input: (val) => val,
    output: (val) => Number(val)
  });
}
function optionEnum(type) {
  return import_bcs.bcs.enum("Option", {
    None: null,
    Some: type
  });
}
const Address = import_bcs.bcs.bytes(import_sui_types.SUI_ADDRESS_LENGTH).transform({
  validate: (val) => {
    const address = typeof val === "string" ? val : (0, import_bcs.toHex)(val);
    if (!address || !(0, import_sui_types.isValidSuiAddress)((0, import_sui_types.normalizeSuiAddress)(address))) {
      throw new Error(`Invalid Sui address ${address}`);
    }
  },
  input: (val) => typeof val === "string" ? (0, import_bcs.fromHex)((0, import_sui_types.normalizeSuiAddress)(val)) : val,
  output: (val) => (0, import_sui_types.normalizeSuiAddress)((0, import_bcs.toHex)(val))
});
const ObjectDigest = import_bcs.bcs.vector(import_bcs.bcs.u8()).transform({
  name: "ObjectDigest",
  input: (value) => (0, import_bcs.fromBase58)(value),
  output: (value) => (0, import_bcs.toBase58)(new Uint8Array(value)),
  validate: (value) => {
    if ((0, import_bcs.fromBase58)(value).length !== 32) {
      throw new Error("ObjectDigest must be 32 bytes");
    }
  }
});
const SuiObjectRef = import_bcs.bcs.struct("SuiObjectRef", {
  objectId: Address,
  version: import_bcs.bcs.u64(),
  digest: ObjectDigest
});
const SharedObjectRef = import_bcs.bcs.struct("SharedObjectRef", {
  objectId: Address,
  initialSharedVersion: import_bcs.bcs.u64(),
  mutable: import_bcs.bcs.bool()
});
const ObjectArg = import_bcs.bcs.enum("ObjectArg", {
  ImmOrOwnedObject: SuiObjectRef,
  SharedObject: SharedObjectRef,
  Receiving: SuiObjectRef
});
const CallArg = import_bcs.bcs.enum("CallArg", {
  Pure: import_bcs.bcs.struct("Pure", {
    bytes: import_bcs.bcs.vector(import_bcs.bcs.u8()).transform({
      input: (val) => typeof val === "string" ? (0, import_bcs.fromBase64)(val) : val,
      output: (val) => (0, import_bcs.toBase64)(new Uint8Array(val))
    })
  }),
  Object: ObjectArg
});
const InnerTypeTag = import_bcs.bcs.enum("TypeTag", {
  bool: null,
  u8: null,
  u64: null,
  u128: null,
  address: null,
  signer: null,
  vector: import_bcs.bcs.lazy(() => InnerTypeTag),
  struct: import_bcs.bcs.lazy(() => StructTag),
  u16: null,
  u32: null,
  u256: null
});
const TypeTag = InnerTypeTag.transform({
  input: (typeTag) => typeof typeTag === "string" ? import_type_tag_serializer.TypeTagSerializer.parseFromStr(typeTag, true) : typeTag,
  output: (typeTag) => import_type_tag_serializer.TypeTagSerializer.tagToString(typeTag)
});
const Argument = import_bcs.bcs.enum("Argument", {
  GasCoin: null,
  Input: import_bcs.bcs.u16(),
  Result: import_bcs.bcs.u16(),
  NestedResult: import_bcs.bcs.tuple([import_bcs.bcs.u16(), import_bcs.bcs.u16()])
});
const ProgrammableMoveCall = import_bcs.bcs.struct("ProgrammableMoveCall", {
  package: Address,
  module: import_bcs.bcs.string(),
  function: import_bcs.bcs.string(),
  typeArguments: import_bcs.bcs.vector(TypeTag),
  arguments: import_bcs.bcs.vector(Argument)
});
const Command = import_bcs.bcs.enum("Command", {
  /**
   * A Move Call - any public Move function can be called via
   * this transaction. The results can be used that instant to pass
   * into the next transaction.
   */
  MoveCall: ProgrammableMoveCall,
  /**
   * Transfer vector of objects to a receiver.
   */
  TransferObjects: import_bcs.bcs.struct("TransferObjects", {
    objects: import_bcs.bcs.vector(Argument),
    address: Argument
  }),
  // /**
  //  * Split `amount` from a `coin`.
  //  */
  SplitCoins: import_bcs.bcs.struct("SplitCoins", {
    coin: Argument,
    amounts: import_bcs.bcs.vector(Argument)
  }),
  // /**
  //  * Merge Vector of Coins (`sources`) into a `destination`.
  //  */
  MergeCoins: import_bcs.bcs.struct("MergeCoins", {
    destination: Argument,
    sources: import_bcs.bcs.vector(Argument)
  }),
  // /**
  //  * Publish a Move module.
  //  */
  Publish: import_bcs.bcs.struct("Publish", {
    modules: import_bcs.bcs.vector(
      import_bcs.bcs.vector(import_bcs.bcs.u8()).transform({
        input: (val) => typeof val === "string" ? (0, import_bcs.fromBase64)(val) : val,
        output: (val) => (0, import_bcs.toBase64)(new Uint8Array(val))
      })
    ),
    dependencies: import_bcs.bcs.vector(Address)
  }),
  // /**
  //  * Build a vector of objects using the input arguments.
  //  * It is impossible to export construct a `vector<T: key>` otherwise,
  //  * so this call serves a utility function.
  //  */
  MakeMoveVec: import_bcs.bcs.struct("MakeMoveVec", {
    type: optionEnum(TypeTag).transform({
      input: (val) => val === null ? {
        None: true
      } : {
        Some: val
      },
      output: (val) => val.Some ?? null
    }),
    elements: import_bcs.bcs.vector(Argument)
  }),
  Upgrade: import_bcs.bcs.struct("Upgrade", {
    modules: import_bcs.bcs.vector(
      import_bcs.bcs.vector(import_bcs.bcs.u8()).transform({
        input: (val) => typeof val === "string" ? (0, import_bcs.fromBase64)(val) : val,
        output: (val) => (0, import_bcs.toBase64)(new Uint8Array(val))
      })
    ),
    dependencies: import_bcs.bcs.vector(Address),
    package: Address,
    ticket: Argument
  })
});
const ProgrammableTransaction = import_bcs.bcs.struct("ProgrammableTransaction", {
  inputs: import_bcs.bcs.vector(CallArg),
  commands: import_bcs.bcs.vector(Command)
});
const TransactionKind = import_bcs.bcs.enum("TransactionKind", {
  ProgrammableTransaction,
  ChangeEpoch: null,
  Genesis: null,
  ConsensusCommitPrologue: null
});
const TransactionExpiration = import_bcs.bcs.enum("TransactionExpiration", {
  None: null,
  Epoch: unsafe_u64()
});
const StructTag = import_bcs.bcs.struct("StructTag", {
  address: Address,
  module: import_bcs.bcs.string(),
  name: import_bcs.bcs.string(),
  typeParams: import_bcs.bcs.vector(InnerTypeTag)
});
const GasData = import_bcs.bcs.struct("GasData", {
  payment: import_bcs.bcs.vector(SuiObjectRef),
  owner: Address,
  price: import_bcs.bcs.u64(),
  budget: import_bcs.bcs.u64()
});
const TransactionDataV1 = import_bcs.bcs.struct("TransactionDataV1", {
  kind: TransactionKind,
  sender: Address,
  gasData: GasData,
  expiration: TransactionExpiration
});
const TransactionData = import_bcs.bcs.enum("TransactionData", {
  V1: TransactionDataV1
});
const IntentScope = import_bcs.bcs.enum("IntentScope", {
  TransactionData: null,
  TransactionEffects: null,
  CheckpointSummary: null,
  PersonalMessage: null
});
const IntentVersion = import_bcs.bcs.enum("IntentVersion", {
  V0: null
});
const AppId = import_bcs.bcs.enum("AppId", {
  Sui: null
});
const Intent = import_bcs.bcs.struct("Intent", {
  scope: IntentScope,
  version: IntentVersion,
  appId: AppId
});
function IntentMessage(T) {
  return import_bcs.bcs.struct(`IntentMessage<${T.name}>`, {
    intent: Intent,
    value: T
  });
}
const CompressedSignature = import_bcs.bcs.enum("CompressedSignature", {
  ED25519: import_bcs.bcs.fixedArray(64, import_bcs.bcs.u8()),
  Secp256k1: import_bcs.bcs.fixedArray(64, import_bcs.bcs.u8()),
  Secp256r1: import_bcs.bcs.fixedArray(64, import_bcs.bcs.u8()),
  ZkLogin: import_bcs.bcs.vector(import_bcs.bcs.u8())
});
const PublicKey = import_bcs.bcs.enum("PublicKey", {
  ED25519: import_bcs.bcs.fixedArray(32, import_bcs.bcs.u8()),
  Secp256k1: import_bcs.bcs.fixedArray(33, import_bcs.bcs.u8()),
  Secp256r1: import_bcs.bcs.fixedArray(33, import_bcs.bcs.u8()),
  ZkLogin: import_bcs.bcs.vector(import_bcs.bcs.u8())
});
const MultiSigPkMap = import_bcs.bcs.struct("MultiSigPkMap", {
  pubKey: PublicKey,
  weight: import_bcs.bcs.u8()
});
const MultiSigPublicKey = import_bcs.bcs.struct("MultiSigPublicKey", {
  pk_map: import_bcs.bcs.vector(MultiSigPkMap),
  threshold: import_bcs.bcs.u16()
});
const MultiSig = import_bcs.bcs.struct("MultiSig", {
  sigs: import_bcs.bcs.vector(CompressedSignature),
  bitmap: import_bcs.bcs.u16(),
  multisig_pk: MultiSigPublicKey
});
const base64String = import_bcs.bcs.vector(import_bcs.bcs.u8()).transform({
  input: (val) => typeof val === "string" ? (0, import_bcs.fromBase64)(val) : val,
  output: (val) => (0, import_bcs.toBase64)(new Uint8Array(val))
});
const SenderSignedTransaction = import_bcs.bcs.struct("SenderSignedTransaction", {
  intentMessage: IntentMessage(TransactionData),
  txSignatures: import_bcs.bcs.vector(base64String)
});
const SenderSignedData = import_bcs.bcs.vector(SenderSignedTransaction, {
  name: "SenderSignedData"
});
const PasskeyAuthenticator = import_bcs.bcs.struct("PasskeyAuthenticator", {
  authenticatorData: import_bcs.bcs.vector(import_bcs.bcs.u8()),
  clientDataJson: import_bcs.bcs.string(),
  userSignature: import_bcs.bcs.vector(import_bcs.bcs.u8())
});
//# sourceMappingURL=bcs.js.map
