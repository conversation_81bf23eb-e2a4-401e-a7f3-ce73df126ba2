"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var effects_exports = {};
__export(effects_exports, {
  TransactionEffects: () => TransactionEffects
});
module.exports = __toCommonJS(effects_exports);
var import_bcs = require("@mysten/bcs");
var import_bcs2 = require("./bcs.js");
const PackageUpgradeError = import_bcs.bcs.enum("PackageUpgradeError", {
  UnableToFetchPackage: import_bcs.bcs.struct("UnableToFetchPackage", { packageId: import_bcs2.Address }),
  NotAPackage: import_bcs.bcs.struct("NotAPackage", { objectId: import_bcs2.Address }),
  IncompatibleUpgrade: null,
  DigestDoesNotMatch: import_bcs.bcs.struct("DigestDoesNotMatch", { digest: import_bcs.bcs.vector(import_bcs.bcs.u8()) }),
  UnknownUpgradePolicy: import_bcs.bcs.struct("UnknownUpgradePolicy", { policy: import_bcs.bcs.u8() }),
  PackageIDDoesNotMatch: import_bcs.bcs.struct("PackageIDDoesNotMatch", {
    packageId: import_bcs2.Address,
    ticketId: import_bcs2.Address
  })
});
const ModuleId = import_bcs.bcs.struct("ModuleId", {
  address: import_bcs2.Address,
  name: import_bcs.bcs.string()
});
const MoveLocation = import_bcs.bcs.struct("MoveLocation", {
  module: ModuleId,
  function: import_bcs.bcs.u16(),
  instruction: import_bcs.bcs.u16(),
  functionName: import_bcs.bcs.option(import_bcs.bcs.string())
});
const CommandArgumentError = import_bcs.bcs.enum("CommandArgumentError", {
  TypeMismatch: null,
  InvalidBCSBytes: null,
  InvalidUsageOfPureArg: null,
  InvalidArgumentToPrivateEntryFunction: null,
  IndexOutOfBounds: import_bcs.bcs.struct("IndexOutOfBounds", { idx: import_bcs.bcs.u16() }),
  SecondaryIndexOutOfBounds: import_bcs.bcs.struct("SecondaryIndexOutOfBounds", {
    resultIdx: import_bcs.bcs.u16(),
    secondaryIdx: import_bcs.bcs.u16()
  }),
  InvalidResultArity: import_bcs.bcs.struct("InvalidResultArity", { resultIdx: import_bcs.bcs.u16() }),
  InvalidGasCoinUsage: null,
  InvalidValueUsage: null,
  InvalidObjectByValue: null,
  InvalidObjectByMutRef: null,
  SharedObjectOperationNotAllowed: null
});
const TypeArgumentError = import_bcs.bcs.enum("TypeArgumentError", {
  TypeNotFound: null,
  ConstraintNotSatisfied: null
});
const ExecutionFailureStatus = import_bcs.bcs.enum("ExecutionFailureStatus", {
  InsufficientGas: null,
  InvalidGasObject: null,
  InvariantViolation: null,
  FeatureNotYetSupported: null,
  MoveObjectTooBig: import_bcs.bcs.struct("MoveObjectTooBig", {
    objectSize: import_bcs.bcs.u64(),
    maxObjectSize: import_bcs.bcs.u64()
  }),
  MovePackageTooBig: import_bcs.bcs.struct("MovePackageTooBig", {
    objectSize: import_bcs.bcs.u64(),
    maxObjectSize: import_bcs.bcs.u64()
  }),
  CircularObjectOwnership: import_bcs.bcs.struct("CircularObjectOwnership", { object: import_bcs2.Address }),
  InsufficientCoinBalance: null,
  CoinBalanceOverflow: null,
  PublishErrorNonZeroAddress: null,
  SuiMoveVerificationError: null,
  MovePrimitiveRuntimeError: import_bcs.bcs.option(MoveLocation),
  MoveAbort: import_bcs.bcs.tuple([MoveLocation, import_bcs.bcs.u64()]),
  VMVerificationOrDeserializationError: null,
  VMInvariantViolation: null,
  FunctionNotFound: null,
  ArityMismatch: null,
  TypeArityMismatch: null,
  NonEntryFunctionInvoked: null,
  CommandArgumentError: import_bcs.bcs.struct("CommandArgumentError", {
    argIdx: import_bcs.bcs.u16(),
    kind: CommandArgumentError
  }),
  TypeArgumentError: import_bcs.bcs.struct("TypeArgumentError", {
    argumentIdx: import_bcs.bcs.u16(),
    kind: TypeArgumentError
  }),
  UnusedValueWithoutDrop: import_bcs.bcs.struct("UnusedValueWithoutDrop", {
    resultIdx: import_bcs.bcs.u16(),
    secondaryIdx: import_bcs.bcs.u16()
  }),
  InvalidPublicFunctionReturnType: import_bcs.bcs.struct("InvalidPublicFunctionReturnType", {
    idx: import_bcs.bcs.u16()
  }),
  InvalidTransferObject: null,
  EffectsTooLarge: import_bcs.bcs.struct("EffectsTooLarge", { currentSize: import_bcs.bcs.u64(), maxSize: import_bcs.bcs.u64() }),
  PublishUpgradeMissingDependency: null,
  PublishUpgradeDependencyDowngrade: null,
  PackageUpgradeError: import_bcs.bcs.struct("PackageUpgradeError", { upgradeError: PackageUpgradeError }),
  WrittenObjectsTooLarge: import_bcs.bcs.struct("WrittenObjectsTooLarge", {
    currentSize: import_bcs.bcs.u64(),
    maxSize: import_bcs.bcs.u64()
  }),
  CertificateDenied: null,
  SuiMoveVerificationTimedout: null,
  SharedObjectOperationNotAllowed: null,
  InputObjectDeleted: null,
  ExecutionCancelledDueToSharedObjectCongestion: import_bcs.bcs.struct(
    "ExecutionCancelledDueToSharedObjectCongestion",
    {
      congestedObjects: import_bcs.bcs.vector(import_bcs2.Address)
    }
  ),
  AddressDeniedForCoin: import_bcs.bcs.struct("AddressDeniedForCoin", {
    address: import_bcs2.Address,
    coinType: import_bcs.bcs.string()
  }),
  CoinTypeGlobalPause: import_bcs.bcs.struct("CoinTypeGlobalPause", { coinType: import_bcs.bcs.string() }),
  ExecutionCancelledDueToRandomnessUnavailable: null
});
const ExecutionStatus = import_bcs.bcs.enum("ExecutionStatus", {
  Success: null,
  Failed: import_bcs.bcs.struct("ExecutionFailed", {
    error: ExecutionFailureStatus,
    command: import_bcs.bcs.option(import_bcs.bcs.u64())
  })
});
const GasCostSummary = import_bcs.bcs.struct("GasCostSummary", {
  computationCost: import_bcs.bcs.u64(),
  storageCost: import_bcs.bcs.u64(),
  storageRebate: import_bcs.bcs.u64(),
  nonRefundableStorageFee: import_bcs.bcs.u64()
});
const Owner = import_bcs.bcs.enum("Owner", {
  AddressOwner: import_bcs2.Address,
  ObjectOwner: import_bcs2.Address,
  Shared: import_bcs.bcs.struct("Shared", {
    initialSharedVersion: import_bcs.bcs.u64()
  }),
  Immutable: null
});
const TransactionEffectsV1 = import_bcs.bcs.struct("TransactionEffectsV1", {
  status: ExecutionStatus,
  executedEpoch: import_bcs.bcs.u64(),
  gasUsed: GasCostSummary,
  modifiedAtVersions: import_bcs.bcs.vector(import_bcs.bcs.tuple([import_bcs2.Address, import_bcs.bcs.u64()])),
  sharedObjects: import_bcs.bcs.vector(import_bcs2.SuiObjectRef),
  transactionDigest: import_bcs2.ObjectDigest,
  created: import_bcs.bcs.vector(import_bcs.bcs.tuple([import_bcs2.SuiObjectRef, Owner])),
  mutated: import_bcs.bcs.vector(import_bcs.bcs.tuple([import_bcs2.SuiObjectRef, Owner])),
  unwrapped: import_bcs.bcs.vector(import_bcs.bcs.tuple([import_bcs2.SuiObjectRef, Owner])),
  deleted: import_bcs.bcs.vector(import_bcs2.SuiObjectRef),
  unwrappedThenDeleted: import_bcs.bcs.vector(import_bcs2.SuiObjectRef),
  wrapped: import_bcs.bcs.vector(import_bcs2.SuiObjectRef),
  gasObject: import_bcs.bcs.tuple([import_bcs2.SuiObjectRef, Owner]),
  eventsDigest: import_bcs.bcs.option(import_bcs2.ObjectDigest),
  dependencies: import_bcs.bcs.vector(import_bcs2.ObjectDigest)
});
const VersionDigest = import_bcs.bcs.tuple([import_bcs.bcs.u64(), import_bcs2.ObjectDigest]);
const ObjectIn = import_bcs.bcs.enum("ObjectIn", {
  NotExist: null,
  Exist: import_bcs.bcs.tuple([VersionDigest, Owner])
});
const ObjectOut = import_bcs.bcs.enum("ObjectOut", {
  NotExist: null,
  ObjectWrite: import_bcs.bcs.tuple([import_bcs2.ObjectDigest, Owner]),
  PackageWrite: VersionDigest
});
const IDOperation = import_bcs.bcs.enum("IDOperation", {
  None: null,
  Created: null,
  Deleted: null
});
const EffectsObjectChange = import_bcs.bcs.struct("EffectsObjectChange", {
  inputState: ObjectIn,
  outputState: ObjectOut,
  idOperation: IDOperation
});
const UnchangedSharedKind = import_bcs.bcs.enum("UnchangedSharedKind", {
  ReadOnlyRoot: VersionDigest,
  MutateDeleted: import_bcs.bcs.u64(),
  ReadDeleted: import_bcs.bcs.u64(),
  Cancelled: import_bcs.bcs.u64(),
  PerEpochConfig: null
});
const TransactionEffectsV2 = import_bcs.bcs.struct("TransactionEffectsV2", {
  status: ExecutionStatus,
  executedEpoch: import_bcs.bcs.u64(),
  gasUsed: GasCostSummary,
  transactionDigest: import_bcs2.ObjectDigest,
  gasObjectIndex: import_bcs.bcs.option(import_bcs.bcs.u32()),
  eventsDigest: import_bcs.bcs.option(import_bcs2.ObjectDigest),
  dependencies: import_bcs.bcs.vector(import_bcs2.ObjectDigest),
  lamportVersion: import_bcs.bcs.u64(),
  changedObjects: import_bcs.bcs.vector(import_bcs.bcs.tuple([import_bcs2.Address, EffectsObjectChange])),
  unchangedSharedObjects: import_bcs.bcs.vector(import_bcs.bcs.tuple([import_bcs2.Address, UnchangedSharedKind])),
  auxDataDigest: import_bcs.bcs.option(import_bcs2.ObjectDigest)
});
const TransactionEffects = import_bcs.bcs.enum("TransactionEffects", {
  V1: TransactionEffectsV1,
  V2: TransactionEffectsV2
});
//# sourceMappingURL=effects.js.map
