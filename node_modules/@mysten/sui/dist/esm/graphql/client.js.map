{"version": 3, "sources": ["../../../src/graphql/client.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { TypedDocumentNode } from '@graphql-typed-document-node/core';\nimport type { TadaDocumentNode } from 'gql.tada';\nimport type { DocumentNode } from 'graphql';\nimport { print } from 'graphql';\n\nexport type GraphQLDocument<\n\tResult = Record<string, unknown>,\n\tVariables = Record<string, unknown>,\n> =\n\t| string\n\t| DocumentNode\n\t| TypedDocumentNode<Result, Variables>\n\t| TadaDocumentNode<Result, Variables>;\n\nexport type GraphQLQueryOptions<\n\tResult = Record<string, unknown>,\n\tVariables = Record<string, unknown>,\n> = {\n\tquery: GraphQLDocument<Result, Variables>;\n\toperationName?: string;\n\textensions?: Record<string, unknown>;\n} & (Variables extends { [key: string]: never }\n\t? { variables?: Variables }\n\t: {\n\t\t\tvariables: Variables;\n\t\t});\n\nexport type GraphQLQueryResult<Result = Record<string, unknown>> = {\n\tdata?: Result;\n\terrors?: GraphQLResponseErrors;\n\textensions?: Record<string, unknown>;\n};\n\nexport type GraphQLResponseErrors = Array<{\n\tmessage: string;\n\tlocations?: { line: number; column: number }[];\n\tpath?: (string | number)[];\n}>;\n\nexport interface SuiGraphQLClientOptions<Queries extends Record<string, GraphQLDocument>> {\n\turl: string;\n\tfetch?: typeof fetch;\n\theaders?: Record<string, string>;\n\tqueries?: Queries;\n}\n\nexport class SuiGraphQLRequestError extends Error {}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport class SuiGraphQLClient<Queries extends Record<string, GraphQLDocument> = {}> {\n\t#url: string;\n\t#queries: Queries;\n\t#headers: Record<string, string>;\n\t#fetch: typeof fetch;\n\n\tconstructor({\n\t\turl,\n\t\tfetch: fetchFn = fetch,\n\t\theaders = {},\n\t\tqueries = {} as Queries,\n\t}: SuiGraphQLClientOptions<Queries>) {\n\t\tthis.#url = url;\n\t\tthis.#queries = queries;\n\t\tthis.#headers = headers;\n\t\tthis.#fetch = (...args) => fetchFn(...args);\n\t}\n\n\tasync query<Result = Record<string, unknown>, Variables = Record<string, unknown>>(\n\t\toptions: GraphQLQueryOptions<Result, Variables>,\n\t): Promise<GraphQLQueryResult<Result>> {\n\t\tconst res = await this.#fetch(this.#url, {\n\t\t\tmethod: 'POST',\n\t\t\theaders: {\n\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t...this.#headers,\n\t\t\t},\n\t\t\tbody: JSON.stringify({\n\t\t\t\tquery: typeof options.query === 'string' ? String(options.query) : print(options.query),\n\t\t\t\tvariables: options.variables,\n\t\t\t\textensions: options.extensions,\n\t\t\t\toperationName: options.operationName,\n\t\t\t}),\n\t\t});\n\n\t\tif (!res.ok) {\n\t\t\tthrow new SuiGraphQLRequestError(`GraphQL request failed: ${res.statusText} (${res.status})`);\n\t\t}\n\n\t\treturn await res.json();\n\t}\n\n\tasync execute<\n\t\tconst Query extends Extract<keyof Queries, string>,\n\t\tResult = Queries[Query] extends GraphQLDocument<infer R, unknown> ? R : Record<string, unknown>,\n\t\tVariables = Queries[Query] extends GraphQLDocument<unknown, infer V>\n\t\t\t? V\n\t\t\t: Record<string, unknown>,\n\t>(\n\t\tquery: Query,\n\t\toptions: Omit<GraphQLQueryOptions<Result, Variables>, 'query'>,\n\t): Promise<GraphQLQueryResult<Result>> {\n\t\treturn this.query({\n\t\t\t...(options as { variables: Record<string, unknown> }),\n\t\t\tquery: this.#queries[query]!,\n\t\t}) as Promise<GraphQLQueryResult<Result>>;\n\t}\n}\n"], "mappings": ";;;;;;;AAAA;AAMA,SAAS,aAAa;AA2Cf,MAAM,+BAA+B,MAAM;AAAC;AAG5C,MAAM,iBAAuE;AAAA,EAMnF,YAAY;AAAA,IACX;AAAA,IACA,OAAO,UAAU;AAAA,IACjB,UAAU,CAAC;AAAA,IACX,UAAU,CAAC;AAAA,EACZ,GAAqC;AAVrC;AACA;AACA;AACA;AAQC,uBAAK,MAAO;AACZ,uBAAK,UAAW;AAChB,uBAAK,UAAW;AAChB,uBAAK,QAAS,IAAI,SAAS,QAAQ,GAAG,IAAI;AAAA,EAC3C;AAAA,EAEA,MAAM,MACL,SACsC;AACtC,UAAM,MAAM,MAAM,mBAAK,QAAL,WAAY,mBAAK,OAAM;AAAA,MACxC,QAAQ;AAAA,MACR,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,GAAG,mBAAK;AAAA,MACT;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACpB,OAAO,OAAO,QAAQ,UAAU,WAAW,OAAO,QAAQ,KAAK,IAAI,MAAM,QAAQ,KAAK;AAAA,QACtF,WAAW,QAAQ;AAAA,QACnB,YAAY,QAAQ;AAAA,QACpB,eAAe,QAAQ;AAAA,MACxB,CAAC;AAAA,IACF;AAEA,QAAI,CAAC,IAAI,IAAI;AACZ,YAAM,IAAI,uBAAuB,2BAA2B,IAAI,UAAU,KAAK,IAAI,MAAM,GAAG;AAAA,IAC7F;AAEA,WAAO,MAAM,IAAI,KAAK;AAAA,EACvB;AAAA,EAEA,MAAM,QAOL,OACA,SACsC;AACtC,WAAO,KAAK,MAAM;AAAA,MACjB,GAAI;AAAA,MACJ,OAAO,mBAAK,UAAS,KAAK;AAAA,IAC3B,CAAC;AAAA,EACF;AACD;AAxDC;AACA;AACA;AACA;", "names": []}