{"version": 3, "sources": ["../../../src/verify/verify.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64 } from '@mysten/bcs';\n\nimport type { PublicKey, SignatureFlag, SignatureScheme } from '../cryptography/index.js';\nimport { parseSerializedSignature, SIGNATURE_FLAG_TO_SCHEME } from '../cryptography/index.js';\nimport type { SuiGraphQLClient } from '../graphql/client.js';\nimport { Ed25519PublicKey } from '../keypairs/ed25519/publickey.js';\nimport { PasskeyPublicKey } from '../keypairs/passkey/publickey.js';\nimport { Secp256k1PublicKey } from '../keypairs/secp256k1/publickey.js';\nimport { Secp256r1PublicKey } from '../keypairs/secp256r1/publickey.js';\n// eslint-disable-next-line import/no-cycle\nimport { MultiSigPublicKey } from '../multisig/publickey.js';\nimport { ZkLoginPublicIdentifier } from '../zklogin/publickey.js';\n\nexport async function verifySignature(\n\tbytes: Uint8Array,\n\tsignature: string,\n\toptions?: {\n\t\taddress?: string;\n\t},\n): Promise<PublicKey> {\n\tconst parsedSignature = parseSignature(signature);\n\n\tif (!(await parsedSignature.publicKey.verify(bytes, parsedSignature.serializedSignature))) {\n\t\tthrow new Error(`Signature is not valid for the provided data`);\n\t}\n\n\tif (options?.address && !parsedSignature.publicKey.verifyAddress(options.address)) {\n\t\tthrow new Error(`Signature is not valid for the provided address`);\n\t}\n\n\treturn parsedSignature.publicKey;\n}\n\nexport async function verifyPersonalMessageSignature(\n\tmessage: Uint8Array,\n\tsignature: string,\n\toptions: { client?: SuiGraphQLClient; address?: string } = {},\n): Promise<PublicKey> {\n\tconst parsedSignature = parseSignature(signature, options);\n\n\tif (\n\t\t!(await parsedSignature.publicKey.verifyPersonalMessage(\n\t\t\tmessage,\n\t\t\tparsedSignature.serializedSignature,\n\t\t))\n\t) {\n\t\tthrow new Error(`Signature is not valid for the provided message`);\n\t}\n\n\tif (options?.address && !parsedSignature.publicKey.verifyAddress(options.address)) {\n\t\tthrow new Error(`Signature is not valid for the provided address`);\n\t}\n\n\treturn parsedSignature.publicKey;\n}\n\nexport async function verifyTransactionSignature(\n\ttransaction: Uint8Array,\n\tsignature: string,\n\toptions: { client?: SuiGraphQLClient; address?: string } = {},\n): Promise<PublicKey> {\n\tconst parsedSignature = parseSignature(signature, options);\n\n\tif (\n\t\t!(await parsedSignature.publicKey.verifyTransaction(\n\t\t\ttransaction,\n\t\t\tparsedSignature.serializedSignature,\n\t\t))\n\t) {\n\t\tthrow new Error(`Signature is not valid for the provided Transaction`);\n\t}\n\n\tif (options?.address && !parsedSignature.publicKey.verifyAddress(options.address)) {\n\t\tthrow new Error(`Signature is not valid for the provided address`);\n\t}\n\n\treturn parsedSignature.publicKey;\n}\n\nfunction parseSignature(signature: string, options: { client?: SuiGraphQLClient } = {}) {\n\tconst parsedSignature = parseSerializedSignature(signature);\n\n\tif (parsedSignature.signatureScheme === 'MultiSig') {\n\t\treturn {\n\t\t\t...parsedSignature,\n\t\t\tpublicKey: new MultiSigPublicKey(parsedSignature.multisig.multisig_pk),\n\t\t};\n\t}\n\n\tconst publicKey = publicKeyFromRawBytes(\n\t\tparsedSignature.signatureScheme,\n\t\tparsedSignature.publicKey,\n\t\toptions,\n\t);\n\treturn {\n\t\t...parsedSignature,\n\t\tpublicKey,\n\t};\n}\n\nexport function publicKeyFromRawBytes(\n\tsignatureScheme: SignatureScheme,\n\tbytes: Uint8Array,\n\toptions: { client?: SuiGraphQLClient } = {},\n): PublicKey {\n\tswitch (signatureScheme) {\n\t\tcase 'ED25519':\n\t\t\treturn new Ed25519PublicKey(bytes);\n\t\tcase 'Secp256k1':\n\t\t\treturn new Secp256k1PublicKey(bytes);\n\t\tcase 'Secp256r1':\n\t\t\treturn new Secp256r1PublicKey(bytes);\n\t\tcase 'MultiSig':\n\t\t\treturn new MultiSigPublicKey(bytes);\n\t\tcase 'ZkLogin':\n\t\t\treturn new ZkLoginPublicIdentifier(bytes, options);\n\t\tcase 'Passkey':\n\t\t\treturn new PasskeyPublicKey(bytes);\n\t\tdefault:\n\t\t\tthrow new Error(`Unsupported signature scheme ${signatureScheme}`);\n\t}\n}\n\nexport function publicKeyFromSuiBytes(\n\tpublicKey: string | Uint8Array,\n\toptions: { client?: SuiGraphQLClient } = {},\n) {\n\tconst bytes = typeof publicKey === 'string' ? fromBase64(publicKey) : publicKey;\n\n\tconst signatureScheme = SIGNATURE_FLAG_TO_SCHEME[bytes[0] as SignatureFlag];\n\n\treturn publicKeyFromRawBytes(signatureScheme, bytes.slice(1), options);\n}\n"], "mappings": "AAGA,SAAS,kBAAkB;AAG3B,SAAS,0BAA0B,gCAAgC;AAEnE,SAAS,wBAAwB;AACjC,SAAS,wBAAwB;AACjC,SAAS,0BAA0B;AACnC,SAAS,0BAA0B;AAEnC,SAAS,yBAAyB;AAClC,SAAS,+BAA+B;AAExC,eAAsB,gBACrB,OACA,WACA,SAGqB;AACrB,QAAM,kBAAkB,eAAe,SAAS;AAEhD,MAAI,CAAE,MAAM,gBAAgB,UAAU,OAAO,OAAO,gBAAgB,mBAAmB,GAAI;AAC1F,UAAM,IAAI,MAAM,8CAA8C;AAAA,EAC/D;AAEA,MAAI,SAAS,WAAW,CAAC,gBAAgB,UAAU,cAAc,QAAQ,OAAO,GAAG;AAClF,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAClE;AAEA,SAAO,gBAAgB;AACxB;AAEA,eAAsB,+BACrB,SACA,WACA,UAA2D,CAAC,GACvC;AACrB,QAAM,kBAAkB,eAAe,WAAW,OAAO;AAEzD,MACC,CAAE,MAAM,gBAAgB,UAAU;AAAA,IACjC;AAAA,IACA,gBAAgB;AAAA,EACjB,GACC;AACD,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAClE;AAEA,MAAI,SAAS,WAAW,CAAC,gBAAgB,UAAU,cAAc,QAAQ,OAAO,GAAG;AAClF,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAClE;AAEA,SAAO,gBAAgB;AACxB;AAEA,eAAsB,2BACrB,aACA,WACA,UAA2D,CAAC,GACvC;AACrB,QAAM,kBAAkB,eAAe,WAAW,OAAO;AAEzD,MACC,CAAE,MAAM,gBAAgB,UAAU;AAAA,IACjC;AAAA,IACA,gBAAgB;AAAA,EACjB,GACC;AACD,UAAM,IAAI,MAAM,qDAAqD;AAAA,EACtE;AAEA,MAAI,SAAS,WAAW,CAAC,gBAAgB,UAAU,cAAc,QAAQ,OAAO,GAAG;AAClF,UAAM,IAAI,MAAM,iDAAiD;AAAA,EAClE;AAEA,SAAO,gBAAgB;AACxB;AAEA,SAAS,eAAe,WAAmB,UAAyC,CAAC,GAAG;AACvF,QAAM,kBAAkB,yBAAyB,SAAS;AAE1D,MAAI,gBAAgB,oBAAoB,YAAY;AACnD,WAAO;AAAA,MACN,GAAG;AAAA,MACH,WAAW,IAAI,kBAAkB,gBAAgB,SAAS,WAAW;AAAA,IACtE;AAAA,EACD;AAEA,QAAM,YAAY;AAAA,IACjB,gBAAgB;AAAA,IAChB,gBAAgB;AAAA,IAChB;AAAA,EACD;AACA,SAAO;AAAA,IACN,GAAG;AAAA,IACH;AAAA,EACD;AACD;AAEO,SAAS,sBACf,iBACA,OACA,UAAyC,CAAC,GAC9B;AACZ,UAAQ,iBAAiB;AAAA,IACxB,KAAK;AACJ,aAAO,IAAI,iBAAiB,KAAK;AAAA,IAClC,KAAK;AACJ,aAAO,IAAI,mBAAmB,KAAK;AAAA,IACpC,KAAK;AACJ,aAAO,IAAI,mBAAmB,KAAK;AAAA,IACpC,KAAK;AACJ,aAAO,IAAI,kBAAkB,KAAK;AAAA,IACnC,KAAK;AACJ,aAAO,IAAI,wBAAwB,OAAO,OAAO;AAAA,IAClD,KAAK;AACJ,aAAO,IAAI,iBAAiB,KAAK;AAAA,IAClC;AACC,YAAM,IAAI,MAAM,gCAAgC,eAAe,EAAE;AAAA,EACnE;AACD;AAEO,SAAS,sBACf,WACA,UAAyC,CAAC,GACzC;AACD,QAAM,QAAQ,OAAO,cAAc,WAAW,WAAW,SAAS,IAAI;AAEtE,QAAM,kBAAkB,yBAAyB,MAAM,CAAC,CAAkB;AAE1E,SAAO,sBAAsB,iBAAiB,MAAM,MAAM,CAAC,GAAG,OAAO;AACtE;", "names": []}