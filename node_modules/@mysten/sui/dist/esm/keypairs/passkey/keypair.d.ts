import type { AuthenticationCredential, RegistrationCredential } from '@simplewebauthn/typescript-types';
import type { IntentScope, SignatureWithBytes } from '../../cryptography/index.js';
import { Signer } from '../../cryptography/index.js';
import type { PublicKey } from '../../cryptography/publickey.js';
import type { SignatureScheme } from '../../cryptography/signature-scheme.js';
type DeepPartialConfigKeys = 'rp' | 'user' | 'authenticatorSelection';
type DeepPartial<T> = T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;
export type BrowserPasswordProviderOptions = Pick<DeepPartial<PublicKeyCredentialCreationOptions>, DeepPartialConfigKeys> & Omit<Partial<PublicKeyCredentialCreationOptions>, DeepPartialConfigKeys | 'pubKeyCredParams' | 'challenge'>;
export interface PasskeyProvider {
    create(): Promise<RegistrationCredential>;
    get(challenge: Uint8Array): Promise<AuthenticationCredential>;
}
export declare class BrowserPasskeyProvider implements PasskeyProvider {
    #private;
    constructor(name: string, options: BrowserPasswordProviderOptions);
    create(): Promise<RegistrationCredential>;
    get(challenge: Uint8Array): Promise<AuthenticationCredential>;
}
/**
 * @experimental
 * A passkey signer used for signing transactions. This is a client side implementation for [SIP-9](https://github.com/sui-foundation/sips/blob/main/sips/sip-9.md).
 */
export declare class PasskeyKeypair extends Signer {
    private publicKey;
    private provider;
    /**
     * Get the key scheme of passkey,
     */
    getKeyScheme(): SignatureScheme;
    /**
     * Creates an instance of Passkey signer. It's expected to call the static `getPasskeyInstance` method to create an instance.
     * For example:
     * ```
     * const signer = await PasskeyKeypair.getPasskeyInstance();
     * ```
     */
    constructor(publicKey: Uint8Array, provider: PasskeyProvider);
    /**
     * Creates an instance of Passkey signer invoking the passkey from navigator.
     */
    static getPasskeyInstance(provider: PasskeyProvider): Promise<PasskeyKeypair>;
    /**
     * Return the public key for this passkey.
     */
    getPublicKey(): PublicKey;
    /**
     * Return the signature for the provided data (i.e. blake2b(intent_message)).
     * This is sent to passkey as the challenge field.
     */
    sign(data: Uint8Array): Promise<Uint8Array>;
    /**
     * This overrides the base class implementation that accepts the raw bytes and signs its
     * digest of the intent message, then serialize it with the passkey flag.
     */
    signWithIntent(bytes: Uint8Array, intent: IntentScope): Promise<SignatureWithBytes>;
}
export {};
