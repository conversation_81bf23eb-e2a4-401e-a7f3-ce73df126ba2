{"version": 3, "sources": ["../../../src/transactions/utils.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiMoveNormalizedType } from '../client/index.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { CallArg } from './data/internal.js';\n\nexport function extractMutableReference(\n\tnormalizedType: SuiMoveNormalizedType,\n): SuiMoveNormalizedType | undefined {\n\treturn typeof normalizedType === 'object' && 'MutableReference' in normalizedType\n\t\t? normalizedType.MutableReference\n\t\t: undefined;\n}\n\nexport function extractReference(\n\tnormalizedType: SuiMoveNormalizedType,\n): SuiMoveNormalizedType | undefined {\n\treturn typeof normalizedType === 'object' && 'Reference' in normalizedType\n\t\t? normalizedType.Reference\n\t\t: undefined;\n}\n\nexport function extractStructTag(\n\tnormalizedType: SuiMoveNormalizedType,\n): Extract<SuiMoveNormalizedType, { Struct: unknown }> | undefined {\n\tif (typeof normalizedType === 'object' && 'Struct' in normalizedType) {\n\t\treturn normalizedType;\n\t}\n\n\tconst ref = extractReference(normalizedType);\n\tconst mutRef = extractMutableReference(normalizedType);\n\n\tif (typeof ref === 'object' && 'Struct' in ref) {\n\t\treturn ref;\n\t}\n\n\tif (typeof mutRef === 'object' && 'Struct' in mutRef) {\n\t\treturn mutRef;\n\t}\n\treturn undefined;\n}\n\nexport function getIdFromCallArg(arg: string | CallArg) {\n\tif (typeof arg === 'string') {\n\t\treturn normalizeSuiAddress(arg);\n\t}\n\n\tif (arg.Object) {\n\t\tif (arg.Object.ImmOrOwnedObject) {\n\t\t\treturn normalizeSuiAddress(arg.Object.ImmOrOwnedObject.objectId);\n\t\t}\n\n\t\tif (arg.Object.Receiving) {\n\t\t\treturn normalizeSuiAddress(arg.Object.Receiving.objectId);\n\t\t}\n\n\t\treturn normalizeSuiAddress(arg.Object.SharedObject.objectId);\n\t}\n\n\tif (arg.UnresolvedObject) {\n\t\treturn normalizeSuiAddress(arg.UnresolvedObject.objectId);\n\t}\n\n\treturn undefined;\n}\n"], "mappings": "AAIA,SAAS,2BAA2B;AAG7B,SAAS,wBACf,gBACoC;AACpC,SAAO,OAAO,mBAAmB,YAAY,sBAAsB,iBAChE,eAAe,mBACf;AACJ;AAEO,SAAS,iBACf,gBACoC;AACpC,SAAO,OAAO,mBAAmB,YAAY,eAAe,iBACzD,eAAe,YACf;AACJ;AAEO,SAAS,iBACf,gBACkE;AAClE,MAAI,OAAO,mBAAmB,YAAY,YAAY,gBAAgB;AACrE,WAAO;AAAA,EACR;AAEA,QAAM,MAAM,iBAAiB,cAAc;AAC3C,QAAM,SAAS,wBAAwB,cAAc;AAErD,MAAI,OAAO,QAAQ,YAAY,YAAY,KAAK;AAC/C,WAAO;AAAA,EACR;AAEA,MAAI,OAAO,WAAW,YAAY,YAAY,QAAQ;AACrD,WAAO;AAAA,EACR;AACA,SAAO;AACR;AAEO,SAAS,iBAAiB,KAAuB;AACvD,MAAI,OAAO,QAAQ,UAAU;AAC5B,WAAO,oBAAoB,GAAG;AAAA,EAC/B;AAEA,MAAI,IAAI,QAAQ;AACf,QAAI,IAAI,OAAO,kBAAkB;AAChC,aAAO,oBAAoB,IAAI,OAAO,iBAAiB,QAAQ;AAAA,IAChE;AAEA,QAAI,IAAI,OAAO,WAAW;AACzB,aAAO,oBAAoB,IAAI,OAAO,UAAU,QAAQ;AAAA,IACzD;AAEA,WAAO,oBAAoB,IAAI,OAAO,aAAa,QAAQ;AAAA,EAC5D;AAEA,MAAI,IAAI,kBAAkB;AACzB,WAAO,oBAAoB,IAAI,iBAAiB,QAAQ;AAAA,EACzD;AAEA,SAAO;AACR;", "names": []}