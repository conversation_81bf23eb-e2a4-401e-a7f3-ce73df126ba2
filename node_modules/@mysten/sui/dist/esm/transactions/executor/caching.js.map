{"version": 3, "sources": ["../../../../src/transactions/executor/caching.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '../../bcs/index.js';\nimport type { ExecuteTransactionBlockParams, SuiClient } from '../../client/index.js';\nimport type { Signer } from '../../cryptography/keypair.js';\nimport type { BuildTransactionOptions } from '../json-rpc-resolver.js';\nimport type { ObjectCacheOptions } from '../ObjectCache.js';\nimport { ObjectCache } from '../ObjectCache.js';\nimport type { Transaction } from '../Transaction.js';\nimport { isTransaction } from '../Transaction.js';\n\nexport class CachingTransactionExecutor {\n\t#client: SuiClient;\n\t#lastDigest: string | null = null;\n\tcache: ObjectCache;\n\n\tconstructor({\n\t\tclient,\n\t\t...options\n\t}: ObjectCacheOptions & {\n\t\tclient: SuiClient;\n\t}) {\n\t\tthis.#client = client;\n\t\tthis.cache = new ObjectCache(options);\n\t}\n\n\t/**\n\t * Clears all Owned objects\n\t * Immutable objects, Shared objects, and Move function definitions will be preserved\n\t */\n\tasync reset() {\n\t\tawait Promise.all([\n\t\t\tthis.cache.clearOwnedObjects(),\n\t\t\tthis.cache.clearCustom(),\n\t\t\tthis.waitForLastTransaction(),\n\t\t]);\n\t}\n\n\tasync buildTransaction({\n\t\ttransaction,\n\t\t...options\n\t}: { transaction: Transaction } & BuildTransactionOptions) {\n\t\ttransaction.addBuildPlugin(this.cache.asPlugin());\n\t\treturn transaction.build({\n\t\t\tclient: this.#client,\n\t\t\t...options,\n\t\t});\n\t}\n\n\tasync executeTransaction({\n\t\ttransaction,\n\t\toptions,\n\t\t...input\n\t}: {\n\t\ttransaction: Transaction | Uint8Array;\n\t} & Omit<ExecuteTransactionBlockParams, 'transactionBlock'>) {\n\t\tconst bytes = isTransaction(transaction)\n\t\t\t? await this.buildTransaction({ transaction })\n\t\t\t: transaction;\n\n\t\tconst results = await this.#client.executeTransactionBlock({\n\t\t\t...input,\n\t\t\ttransactionBlock: bytes,\n\t\t\toptions: {\n\t\t\t\t...options,\n\t\t\t\tshowRawEffects: true,\n\t\t\t},\n\t\t});\n\n\t\tif (results.rawEffects) {\n\t\t\tconst effects = bcs.TransactionEffects.parse(Uint8Array.from(results.rawEffects));\n\t\t\tawait this.applyEffects(effects);\n\t\t}\n\n\t\treturn results;\n\t}\n\n\tasync signAndExecuteTransaction({\n\t\toptions,\n\t\ttransaction,\n\t\t...input\n\t}: {\n\t\ttransaction: Transaction;\n\n\t\tsigner: Signer;\n\t} & Omit<ExecuteTransactionBlockParams, 'transactionBlock' | 'signature'>) {\n\t\ttransaction.setSenderIfNotSet(input.signer.toSuiAddress());\n\t\tconst bytes = await this.buildTransaction({ transaction });\n\t\tconst { signature } = await input.signer.signTransaction(bytes);\n\t\tconst results = await this.executeTransaction({\n\t\t\ttransaction: bytes,\n\t\t\tsignature,\n\t\t\toptions,\n\t\t});\n\n\t\treturn results;\n\t}\n\n\tasync applyEffects(effects: typeof bcs.TransactionEffects.$inferType) {\n\t\tthis.#lastDigest = effects.V2?.transactionDigest ?? null;\n\t\tawait this.cache.applyEffects(effects);\n\t}\n\n\tasync waitForLastTransaction() {\n\t\tif (this.#lastDigest) {\n\t\t\tawait this.#client.waitForTransaction({ digest: this.#lastDigest });\n\t\t\tthis.#lastDigest = null;\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;AAAA;AAGA,SAAS,WAAW;AAKpB,SAAS,mBAAmB;AAE5B,SAAS,qBAAqB;AAEvB,MAAM,2BAA2B;AAAA,EAKvC,YAAY;AAAA,IACX;AAAA,IACA,GAAG;AAAA,EACJ,GAEG;AATH;AACA,oCAA6B;AAS5B,uBAAK,SAAU;AACf,SAAK,QAAQ,IAAI,YAAY,OAAO;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,QAAQ;AACb,UAAM,QAAQ,IAAI;AAAA,MACjB,KAAK,MAAM,kBAAkB;AAAA,MAC7B,KAAK,MAAM,YAAY;AAAA,MACvB,KAAK,uBAAuB;AAAA,IAC7B,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB;AAAA,IACtB;AAAA,IACA,GAAG;AAAA,EACJ,GAA2D;AAC1D,gBAAY,eAAe,KAAK,MAAM,SAAS,CAAC;AAChD,WAAO,YAAY,MAAM;AAAA,MACxB,QAAQ,mBAAK;AAAA,MACb,GAAG;AAAA,IACJ,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,mBAAmB;AAAA,IACxB;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GAE6D;AAC5D,UAAM,QAAQ,cAAc,WAAW,IACpC,MAAM,KAAK,iBAAiB,EAAE,YAAY,CAAC,IAC3C;AAEH,UAAM,UAAU,MAAM,mBAAK,SAAQ,wBAAwB;AAAA,MAC1D,GAAG;AAAA,MACH,kBAAkB;AAAA,MAClB,SAAS;AAAA,QACR,GAAG;AAAA,QACH,gBAAgB;AAAA,MACjB;AAAA,IACD,CAAC;AAED,QAAI,QAAQ,YAAY;AACvB,YAAM,UAAU,IAAI,mBAAmB,MAAM,WAAW,KAAK,QAAQ,UAAU,CAAC;AAChF,YAAM,KAAK,aAAa,OAAO;AAAA,IAChC;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,0BAA0B;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GAI2E;AAC1E,gBAAY,kBAAkB,MAAM,OAAO,aAAa,CAAC;AACzD,UAAM,QAAQ,MAAM,KAAK,iBAAiB,EAAE,YAAY,CAAC;AACzD,UAAM,EAAE,UAAU,IAAI,MAAM,MAAM,OAAO,gBAAgB,KAAK;AAC9D,UAAM,UAAU,MAAM,KAAK,mBAAmB;AAAA,MAC7C,aAAa;AAAA,MACb;AAAA,MACA;AAAA,IACD,CAAC;AAED,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,aAAa,SAAmD;AACrE,uBAAK,aAAc,QAAQ,IAAI,qBAAqB;AACpD,UAAM,KAAK,MAAM,aAAa,OAAO;AAAA,EACtC;AAAA,EAEA,MAAM,yBAAyB;AAC9B,QAAI,mBAAK,cAAa;AACrB,YAAM,mBAAK,SAAQ,mBAAmB,EAAE,QAAQ,mBAAK,aAAY,CAAC;AAClE,yBAAK,aAAc;AAAA,IACpB;AAAA,EACD;AACD;AAjGC;AACA;", "names": []}