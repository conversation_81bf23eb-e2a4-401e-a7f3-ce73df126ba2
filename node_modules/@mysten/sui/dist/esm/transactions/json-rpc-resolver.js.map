{"version": 3, "sources": ["../../../src/transactions/json-rpc-resolver.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { parse } from 'valibot';\n\nimport type { BcsType } from '../bcs/index.js';\nimport { bcs } from '../bcs/index.js';\nimport type { SuiClient } from '../client/client.js';\nimport { normalizeSuiAddress, normalizeSuiObjectId, SUI_TYPE_ARG } from '../utils/index.js';\nimport { ObjectRef } from './data/internal.js';\nimport type { Argument, CallArg, Command, OpenMoveTypeSignature } from './data/internal.js';\nimport { Inputs } from './Inputs.js';\nimport { getPureBcsSchema, isTxContext, normalizedTypeToMoveTypeSignature } from './serializer.js';\nimport type { TransactionDataBuilder } from './TransactionData.js';\n\n// The maximum objects that can be fetched at once using multiGetObjects.\nconst MAX_OBJECTS_PER_FETCH = 50;\n\n// An amount of gas (in gas units) that is added to transactions as an overhead to ensure transactions do not fail.\nconst GAS_SAFE_OVERHEAD = 1000n;\nconst MAX_GAS = 50_000_000_000;\n\nexport interface BuildTransactionOptions {\n\tclient?: SuiClient;\n\tonlyTransactionKind?: boolean;\n}\n\nexport interface SerializeTransactionOptions extends BuildTransactionOptions {\n\tsupportedIntents?: string[];\n}\n\nexport type TransactionPlugin = (\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n\tnext: () => Promise<void>,\n) => Promise<void>;\n\nexport async function resolveTransactionData(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n\tnext: () => Promise<void>,\n) {\n\tawait normalizeInputs(transactionData, options);\n\tawait resolveObjectReferences(transactionData, options);\n\n\tif (!options.onlyTransactionKind) {\n\t\tawait setGasPrice(transactionData, options);\n\t\tawait setGasBudget(transactionData, options);\n\t\tawait setGasPayment(transactionData, options);\n\t}\n\tawait validate(transactionData);\n\treturn await next();\n}\n\nasync function setGasPrice(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n) {\n\tif (!transactionData.gasConfig.price) {\n\t\ttransactionData.gasConfig.price = String(await getClient(options).getReferenceGasPrice());\n\t}\n}\n\nasync function setGasBudget(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n) {\n\tif (transactionData.gasConfig.budget) {\n\t\treturn;\n\t}\n\n\tconst dryRunResult = await getClient(options).dryRunTransactionBlock({\n\t\ttransactionBlock: transactionData.build({\n\t\t\toverrides: {\n\t\t\t\tgasData: {\n\t\t\t\t\tbudget: String(MAX_GAS),\n\t\t\t\t\tpayment: [],\n\t\t\t\t},\n\t\t\t},\n\t\t}),\n\t});\n\n\tif (dryRunResult.effects.status.status !== 'success') {\n\t\tthrow new Error(\n\t\t\t`Dry run failed, could not automatically determine a budget: ${dryRunResult.effects.status.error}`,\n\t\t\t{ cause: dryRunResult },\n\t\t);\n\t}\n\n\tconst safeOverhead = GAS_SAFE_OVERHEAD * BigInt(transactionData.gasConfig.price || 1n);\n\n\tconst baseComputationCostWithOverhead =\n\t\tBigInt(dryRunResult.effects.gasUsed.computationCost) + safeOverhead;\n\n\tconst gasBudget =\n\t\tbaseComputationCostWithOverhead +\n\t\tBigInt(dryRunResult.effects.gasUsed.storageCost) -\n\t\tBigInt(dryRunResult.effects.gasUsed.storageRebate);\n\n\ttransactionData.gasConfig.budget = String(\n\t\tgasBudget > baseComputationCostWithOverhead ? gasBudget : baseComputationCostWithOverhead,\n\t);\n}\n\n// The current default is just picking _all_ coins we can which may not be ideal.\nasync function setGasPayment(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n) {\n\tif (!transactionData.gasConfig.payment) {\n\t\tconst coins = await getClient(options).getCoins({\n\t\t\towner: transactionData.gasConfig.owner || transactionData.sender!,\n\t\t\tcoinType: SUI_TYPE_ARG,\n\t\t});\n\n\t\tconst paymentCoins = coins.data\n\t\t\t// Filter out coins that are also used as input:\n\t\t\t.filter((coin) => {\n\t\t\t\tconst matchingInput = transactionData.inputs.find((input) => {\n\t\t\t\t\tif (input.Object?.ImmOrOwnedObject) {\n\t\t\t\t\t\treturn coin.coinObjectId === input.Object.ImmOrOwnedObject.objectId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn false;\n\t\t\t\t});\n\n\t\t\t\treturn !matchingInput;\n\t\t\t})\n\t\t\t.map((coin) => ({\n\t\t\t\tobjectId: coin.coinObjectId,\n\t\t\t\tdigest: coin.digest,\n\t\t\t\tversion: coin.version,\n\t\t\t}));\n\n\t\tif (!paymentCoins.length) {\n\t\t\tthrow new Error('No valid gas coins found for the transaction.');\n\t\t}\n\n\t\ttransactionData.gasConfig.payment = paymentCoins.map((payment) => parse(ObjectRef, payment));\n\t}\n}\n\nasync function resolveObjectReferences(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n) {\n\t// Keep track of the object references that will need to be resolved at the end of the transaction.\n\t// We keep the input by-reference to avoid needing to re-resolve it:\n\tconst objectsToResolve = transactionData.inputs.filter((input) => {\n\t\treturn (\n\t\t\tinput.UnresolvedObject &&\n\t\t\t!(input.UnresolvedObject.version || input.UnresolvedObject?.initialSharedVersion)\n\t\t);\n\t}) as Extract<CallArg, { UnresolvedObject: unknown }>[];\n\n\tconst dedupedIds = [\n\t\t...new Set(\n\t\t\tobjectsToResolve.map((input) => normalizeSuiObjectId(input.UnresolvedObject.objectId)),\n\t\t),\n\t];\n\n\tconst objectChunks = dedupedIds.length ? chunk(dedupedIds, MAX_OBJECTS_PER_FETCH) : [];\n\tconst resolved = (\n\t\tawait Promise.all(\n\t\t\tobjectChunks.map((chunk) =>\n\t\t\t\tgetClient(options).multiGetObjects({\n\t\t\t\t\tids: chunk,\n\t\t\t\t\toptions: { showOwner: true },\n\t\t\t\t}),\n\t\t\t),\n\t\t)\n\t).flat();\n\n\tconst responsesById = new Map(\n\t\tdedupedIds.map((id, index) => {\n\t\t\treturn [id, resolved[index]];\n\t\t}),\n\t);\n\n\tconst invalidObjects = Array.from(responsesById)\n\t\t.filter(([_, obj]) => obj.error)\n\t\t.map(([_, obj]) => JSON.stringify(obj.error));\n\n\tif (invalidObjects.length) {\n\t\tthrow new Error(`The following input objects are invalid: ${invalidObjects.join(', ')}`);\n\t}\n\n\tconst objects = resolved.map((object) => {\n\t\tif (object.error || !object.data) {\n\t\t\tthrow new Error(`Failed to fetch object: ${object.error}`);\n\t\t}\n\t\tconst owner = object.data.owner;\n\t\tconst initialSharedVersion =\n\t\t\towner && typeof owner === 'object' && 'Shared' in owner\n\t\t\t\t? owner.Shared.initial_shared_version\n\t\t\t\t: null;\n\n\t\treturn {\n\t\t\tobjectId: object.data.objectId,\n\t\t\tdigest: object.data.digest,\n\t\t\tversion: object.data.version,\n\t\t\tinitialSharedVersion,\n\t\t};\n\t});\n\n\tconst objectsById = new Map(\n\t\tdedupedIds.map((id, index) => {\n\t\t\treturn [id, objects[index]];\n\t\t}),\n\t);\n\n\tfor (const [index, input] of transactionData.inputs.entries()) {\n\t\tif (!input.UnresolvedObject) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tlet updated: CallArg | undefined;\n\t\tconst id = normalizeSuiAddress(input.UnresolvedObject.objectId);\n\t\tconst object = objectsById.get(id);\n\n\t\tif (input.UnresolvedObject.initialSharedVersion ?? object?.initialSharedVersion) {\n\t\t\tupdated = Inputs.SharedObjectRef({\n\t\t\t\tobjectId: id,\n\t\t\t\tinitialSharedVersion:\n\t\t\t\t\tinput.UnresolvedObject.initialSharedVersion || object?.initialSharedVersion!,\n\t\t\t\tmutable: isUsedAsMutable(transactionData, index),\n\t\t\t});\n\t\t} else if (isUsedAsReceiving(transactionData, index)) {\n\t\t\tupdated = Inputs.ReceivingRef(\n\t\t\t\t{\n\t\t\t\t\tobjectId: id,\n\t\t\t\t\tdigest: input.UnresolvedObject.digest ?? object?.digest!,\n\t\t\t\t\tversion: input.UnresolvedObject.version ?? object?.version!,\n\t\t\t\t}!,\n\t\t\t);\n\t\t}\n\n\t\ttransactionData.inputs[transactionData.inputs.indexOf(input)] =\n\t\t\tupdated ??\n\t\t\tInputs.ObjectRef({\n\t\t\t\tobjectId: id,\n\t\t\t\tdigest: input.UnresolvedObject.digest ?? object?.digest!,\n\t\t\t\tversion: input.UnresolvedObject.version ?? object?.version!,\n\t\t\t});\n\t}\n}\n\nasync function normalizeInputs(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n) {\n\tconst { inputs, commands } = transactionData;\n\tconst moveCallsToResolve: Extract<Command, { MoveCall: unknown }>['MoveCall'][] = [];\n\tconst moveFunctionsToResolve = new Set<string>();\n\n\tcommands.forEach((command) => {\n\t\t// Special case move call:\n\t\tif (command.MoveCall) {\n\t\t\t// Determine if any of the arguments require encoding.\n\t\t\t// - If they don't, then this is good to go.\n\t\t\t// - If they do, then we need to fetch the normalized move module.\n\n\t\t\t// If we already know the argument types, we don't need to resolve them again\n\t\t\tif (command.MoveCall._argumentTypes) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst inputs = command.MoveCall.arguments.map((arg) => {\n\t\t\t\tif (arg.$kind === 'Input') {\n\t\t\t\t\treturn transactionData.inputs[arg.Input];\n\t\t\t\t}\n\t\t\t\treturn null;\n\t\t\t});\n\t\t\tconst needsResolution = inputs.some(\n\t\t\t\t(input) => input?.UnresolvedPure || input?.UnresolvedObject,\n\t\t\t);\n\n\t\t\tif (needsResolution) {\n\t\t\t\tconst functionName = `${command.MoveCall.package}::${command.MoveCall.module}::${command.MoveCall.function}`;\n\t\t\t\tmoveFunctionsToResolve.add(functionName);\n\t\t\t\tmoveCallsToResolve.push(command.MoveCall);\n\t\t\t}\n\t\t}\n\n\t\t// Special handling for values that where previously encoded using the wellKnownEncoding pattern.\n\t\t// This should only happen when transaction data was hydrated from an old version of the SDK\n\t\tswitch (command.$kind) {\n\t\t\tcase 'SplitCoins':\n\t\t\t\tcommand.SplitCoins.amounts.forEach((amount) => {\n\t\t\t\t\tnormalizeRawArgument(amount, bcs.U64, transactionData);\n\t\t\t\t});\n\t\t\t\tbreak;\n\t\t\tcase 'TransferObjects':\n\t\t\t\tnormalizeRawArgument(command.TransferObjects.address, bcs.Address, transactionData);\n\t\t\t\tbreak;\n\t\t}\n\t});\n\n\tconst moveFunctionParameters = new Map<string, OpenMoveTypeSignature[]>();\n\tif (moveFunctionsToResolve.size > 0) {\n\t\tconst client = getClient(options);\n\t\tawait Promise.all(\n\t\t\t[...moveFunctionsToResolve].map(async (functionName) => {\n\t\t\t\tconst [packageId, moduleId, functionId] = functionName.split('::');\n\t\t\t\tconst def = await client.getNormalizedMoveFunction({\n\t\t\t\t\tpackage: packageId,\n\t\t\t\t\tmodule: moduleId,\n\t\t\t\t\tfunction: functionId,\n\t\t\t\t});\n\n\t\t\t\tmoveFunctionParameters.set(\n\t\t\t\t\tfunctionName,\n\t\t\t\t\tdef.parameters.map((param) => normalizedTypeToMoveTypeSignature(param)),\n\t\t\t\t);\n\t\t\t}),\n\t\t);\n\t}\n\n\tif (moveCallsToResolve.length) {\n\t\tawait Promise.all(\n\t\t\tmoveCallsToResolve.map(async (moveCall) => {\n\t\t\t\tconst parameters = moveFunctionParameters.get(\n\t\t\t\t\t`${moveCall.package}::${moveCall.module}::${moveCall.function}`,\n\t\t\t\t);\n\n\t\t\t\tif (!parameters) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// Entry functions can have a mutable reference to an instance of the TxContext\n\t\t\t\t// struct defined in the TxContext module as the last parameter. The caller of\n\t\t\t\t// the function does not need to pass it in as an argument.\n\t\t\t\tconst hasTxContext = parameters.length > 0 && isTxContext(parameters.at(-1)!);\n\t\t\t\tconst params = hasTxContext ? parameters.slice(0, parameters.length - 1) : parameters;\n\n\t\t\t\tmoveCall._argumentTypes = params;\n\t\t\t}),\n\t\t);\n\t}\n\n\tcommands.forEach((command) => {\n\t\tif (!command.MoveCall) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst moveCall = command.MoveCall;\n\t\tconst fnName = `${moveCall.package}::${moveCall.module}::${moveCall.function}`;\n\t\tconst params = moveCall._argumentTypes;\n\n\t\tif (!params) {\n\t\t\treturn;\n\t\t}\n\n\t\tif (params.length !== command.MoveCall.arguments.length) {\n\t\t\tthrow new Error(`Incorrect number of arguments for ${fnName}`);\n\t\t}\n\n\t\tparams.forEach((param, i) => {\n\t\t\tconst arg = moveCall.arguments[i];\n\t\t\tif (arg.$kind !== 'Input') return;\n\t\t\tconst input = inputs[arg.Input];\n\n\t\t\t// Skip if the input is already resolved\n\t\t\tif (!input.UnresolvedPure && !input.UnresolvedObject) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst inputValue = input.UnresolvedPure?.value ?? input.UnresolvedObject?.objectId!;\n\n\t\t\tconst schema = getPureBcsSchema(param.body);\n\t\t\tif (schema) {\n\t\t\t\targ.type = 'pure';\n\t\t\t\tinputs[inputs.indexOf(input)] = Inputs.Pure(schema.serialize(inputValue));\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tif (typeof inputValue !== 'string') {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Expect the argument to be an object id string, got ${JSON.stringify(\n\t\t\t\t\t\tinputValue,\n\t\t\t\t\t\tnull,\n\t\t\t\t\t\t2,\n\t\t\t\t\t)}`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\targ.type = 'object';\n\t\t\tconst unresolvedObject: typeof input = input.UnresolvedPure\n\t\t\t\t? {\n\t\t\t\t\t\t$kind: 'UnresolvedObject',\n\t\t\t\t\t\tUnresolvedObject: {\n\t\t\t\t\t\t\tobjectId: inputValue,\n\t\t\t\t\t\t},\n\t\t\t\t\t}\n\t\t\t\t: input;\n\n\t\t\tinputs[arg.Input] = unresolvedObject;\n\t\t});\n\t});\n}\n\nfunction validate(transactionData: TransactionDataBuilder) {\n\ttransactionData.inputs.forEach((input, index) => {\n\t\tif (input.$kind !== 'Object' && input.$kind !== 'Pure') {\n\t\t\tthrow new Error(\n\t\t\t\t`Input at index ${index} has not been resolved.  Expected a Pure or Object input, but found ${JSON.stringify(\n\t\t\t\t\tinput,\n\t\t\t\t)}`,\n\t\t\t);\n\t\t}\n\t});\n}\n\nfunction normalizeRawArgument(\n\targ: Argument,\n\tschema: BcsType<any>,\n\ttransactionData: TransactionDataBuilder,\n) {\n\tif (arg.$kind !== 'Input') {\n\t\treturn;\n\t}\n\tconst input = transactionData.inputs[arg.Input];\n\n\tif (input.$kind !== 'UnresolvedPure') {\n\t\treturn;\n\t}\n\n\ttransactionData.inputs[arg.Input] = Inputs.Pure(schema.serialize(input.UnresolvedPure.value));\n}\n\nfunction isUsedAsMutable(transactionData: TransactionDataBuilder, index: number) {\n\tlet usedAsMutable = false;\n\n\ttransactionData.getInputUses(index, (arg, tx) => {\n\t\tif (tx.MoveCall && tx.MoveCall._argumentTypes) {\n\t\t\tconst argIndex = tx.MoveCall.arguments.indexOf(arg);\n\t\t\tusedAsMutable = tx.MoveCall._argumentTypes[argIndex].ref !== '&' || usedAsMutable;\n\t\t}\n\n\t\tif (tx.$kind === 'MakeMoveVec' || tx.$kind === 'MergeCoins' || tx.$kind === 'SplitCoins') {\n\t\t\tusedAsMutable = true;\n\t\t}\n\t});\n\n\treturn usedAsMutable;\n}\n\nfunction isUsedAsReceiving(transactionData: TransactionDataBuilder, index: number) {\n\tlet usedAsReceiving = false;\n\n\ttransactionData.getInputUses(index, (arg, tx) => {\n\t\tif (tx.MoveCall && tx.MoveCall._argumentTypes) {\n\t\t\tconst argIndex = tx.MoveCall.arguments.indexOf(arg);\n\t\t\tusedAsReceiving = isReceivingType(tx.MoveCall._argumentTypes[argIndex]) || usedAsReceiving;\n\t\t}\n\t});\n\n\treturn usedAsReceiving;\n}\n\nfunction isReceivingType(type: OpenMoveTypeSignature): boolean {\n\tif (typeof type.body !== 'object' || !('datatype' in type.body)) {\n\t\treturn false;\n\t}\n\n\treturn (\n\t\ttype.body.datatype.package === '0x2' &&\n\t\ttype.body.datatype.module === 'transfer' &&\n\t\ttype.body.datatype.type === 'Receiving'\n\t);\n}\n\nexport function getClient(options: BuildTransactionOptions): SuiClient {\n\tif (!options.client) {\n\t\tthrow new Error(\n\t\t\t`No sui client passed to Transaction#build, but transaction data was not sufficient to build offline.`,\n\t\t);\n\t}\n\n\treturn options.client;\n}\n\nfunction chunk<T>(arr: T[], size: number): T[][] {\n\treturn Array.from({ length: Math.ceil(arr.length / size) }, (_, i) =>\n\t\tarr.slice(i * size, i * size + size),\n\t);\n}\n"], "mappings": "AAGA,SAAS,aAAa;AAGtB,SAAS,WAAW;AAEpB,SAAS,qBAAqB,sBAAsB,oBAAoB;AACxE,SAAS,iBAAiB;AAE1B,SAAS,cAAc;AACvB,SAAS,kBAAkB,aAAa,yCAAyC;AAIjF,MAAM,wBAAwB;AAG9B,MAAM,oBAAoB;AAC1B,MAAM,UAAU;AAiBhB,eAAsB,uBACrB,iBACA,SACA,MACC;AACD,QAAM,gBAAgB,iBAAiB,OAAO;AAC9C,QAAM,wBAAwB,iBAAiB,OAAO;AAEtD,MAAI,CAAC,QAAQ,qBAAqB;AACjC,UAAM,YAAY,iBAAiB,OAAO;AAC1C,UAAM,aAAa,iBAAiB,OAAO;AAC3C,UAAM,cAAc,iBAAiB,OAAO;AAAA,EAC7C;AACA,QAAM,SAAS,eAAe;AAC9B,SAAO,MAAM,KAAK;AACnB;AAEA,eAAe,YACd,iBACA,SACC;AACD,MAAI,CAAC,gBAAgB,UAAU,OAAO;AACrC,oBAAgB,UAAU,QAAQ,OAAO,MAAM,UAAU,OAAO,EAAE,qBAAqB,CAAC;AAAA,EACzF;AACD;AAEA,eAAe,aACd,iBACA,SACC;AACD,MAAI,gBAAgB,UAAU,QAAQ;AACrC;AAAA,EACD;AAEA,QAAM,eAAe,MAAM,UAAU,OAAO,EAAE,uBAAuB;AAAA,IACpE,kBAAkB,gBAAgB,MAAM;AAAA,MACvC,WAAW;AAAA,QACV,SAAS;AAAA,UACR,QAAQ,OAAO,OAAO;AAAA,UACtB,SAAS,CAAC;AAAA,QACX;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AAED,MAAI,aAAa,QAAQ,OAAO,WAAW,WAAW;AACrD,UAAM,IAAI;AAAA,MACT,+DAA+D,aAAa,QAAQ,OAAO,KAAK;AAAA,MAChG,EAAE,OAAO,aAAa;AAAA,IACvB;AAAA,EACD;AAEA,QAAM,eAAe,oBAAoB,OAAO,gBAAgB,UAAU,SAAS,EAAE;AAErF,QAAM,kCACL,OAAO,aAAa,QAAQ,QAAQ,eAAe,IAAI;AAExD,QAAM,YACL,kCACA,OAAO,aAAa,QAAQ,QAAQ,WAAW,IAC/C,OAAO,aAAa,QAAQ,QAAQ,aAAa;AAElD,kBAAgB,UAAU,SAAS;AAAA,IAClC,YAAY,kCAAkC,YAAY;AAAA,EAC3D;AACD;AAGA,eAAe,cACd,iBACA,SACC;AACD,MAAI,CAAC,gBAAgB,UAAU,SAAS;AACvC,UAAM,QAAQ,MAAM,UAAU,OAAO,EAAE,SAAS;AAAA,MAC/C,OAAO,gBAAgB,UAAU,SAAS,gBAAgB;AAAA,MAC1D,UAAU;AAAA,IACX,CAAC;AAED,UAAM,eAAe,MAAM,KAEzB,OAAO,CAAC,SAAS;AACjB,YAAM,gBAAgB,gBAAgB,OAAO,KAAK,CAAC,UAAU;AAC5D,YAAI,MAAM,QAAQ,kBAAkB;AACnC,iBAAO,KAAK,iBAAiB,MAAM,OAAO,iBAAiB;AAAA,QAC5D;AAEA,eAAO;AAAA,MACR,CAAC;AAED,aAAO,CAAC;AAAA,IACT,CAAC,EACA,IAAI,CAAC,UAAU;AAAA,MACf,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,SAAS,KAAK;AAAA,IACf,EAAE;AAEH,QAAI,CAAC,aAAa,QAAQ;AACzB,YAAM,IAAI,MAAM,+CAA+C;AAAA,IAChE;AAEA,oBAAgB,UAAU,UAAU,aAAa,IAAI,CAAC,YAAY,MAAM,WAAW,OAAO,CAAC;AAAA,EAC5F;AACD;AAEA,eAAe,wBACd,iBACA,SACC;AAGD,QAAM,mBAAmB,gBAAgB,OAAO,OAAO,CAAC,UAAU;AACjE,WACC,MAAM,oBACN,EAAE,MAAM,iBAAiB,WAAW,MAAM,kBAAkB;AAAA,EAE9D,CAAC;AAED,QAAM,aAAa;AAAA,IAClB,GAAG,IAAI;AAAA,MACN,iBAAiB,IAAI,CAAC,UAAU,qBAAqB,MAAM,iBAAiB,QAAQ,CAAC;AAAA,IACtF;AAAA,EACD;AAEA,QAAM,eAAe,WAAW,SAAS,MAAM,YAAY,qBAAqB,IAAI,CAAC;AACrF,QAAM,YACL,MAAM,QAAQ;AAAA,IACb,aAAa;AAAA,MAAI,CAACA,WACjB,UAAU,OAAO,EAAE,gBAAgB;AAAA,QAClC,KAAKA;AAAA,QACL,SAAS,EAAE,WAAW,KAAK;AAAA,MAC5B,CAAC;AAAA,IACF;AAAA,EACD,GACC,KAAK;AAEP,QAAM,gBAAgB,IAAI;AAAA,IACzB,WAAW,IAAI,CAAC,IAAI,UAAU;AAC7B,aAAO,CAAC,IAAI,SAAS,KAAK,CAAC;AAAA,IAC5B,CAAC;AAAA,EACF;AAEA,QAAM,iBAAiB,MAAM,KAAK,aAAa,EAC7C,OAAO,CAAC,CAAC,GAAG,GAAG,MAAM,IAAI,KAAK,EAC9B,IAAI,CAAC,CAAC,GAAG,GAAG,MAAM,KAAK,UAAU,IAAI,KAAK,CAAC;AAE7C,MAAI,eAAe,QAAQ;AAC1B,UAAM,IAAI,MAAM,4CAA4C,eAAe,KAAK,IAAI,CAAC,EAAE;AAAA,EACxF;AAEA,QAAM,UAAU,SAAS,IAAI,CAAC,WAAW;AACxC,QAAI,OAAO,SAAS,CAAC,OAAO,MAAM;AACjC,YAAM,IAAI,MAAM,2BAA2B,OAAO,KAAK,EAAE;AAAA,IAC1D;AACA,UAAM,QAAQ,OAAO,KAAK;AAC1B,UAAM,uBACL,SAAS,OAAO,UAAU,YAAY,YAAY,QAC/C,MAAM,OAAO,yBACb;AAEJ,WAAO;AAAA,MACN,UAAU,OAAO,KAAK;AAAA,MACtB,QAAQ,OAAO,KAAK;AAAA,MACpB,SAAS,OAAO,KAAK;AAAA,MACrB;AAAA,IACD;AAAA,EACD,CAAC;AAED,QAAM,cAAc,IAAI;AAAA,IACvB,WAAW,IAAI,CAAC,IAAI,UAAU;AAC7B,aAAO,CAAC,IAAI,QAAQ,KAAK,CAAC;AAAA,IAC3B,CAAC;AAAA,EACF;AAEA,aAAW,CAAC,OAAO,KAAK,KAAK,gBAAgB,OAAO,QAAQ,GAAG;AAC9D,QAAI,CAAC,MAAM,kBAAkB;AAC5B;AAAA,IACD;AAEA,QAAI;AACJ,UAAM,KAAK,oBAAoB,MAAM,iBAAiB,QAAQ;AAC9D,UAAM,SAAS,YAAY,IAAI,EAAE;AAEjC,QAAI,MAAM,iBAAiB,wBAAwB,QAAQ,sBAAsB;AAChF,gBAAU,OAAO,gBAAgB;AAAA,QAChC,UAAU;AAAA,QACV,sBACC,MAAM,iBAAiB,wBAAwB,QAAQ;AAAA,QACxD,SAAS,gBAAgB,iBAAiB,KAAK;AAAA,MAChD,CAAC;AAAA,IACF,WAAW,kBAAkB,iBAAiB,KAAK,GAAG;AACrD,gBAAU,OAAO;AAAA,QAChB;AAAA,UACC,UAAU;AAAA,UACV,QAAQ,MAAM,iBAAiB,UAAU,QAAQ;AAAA,UACjD,SAAS,MAAM,iBAAiB,WAAW,QAAQ;AAAA,QACpD;AAAA,MACD;AAAA,IACD;AAEA,oBAAgB,OAAO,gBAAgB,OAAO,QAAQ,KAAK,CAAC,IAC3D,WACA,OAAO,UAAU;AAAA,MAChB,UAAU;AAAA,MACV,QAAQ,MAAM,iBAAiB,UAAU,QAAQ;AAAA,MACjD,SAAS,MAAM,iBAAiB,WAAW,QAAQ;AAAA,IACpD,CAAC;AAAA,EACH;AACD;AAEA,eAAe,gBACd,iBACA,SACC;AACD,QAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,QAAM,qBAA4E,CAAC;AACnF,QAAM,yBAAyB,oBAAI,IAAY;AAE/C,WAAS,QAAQ,CAAC,YAAY;AAE7B,QAAI,QAAQ,UAAU;AAMrB,UAAI,QAAQ,SAAS,gBAAgB;AACpC;AAAA,MACD;AAEA,YAAMC,UAAS,QAAQ,SAAS,UAAU,IAAI,CAAC,QAAQ;AACtD,YAAI,IAAI,UAAU,SAAS;AAC1B,iBAAO,gBAAgB,OAAO,IAAI,KAAK;AAAA,QACxC;AACA,eAAO;AAAA,MACR,CAAC;AACD,YAAM,kBAAkBA,QAAO;AAAA,QAC9B,CAAC,UAAU,OAAO,kBAAkB,OAAO;AAAA,MAC5C;AAEA,UAAI,iBAAiB;AACpB,cAAM,eAAe,GAAG,QAAQ,SAAS,OAAO,KAAK,QAAQ,SAAS,MAAM,KAAK,QAAQ,SAAS,QAAQ;AAC1G,+BAAuB,IAAI,YAAY;AACvC,2BAAmB,KAAK,QAAQ,QAAQ;AAAA,MACzC;AAAA,IACD;AAIA,YAAQ,QAAQ,OAAO;AAAA,MACtB,KAAK;AACJ,gBAAQ,WAAW,QAAQ,QAAQ,CAAC,WAAW;AAC9C,+BAAqB,QAAQ,IAAI,KAAK,eAAe;AAAA,QACtD,CAAC;AACD;AAAA,MACD,KAAK;AACJ,6BAAqB,QAAQ,gBAAgB,SAAS,IAAI,SAAS,eAAe;AAClF;AAAA,IACF;AAAA,EACD,CAAC;AAED,QAAM,yBAAyB,oBAAI,IAAqC;AACxE,MAAI,uBAAuB,OAAO,GAAG;AACpC,UAAM,SAAS,UAAU,OAAO;AAChC,UAAM,QAAQ;AAAA,MACb,CAAC,GAAG,sBAAsB,EAAE,IAAI,OAAO,iBAAiB;AACvD,cAAM,CAAC,WAAW,UAAU,UAAU,IAAI,aAAa,MAAM,IAAI;AACjE,cAAM,MAAM,MAAM,OAAO,0BAA0B;AAAA,UAClD,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,QACX,CAAC;AAED,+BAAuB;AAAA,UACtB;AAAA,UACA,IAAI,WAAW,IAAI,CAAC,UAAU,kCAAkC,KAAK,CAAC;AAAA,QACvE;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAEA,MAAI,mBAAmB,QAAQ;AAC9B,UAAM,QAAQ;AAAA,MACb,mBAAmB,IAAI,OAAO,aAAa;AAC1C,cAAM,aAAa,uBAAuB;AAAA,UACzC,GAAG,SAAS,OAAO,KAAK,SAAS,MAAM,KAAK,SAAS,QAAQ;AAAA,QAC9D;AAEA,YAAI,CAAC,YAAY;AAChB;AAAA,QACD;AAKA,cAAM,eAAe,WAAW,SAAS,KAAK,YAAY,WAAW,GAAG,EAAE,CAAE;AAC5E,cAAM,SAAS,eAAe,WAAW,MAAM,GAAG,WAAW,SAAS,CAAC,IAAI;AAE3E,iBAAS,iBAAiB;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA,EACD;AAEA,WAAS,QAAQ,CAAC,YAAY;AAC7B,QAAI,CAAC,QAAQ,UAAU;AACtB;AAAA,IACD;AAEA,UAAM,WAAW,QAAQ;AACzB,UAAM,SAAS,GAAG,SAAS,OAAO,KAAK,SAAS,MAAM,KAAK,SAAS,QAAQ;AAC5E,UAAM,SAAS,SAAS;AAExB,QAAI,CAAC,QAAQ;AACZ;AAAA,IACD;AAEA,QAAI,OAAO,WAAW,QAAQ,SAAS,UAAU,QAAQ;AACxD,YAAM,IAAI,MAAM,qCAAqC,MAAM,EAAE;AAAA,IAC9D;AAEA,WAAO,QAAQ,CAAC,OAAO,MAAM;AAC5B,YAAM,MAAM,SAAS,UAAU,CAAC;AAChC,UAAI,IAAI,UAAU,QAAS;AAC3B,YAAM,QAAQ,OAAO,IAAI,KAAK;AAG9B,UAAI,CAAC,MAAM,kBAAkB,CAAC,MAAM,kBAAkB;AACrD;AAAA,MACD;AAEA,YAAM,aAAa,MAAM,gBAAgB,SAAS,MAAM,kBAAkB;AAE1E,YAAM,SAAS,iBAAiB,MAAM,IAAI;AAC1C,UAAI,QAAQ;AACX,YAAI,OAAO;AACX,eAAO,OAAO,QAAQ,KAAK,CAAC,IAAI,OAAO,KAAK,OAAO,UAAU,UAAU,CAAC;AACxE;AAAA,MACD;AAEA,UAAI,OAAO,eAAe,UAAU;AACnC,cAAM,IAAI;AAAA,UACT,sDAAsD,KAAK;AAAA,YAC1D;AAAA,YACA;AAAA,YACA;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AAEA,UAAI,OAAO;AACX,YAAM,mBAAiC,MAAM,iBAC1C;AAAA,QACA,OAAO;AAAA,QACP,kBAAkB;AAAA,UACjB,UAAU;AAAA,QACX;AAAA,MACD,IACC;AAEH,aAAO,IAAI,KAAK,IAAI;AAAA,IACrB,CAAC;AAAA,EACF,CAAC;AACF;AAEA,SAAS,SAAS,iBAAyC;AAC1D,kBAAgB,OAAO,QAAQ,CAAC,OAAO,UAAU;AAChD,QAAI,MAAM,UAAU,YAAY,MAAM,UAAU,QAAQ;AACvD,YAAM,IAAI;AAAA,QACT,kBAAkB,KAAK,uEAAuE,KAAK;AAAA,UAClG;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD,CAAC;AACF;AAEA,SAAS,qBACR,KACA,QACA,iBACC;AACD,MAAI,IAAI,UAAU,SAAS;AAC1B;AAAA,EACD;AACA,QAAM,QAAQ,gBAAgB,OAAO,IAAI,KAAK;AAE9C,MAAI,MAAM,UAAU,kBAAkB;AACrC;AAAA,EACD;AAEA,kBAAgB,OAAO,IAAI,KAAK,IAAI,OAAO,KAAK,OAAO,UAAU,MAAM,eAAe,KAAK,CAAC;AAC7F;AAEA,SAAS,gBAAgB,iBAAyC,OAAe;AAChF,MAAI,gBAAgB;AAEpB,kBAAgB,aAAa,OAAO,CAAC,KAAK,OAAO;AAChD,QAAI,GAAG,YAAY,GAAG,SAAS,gBAAgB;AAC9C,YAAM,WAAW,GAAG,SAAS,UAAU,QAAQ,GAAG;AAClD,sBAAgB,GAAG,SAAS,eAAe,QAAQ,EAAE,QAAQ,OAAO;AAAA,IACrE;AAEA,QAAI,GAAG,UAAU,iBAAiB,GAAG,UAAU,gBAAgB,GAAG,UAAU,cAAc;AACzF,sBAAgB;AAAA,IACjB;AAAA,EACD,CAAC;AAED,SAAO;AACR;AAEA,SAAS,kBAAkB,iBAAyC,OAAe;AAClF,MAAI,kBAAkB;AAEtB,kBAAgB,aAAa,OAAO,CAAC,KAAK,OAAO;AAChD,QAAI,GAAG,YAAY,GAAG,SAAS,gBAAgB;AAC9C,YAAM,WAAW,GAAG,SAAS,UAAU,QAAQ,GAAG;AAClD,wBAAkB,gBAAgB,GAAG,SAAS,eAAe,QAAQ,CAAC,KAAK;AAAA,IAC5E;AAAA,EACD,CAAC;AAED,SAAO;AACR;AAEA,SAAS,gBAAgB,MAAsC;AAC9D,MAAI,OAAO,KAAK,SAAS,YAAY,EAAE,cAAc,KAAK,OAAO;AAChE,WAAO;AAAA,EACR;AAEA,SACC,KAAK,KAAK,SAAS,YAAY,SAC/B,KAAK,KAAK,SAAS,WAAW,cAC9B,KAAK,KAAK,SAAS,SAAS;AAE9B;AAEO,SAAS,UAAU,SAA6C;AACtE,MAAI,CAAC,QAAQ,QAAQ;AACpB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO,QAAQ;AAChB;AAEA,SAAS,MAAS,KAAU,MAAqB;AAChD,SAAO,MAAM;AAAA,IAAK,EAAE,QAAQ,KAAK,KAAK,IAAI,SAAS,IAAI,EAAE;AAAA,IAAG,CAAC,GAAG,MAC/D,IAAI,MAAM,IAAI,MAAM,IAAI,OAAO,IAAI;AAAA,EACpC;AACD;", "names": ["chunk", "inputs"]}