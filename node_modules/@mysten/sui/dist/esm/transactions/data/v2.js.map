{"version": 3, "sources": ["../../../../src/transactions/data/v2.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { EnumInputShape } from '@mysten/bcs';\nimport type { GenericSchema, InferInput, InferOutput } from 'valibot';\nimport {\n\tarray,\n\tboolean,\n\tinteger,\n\tliteral,\n\tnullable,\n\tnullish,\n\tnumber,\n\tobject,\n\toptional,\n\tpipe,\n\trecord,\n\tstring,\n\ttuple,\n\tunion,\n\tunknown,\n} from 'valibot';\n\nimport { BCSBytes, JsonU64, ObjectID, ObjectRef, SuiAddress } from './internal.js';\n\ntype Merge<T> = T extends object ? { [K in keyof T]: T[K] } : never;\n\nfunction enumUnion<T extends Record<string, GenericSchema<any>>>(options: T) {\n\treturn union(\n\t\tObject.entries(options).map(([key, value]) => object({ [key]: value })),\n\t) as GenericSchema<\n\t\tEnumInputShape<\n\t\t\tMerge<{\n\t\t\t\t[K in keyof T]: InferInput<T[K]>;\n\t\t\t}>\n\t\t>\n\t>;\n}\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L690-L702\nconst Argument = enumUnion({\n\tGasCoin: literal(true),\n\tInput: pipe(number(), integer()),\n\tResult: pipe(number(), integer()),\n\tNestedResult: tuple([pipe(number(), integer()), pipe(number(), integer())]),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L1387-L1392\nconst GasData = object({\n\tbudget: nullable(JsonU64),\n\tprice: nullable(JsonU64),\n\towner: nullable(SuiAddress),\n\tpayment: nullable(array(ObjectRef)),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L707-L718\nconst ProgrammableMoveCall = object({\n\tpackage: ObjectID,\n\tmodule: string(),\n\tfunction: string(),\n\t// snake case in rust\n\ttypeArguments: array(string()),\n\targuments: array(Argument),\n});\n\nconst $Intent = object({\n\tname: string(),\n\tinputs: record(string(), union([Argument, array(Argument)])),\n\tdata: record(string(), unknown()),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L657-L685\nconst Command = enumUnion({\n\tMoveCall: ProgrammableMoveCall,\n\tTransferObjects: object({\n\t\tobjects: array(Argument),\n\t\taddress: Argument,\n\t}),\n\tSplitCoins: object({\n\t\tcoin: Argument,\n\t\tamounts: array(Argument),\n\t}),\n\tMergeCoins: object({\n\t\tdestination: Argument,\n\t\tsources: array(Argument),\n\t}),\n\tPublish: object({\n\t\tmodules: array(BCSBytes),\n\t\tdependencies: array(ObjectID),\n\t}),\n\tMakeMoveVec: object({\n\t\ttype: nullable(string()),\n\t\telements: array(Argument),\n\t}),\n\tUpgrade: object({\n\t\tmodules: array(BCSBytes),\n\t\tdependencies: array(ObjectID),\n\t\tpackage: ObjectID,\n\t\tticket: Argument,\n\t}),\n\t$Intent,\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L102-L114\nconst ObjectArg = enumUnion({\n\tImmOrOwnedObject: ObjectRef,\n\tSharedObject: object({\n\t\tobjectId: ObjectID,\n\t\t// snake case in rust\n\t\tinitialSharedVersion: JsonU64,\n\t\tmutable: boolean(),\n\t}),\n\tReceiving: ObjectRef,\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L75-L80\nconst CallArg = enumUnion({\n\tObject: ObjectArg,\n\tPure: object({\n\t\tbytes: BCSBytes,\n\t}),\n\tUnresolvedPure: object({\n\t\tvalue: unknown(),\n\t}),\n\tUnresolvedObject: object({\n\t\tobjectId: ObjectID,\n\t\tversion: optional(nullable(JsonU64)),\n\t\tdigest: optional(nullable(string())),\n\t\tinitialSharedVersion: optional(nullable(JsonU64)),\n\t}),\n});\n\nconst TransactionExpiration = enumUnion({\n\tNone: literal(true),\n\tEpoch: JsonU64,\n});\n\nexport const SerializedTransactionDataV2 = object({\n\tversion: literal(2),\n\tsender: nullish(SuiAddress),\n\texpiration: nullish(TransactionExpiration),\n\tgasData: GasData,\n\tinputs: array(CallArg),\n\tcommands: array(Command),\n});\n\nexport type SerializedTransactionDataV2 = InferOutput<typeof SerializedTransactionDataV2>;\n"], "mappings": "AAKA;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AAEP,SAAS,UAAU,SAAS,UAAU,WAAW,kBAAkB;AAInE,SAAS,UAAwD,SAAY;AAC5E,SAAO;AAAA,IACN,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,OAAO,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;AAAA,EACvE;AAOD;AAGA,MAAM,WAAW,UAAU;AAAA,EAC1B,SAAS,QAAQ,IAAI;AAAA,EACrB,OAAO,KAAK,OAAO,GAAG,QAAQ,CAAC;AAAA,EAC/B,QAAQ,KAAK,OAAO,GAAG,QAAQ,CAAC;AAAA,EAChC,cAAc,MAAM,CAAC,KAAK,OAAO,GAAG,QAAQ,CAAC,GAAG,KAAK,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC3E,CAAC;AAGD,MAAM,UAAU,OAAO;AAAA,EACtB,QAAQ,SAAS,OAAO;AAAA,EACxB,OAAO,SAAS,OAAO;AAAA,EACvB,OAAO,SAAS,UAAU;AAAA,EAC1B,SAAS,SAAS,MAAM,SAAS,CAAC;AACnC,CAAC;AAGD,MAAM,uBAAuB,OAAO;AAAA,EACnC,SAAS;AAAA,EACT,QAAQ,OAAO;AAAA,EACf,UAAU,OAAO;AAAA;AAAA,EAEjB,eAAe,MAAM,OAAO,CAAC;AAAA,EAC7B,WAAW,MAAM,QAAQ;AAC1B,CAAC;AAED,MAAM,UAAU,OAAO;AAAA,EACtB,MAAM,OAAO;AAAA,EACb,QAAQ,OAAO,OAAO,GAAG,MAAM,CAAC,UAAU,MAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3D,MAAM,OAAO,OAAO,GAAG,QAAQ,CAAC;AACjC,CAAC;AAGD,MAAM,UAAU,UAAU;AAAA,EACzB,UAAU;AAAA,EACV,iBAAiB,OAAO;AAAA,IACvB,SAAS,MAAM,QAAQ;AAAA,IACvB,SAAS;AAAA,EACV,CAAC;AAAA,EACD,YAAY,OAAO;AAAA,IAClB,MAAM;AAAA,IACN,SAAS,MAAM,QAAQ;AAAA,EACxB,CAAC;AAAA,EACD,YAAY,OAAO;AAAA,IAClB,aAAa;AAAA,IACb,SAAS,MAAM,QAAQ;AAAA,EACxB,CAAC;AAAA,EACD,SAAS,OAAO;AAAA,IACf,SAAS,MAAM,QAAQ;AAAA,IACvB,cAAc,MAAM,QAAQ;AAAA,EAC7B,CAAC;AAAA,EACD,aAAa,OAAO;AAAA,IACnB,MAAM,SAAS,OAAO,CAAC;AAAA,IACvB,UAAU,MAAM,QAAQ;AAAA,EACzB,CAAC;AAAA,EACD,SAAS,OAAO;AAAA,IACf,SAAS,MAAM,QAAQ;AAAA,IACvB,cAAc,MAAM,QAAQ;AAAA,IAC5B,SAAS;AAAA,IACT,QAAQ;AAAA,EACT,CAAC;AAAA,EACD;AACD,CAAC;AAGD,MAAM,YAAY,UAAU;AAAA,EAC3B,kBAAkB;AAAA,EAClB,cAAc,OAAO;AAAA,IACpB,UAAU;AAAA;AAAA,IAEV,sBAAsB;AAAA,IACtB,SAAS,QAAQ;AAAA,EAClB,CAAC;AAAA,EACD,WAAW;AACZ,CAAC;AAGD,MAAM,UAAU,UAAU;AAAA,EACzB,QAAQ;AAAA,EACR,MAAM,OAAO;AAAA,IACZ,OAAO;AAAA,EACR,CAAC;AAAA,EACD,gBAAgB,OAAO;AAAA,IACtB,OAAO,QAAQ;AAAA,EAChB,CAAC;AAAA,EACD,kBAAkB,OAAO;AAAA,IACxB,UAAU;AAAA,IACV,SAAS,SAAS,SAAS,OAAO,CAAC;AAAA,IACnC,QAAQ,SAAS,SAAS,OAAO,CAAC,CAAC;AAAA,IACnC,sBAAsB,SAAS,SAAS,OAAO,CAAC;AAAA,EACjD,CAAC;AACF,CAAC;AAED,MAAM,wBAAwB,UAAU;AAAA,EACvC,MAAM,QAAQ,IAAI;AAAA,EAClB,OAAO;AACR,CAAC;AAEM,MAAM,8BAA8B,OAAO;AAAA,EACjD,SAAS,QAAQ,CAAC;AAAA,EAClB,QAAQ,QAAQ,UAAU;AAAA,EAC1B,YAAY,QAAQ,qBAAqB;AAAA,EACzC,SAAS;AAAA,EACT,QAAQ,MAAM,OAAO;AAAA,EACrB,UAAU,MAAM,OAAO;AACxB,CAAC;", "names": []}