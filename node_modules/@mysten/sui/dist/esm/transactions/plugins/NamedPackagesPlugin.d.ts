import type { SuiGraphQLClient } from '../../graphql/client.js';
import type { BuildTransactionOptions } from '../json-rpc-resolver.js';
import type { TransactionDataBuilder } from '../TransactionData.js';
import type { NamedPackagesPluginCache } from './utils.js';
export type NamedPackagesPluginOptions = {
    /**
     * The SuiGraphQLClient to use for resolving names.
     * The endpoint should be the GraphQL endpoint of the network you are targeting.
     * For non-mainnet networks, if the plugin doesn't work as expected, you need to validate that the
     * RPC provider has support for the `packageByName` and `typeByName` queries (using external resolver).
     */
    suiGraphQLClient: SuiGraphQLClient;
    /**
     * The number of names to resolve in each batch request.
     * Needs to be calculated based on the GraphQL query limits.
     */
    pageSize?: number;
    /**
     * Local overrides for the resolution plugin. Pass this to pre-populate
     * the cache with known packages / types (especially useful for local or CI testing).
     *
     * 	Expected format example:
     *  {
     * 		packages: {
     * 			'@framework/std': '0x1234',
     * 		},
     * 		types: {
     * 			'@framework/std::string::String': '0x1234::string::String',
     * 		},
     * 	}
     *
     */
    overrides?: NamedPackagesPluginCache;
};
/**
 * @experimental This plugin is in experimental phase and there might be breaking changes in the future
 *
 * Adds named resolution so that you can use .move names in your transactions.
 * e.g. `@org/app::type::Type` will be resolved to `0x1234::type::Type`.
 * This plugin will resolve all names & types in the transaction block.
 *
 * To install this plugin globally in your app, use:
 * ```
 * Transaction.registerGlobalSerializationPlugin("namedPackagesPlugin", namedPackagesPlugin({ suiGraphQLClient }));
 * ```
 *
 * You can also define `overrides` to pre-populate name resolutions locally (removes the GraphQL request).
 */
export declare const namedPackagesPlugin: ({ suiGraphQLClient, pageSize, overrides, }: NamedPackagesPluginOptions) => (transactionData: TransactionDataBuilder, _buildOptions: BuildTransactionOptions, next: () => Promise<void>) => Promise<void>;
