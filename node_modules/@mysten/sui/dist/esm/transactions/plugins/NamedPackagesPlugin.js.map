{"version": 3, "sources": ["../../../../src/transactions/plugins/NamedPackagesPlugin.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiGraphQLClient } from '../../graphql/client.js';\nimport type { BuildTransactionOptions } from '../json-rpc-resolver.js';\nimport type { TransactionDataBuilder } from '../TransactionData.js';\nimport type { NamedPackagesPluginCache, NameResolutionRequest } from './utils.js';\nimport { findTransactionBlockNames, listToRequests, replaceNames } from './utils.js';\n\nexport type NamedPackagesPluginOptions = {\n\t/**\n\t * The SuiGraphQLClient to use for resolving names.\n\t * The endpoint should be the GraphQL endpoint of the network you are targeting.\n\t * For non-mainnet networks, if the plugin doesn't work as expected, you need to validate that the\n\t * RPC provider has support for the `packageByName` and `typeByName` queries (using external resolver).\n\t */\n\tsuiGraphQLClient: SuiGraphQLClient;\n\t/**\n\t * The number of names to resolve in each batch request.\n\t * Needs to be calculated based on the GraphQL query limits.\n\t */\n\tpageSize?: number;\n\t/**\n\t * Local overrides for the resolution plugin. Pass this to pre-populate\n\t * the cache with known packages / types (especially useful for local or CI testing).\n\t *\n\t * \tExpected format example:\n\t *  {\n\t * \t\tpackages: {\n\t * \t\t\t'@framework/std': '0x1234',\n\t * \t\t},\n\t * \t\ttypes: {\n\t * \t\t\t'@framework/std::string::String': '0x1234::string::String',\n\t * \t\t},\n\t * \t}\n\t *\n\t */\n\toverrides?: NamedPackagesPluginCache;\n};\n\n/**\n * @experimental This plugin is in experimental phase and there might be breaking changes in the future\n *\n * Adds named resolution so that you can use .move names in your transactions.\n * e.g. `@org/app::type::Type` will be resolved to `0x1234::type::Type`.\n * This plugin will resolve all names & types in the transaction block.\n *\n * To install this plugin globally in your app, use:\n * ```\n * Transaction.registerGlobalSerializationPlugin(\"namedPackagesPlugin\", namedPackagesPlugin({ suiGraphQLClient }));\n * ```\n *\n * You can also define `overrides` to pre-populate name resolutions locally (removes the GraphQL request).\n */\nexport const namedPackagesPlugin = ({\n\tsuiGraphQLClient,\n\tpageSize = 10,\n\toverrides = { packages: {}, types: {} },\n}: NamedPackagesPluginOptions) => {\n\tconst cache = {\n\t\tpackages: { ...overrides.packages },\n\t\ttypes: { ...overrides.types },\n\t};\n\n\treturn async (\n\t\ttransactionData: TransactionDataBuilder,\n\t\t_buildOptions: BuildTransactionOptions,\n\t\tnext: () => Promise<void>,\n\t) => {\n\t\tconst names = findTransactionBlockNames(transactionData);\n\t\tconst batches = listToRequests(\n\t\t\t{\n\t\t\t\tpackages: names.packages.filter((x) => !cache.packages[x]),\n\t\t\t\ttypes: names.types.filter((x) => !cache.types[x]),\n\t\t\t},\n\t\t\tpageSize,\n\t\t);\n\n\t\t// now we need to bulk resolve all the names + types, and replace them in the transaction data.\n\t\t(await Promise.all(batches.map((batch) => query(suiGraphQLClient, batch)))).forEach((res) => {\n\t\t\tObject.assign(cache.types, res.types);\n\t\t\tObject.assign(cache.packages, res.packages);\n\t\t});\n\n\t\treplaceNames(transactionData, cache);\n\n\t\tawait next();\n\t};\n\n\tasync function query(client: SuiGraphQLClient, requests: NameResolutionRequest[]) {\n\t\tconst results: NamedPackagesPluginCache = { packages: {}, types: {} };\n\t\t// avoid making a request if there are no names to resolve.\n\t\tif (requests.length === 0) return results;\n\n\t\t// Create multiple queries for each name / type we need to resolve\n\t\t// TODO: Replace with bulk APIs when available.\n\t\tconst gqlQuery = `{\n        ${requests.map((req) => {\n\t\t\t\t\tconst request = req.type === 'package' ? 'packageByName' : 'typeByName';\n\t\t\t\t\tconst fields = req.type === 'package' ? 'address' : 'repr';\n\n\t\t\t\t\treturn `${gqlQueryKey(req.id)}: ${request}(name:\"${req.name}\") {\n                    ${fields}\n                }`;\n\t\t\t\t})}\n    }`;\n\n\t\tconst result = await client.query({\n\t\t\tquery: gqlQuery,\n\t\t\tvariables: undefined,\n\t\t});\n\n\t\tif (result.errors) throw new Error(JSON.stringify({ query: gqlQuery, errors: result.errors }));\n\n\t\t// Parse the results and create a map of `<name|type> -> <address|repr>`\n\t\tfor (const req of requests) {\n\t\t\tconst key = gqlQueryKey(req.id);\n\t\t\tif (!result.data || !result.data[key]) throw new Error(`No result found for: ${req.name}`);\n\t\t\tconst data = result.data[key] as { address?: string; repr?: string };\n\n\t\t\tif (req.type === 'package') results.packages[req.name] = data.address!;\n\t\t\tif (req.type === 'moveType') results.types[req.name] = data.repr!;\n\t\t}\n\n\t\treturn results;\n\t}\n};\n\nconst gqlQueryKey = (idx: number) => `key_${idx}`;\n"], "mappings": "AAOA,SAAS,2BAA2B,gBAAgB,oBAAoB;AA+CjE,MAAM,sBAAsB,CAAC;AAAA,EACnC;AAAA,EACA,WAAW;AAAA,EACX,YAAY,EAAE,UAAU,CAAC,GAAG,OAAO,CAAC,EAAE;AACvC,MAAkC;AACjC,QAAM,QAAQ;AAAA,IACb,UAAU,EAAE,GAAG,UAAU,SAAS;AAAA,IAClC,OAAO,EAAE,GAAG,UAAU,MAAM;AAAA,EAC7B;AAEA,SAAO,OACN,iBACA,eACA,SACI;AACJ,UAAM,QAAQ,0BAA0B,eAAe;AACvD,UAAM,UAAU;AAAA,MACf;AAAA,QACC,UAAU,MAAM,SAAS,OAAO,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC;AAAA,QACzD,OAAO,MAAM,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,IACD;AAGA,KAAC,MAAM,QAAQ,IAAI,QAAQ,IAAI,CAAC,UAAU,MAAM,kBAAkB,KAAK,CAAC,CAAC,GAAG,QAAQ,CAAC,QAAQ;AAC5F,aAAO,OAAO,MAAM,OAAO,IAAI,KAAK;AACpC,aAAO,OAAO,MAAM,UAAU,IAAI,QAAQ;AAAA,IAC3C,CAAC;AAED,iBAAa,iBAAiB,KAAK;AAEnC,UAAM,KAAK;AAAA,EACZ;AAEA,iBAAe,MAAM,QAA0B,UAAmC;AACjF,UAAM,UAAoC,EAAE,UAAU,CAAC,GAAG,OAAO,CAAC,EAAE;AAEpE,QAAI,SAAS,WAAW,EAAG,QAAO;AAIlC,UAAM,WAAW;AAAA,UACT,SAAS,IAAI,CAAC,QAAQ;AAC3B,YAAM,UAAU,IAAI,SAAS,YAAY,kBAAkB;AAC3D,YAAM,SAAS,IAAI,SAAS,YAAY,YAAY;AAEpD,aAAO,GAAG,YAAY,IAAI,EAAE,CAAC,KAAK,OAAO,UAAU,IAAI,IAAI;AAAA,sBAC1C,MAAM;AAAA;AAAA,IAExB,CAAC,CAAC;AAAA;AAGJ,UAAM,SAAS,MAAM,OAAO,MAAM;AAAA,MACjC,OAAO;AAAA,MACP,WAAW;AAAA,IACZ,CAAC;AAED,QAAI,OAAO,OAAQ,OAAM,IAAI,MAAM,KAAK,UAAU,EAAE,OAAO,UAAU,QAAQ,OAAO,OAAO,CAAC,CAAC;AAG7F,eAAW,OAAO,UAAU;AAC3B,YAAM,MAAM,YAAY,IAAI,EAAE;AAC9B,UAAI,CAAC,OAAO,QAAQ,CAAC,OAAO,KAAK,GAAG,EAAG,OAAM,IAAI,MAAM,wBAAwB,IAAI,IAAI,EAAE;AACzF,YAAM,OAAO,OAAO,KAAK,GAAG;AAE5B,UAAI,IAAI,SAAS,UAAW,SAAQ,SAAS,IAAI,IAAI,IAAI,KAAK;AAC9D,UAAI,IAAI,SAAS,WAAY,SAAQ,MAAM,IAAI,IAAI,IAAI,KAAK;AAAA,IAC7D;AAEA,WAAO;AAAA,EACR;AACD;AAEA,MAAM,cAAc,CAAC,QAAgB,OAAO,GAAG;", "names": []}