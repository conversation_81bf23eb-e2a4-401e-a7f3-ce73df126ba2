{"version": 3, "sources": ["../../../../src/transactions/plugins/utils.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { isValidNamedPackage, isValidNamedType } from '../../utils/move-registry.js';\nimport type { TransactionDataBuilder } from '../TransactionData.js';\n\nexport type NamedPackagesPluginCache = {\n\tpackages: Record<string, string>;\n\ttypes: Record<string, string>;\n};\n\nconst NAME_SEPARATOR = '/';\n\nexport type NameResolutionRequest = {\n\tid: number;\n\ttype: 'package' | 'moveType';\n\tname: string;\n};\n\n/**\n * Looks up all `.move` names in a transaction block.\n * Returns a list of all the names found.\n */\nexport const findTransactionBlockNames = (\n\tbuilder: TransactionDataBuilder,\n): { packages: string[]; types: string[] } => {\n\tconst packages: Set<string> = new Set();\n\tconst types: Set<string> = new Set();\n\n\tfor (const command of builder.commands) {\n\t\tif (command.MakeMoveVec?.type) {\n\t\t\tgetNamesFromTypeList([command.MakeMoveVec.type]).forEach((type) => {\n\t\t\t\ttypes.add(type);\n\t\t\t});\n\t\t\tcontinue;\n\t\t}\n\t\tif (!('MoveCall' in command)) continue;\n\t\tconst tx = command.MoveCall;\n\n\t\tif (!tx) continue;\n\n\t\tconst pkg = tx.package.split('::')[0];\n\t\tif (pkg.includes(NAME_SEPARATOR)) {\n\t\t\tif (!isValidNamedPackage(pkg)) throw new Error(`Invalid package name: ${pkg}`);\n\t\t\tpackages.add(pkg);\n\t\t}\n\n\t\tgetNamesFromTypeList(tx.typeArguments ?? []).forEach((type) => {\n\t\t\ttypes.add(type);\n\t\t});\n\t}\n\n\treturn {\n\t\tpackages: [...packages],\n\t\ttypes: [...types],\n\t};\n};\n\n/**\n * Returns a list of unique types that include a name\n * from the given list.\n *  */\nfunction getNamesFromTypeList(types: string[]) {\n\tconst names = new Set<string>();\n\tfor (const type of types) {\n\t\tif (type.includes(NAME_SEPARATOR)) {\n\t\t\tif (!isValidNamedType(type)) throw new Error(`Invalid type with names: ${type}`);\n\t\t\tnames.add(type);\n\t\t}\n\t}\n\treturn [...names];\n}\n\n/**\n * Replace all names & types in a transaction block\n * with their resolved names/types.\n */\nexport const replaceNames = (builder: TransactionDataBuilder, cache: NamedPackagesPluginCache) => {\n\tfor (const command of builder.commands) {\n\t\t// Replacements for `MakeMoveVec` commands (that can include types)\n\t\tif (command.MakeMoveVec?.type) {\n\t\t\tif (!command.MakeMoveVec.type.includes(NAME_SEPARATOR)) continue;\n\t\t\tif (!cache.types[command.MakeMoveVec.type])\n\t\t\t\tthrow new Error(`No resolution found for type: ${command.MakeMoveVec.type}`);\n\t\t\tcommand.MakeMoveVec.type = cache.types[command.MakeMoveVec.type];\n\t\t}\n\t\t// Replacements for `MoveCall` commands (that can include packages & types)\n\t\tconst tx = command.MoveCall;\n\t\tif (!tx) continue;\n\n\t\tconst nameParts = tx.package.split('::');\n\t\tconst name = nameParts[0];\n\n\t\tif (name.includes(NAME_SEPARATOR) && !cache.packages[name])\n\t\t\tthrow new Error(`No address found for package: ${name}`);\n\n\t\t// Replace package name with address.\n\t\tif (name.includes(NAME_SEPARATOR)) {\n\t\t\tnameParts[0] = cache.packages[name];\n\t\t\ttx.package = nameParts.join('::');\n\t\t}\n\n\t\tconst types = tx.typeArguments;\n\t\tif (!types) continue;\n\n\t\tfor (let i = 0; i < types.length; i++) {\n\t\t\tif (!types[i].includes(NAME_SEPARATOR)) continue;\n\n\t\t\tif (!cache.types[types[i]]) throw new Error(`No resolution found for type: ${types[i]}`);\n\t\t\ttypes[i] = cache.types[types[i]];\n\t\t}\n\n\t\ttx.typeArguments = types;\n\t}\n};\n\nexport const listToRequests = (\n\tnames: { packages: string[]; types: string[] },\n\tbatchSize: number,\n): NameResolutionRequest[][] => {\n\tconst results: NameResolutionRequest[] = [];\n\tconst uniqueNames = deduplicate(names.packages);\n\tconst uniqueTypes = deduplicate(names.types);\n\n\tfor (const [idx, name] of uniqueNames.entries()) {\n\t\tresults.push({ id: idx, type: 'package', name } as NameResolutionRequest);\n\t}\n\tfor (const [idx, type] of uniqueTypes.entries()) {\n\t\tresults.push({\n\t\t\tid: idx + uniqueNames.length,\n\t\t\ttype: 'moveType',\n\t\t\tname: type,\n\t\t} as NameResolutionRequest);\n\t}\n\n\treturn batch(results, batchSize);\n};\n\nconst deduplicate = <T>(arr: T[]): T[] => [...new Set(arr)];\n\nconst batch = <T>(arr: T[], size: number): T[][] => {\n\tconst batches = [];\n\tfor (let i = 0; i < arr.length; i += size) {\n\t\tbatches.push(arr.slice(i, i + size));\n\t}\n\treturn batches;\n};\n"], "mappings": "AAGA,SAAS,qBAAqB,wBAAwB;AAQtD,MAAM,iBAAiB;AAYhB,MAAM,4BAA4B,CACxC,YAC6C;AAC7C,QAAM,WAAwB,oBAAI,IAAI;AACtC,QAAM,QAAqB,oBAAI,IAAI;AAEnC,aAAW,WAAW,QAAQ,UAAU;AACvC,QAAI,QAAQ,aAAa,MAAM;AAC9B,2BAAqB,CAAC,QAAQ,YAAY,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAS;AAClE,cAAM,IAAI,IAAI;AAAA,MACf,CAAC;AACD;AAAA,IACD;AACA,QAAI,EAAE,cAAc,SAAU;AAC9B,UAAM,KAAK,QAAQ;AAEnB,QAAI,CAAC,GAAI;AAET,UAAM,MAAM,GAAG,QAAQ,MAAM,IAAI,EAAE,CAAC;AACpC,QAAI,IAAI,SAAS,cAAc,GAAG;AACjC,UAAI,CAAC,oBAAoB,GAAG,EAAG,OAAM,IAAI,MAAM,yBAAyB,GAAG,EAAE;AAC7E,eAAS,IAAI,GAAG;AAAA,IACjB;AAEA,yBAAqB,GAAG,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS;AAC9D,YAAM,IAAI,IAAI;AAAA,IACf,CAAC;AAAA,EACF;AAEA,SAAO;AAAA,IACN,UAAU,CAAC,GAAG,QAAQ;AAAA,IACtB,OAAO,CAAC,GAAG,KAAK;AAAA,EACjB;AACD;AAMA,SAAS,qBAAqB,OAAiB;AAC9C,QAAM,QAAQ,oBAAI,IAAY;AAC9B,aAAW,QAAQ,OAAO;AACzB,QAAI,KAAK,SAAS,cAAc,GAAG;AAClC,UAAI,CAAC,iBAAiB,IAAI,EAAG,OAAM,IAAI,MAAM,4BAA4B,IAAI,EAAE;AAC/E,YAAM,IAAI,IAAI;AAAA,IACf;AAAA,EACD;AACA,SAAO,CAAC,GAAG,KAAK;AACjB;AAMO,MAAM,eAAe,CAAC,SAAiC,UAAoC;AACjG,aAAW,WAAW,QAAQ,UAAU;AAEvC,QAAI,QAAQ,aAAa,MAAM;AAC9B,UAAI,CAAC,QAAQ,YAAY,KAAK,SAAS,cAAc,EAAG;AACxD,UAAI,CAAC,MAAM,MAAM,QAAQ,YAAY,IAAI;AACxC,cAAM,IAAI,MAAM,iCAAiC,QAAQ,YAAY,IAAI,EAAE;AAC5E,cAAQ,YAAY,OAAO,MAAM,MAAM,QAAQ,YAAY,IAAI;AAAA,IAChE;AAEA,UAAM,KAAK,QAAQ;AACnB,QAAI,CAAC,GAAI;AAET,UAAM,YAAY,GAAG,QAAQ,MAAM,IAAI;AACvC,UAAM,OAAO,UAAU,CAAC;AAExB,QAAI,KAAK,SAAS,cAAc,KAAK,CAAC,MAAM,SAAS,IAAI;AACxD,YAAM,IAAI,MAAM,iCAAiC,IAAI,EAAE;AAGxD,QAAI,KAAK,SAAS,cAAc,GAAG;AAClC,gBAAU,CAAC,IAAI,MAAM,SAAS,IAAI;AAClC,SAAG,UAAU,UAAU,KAAK,IAAI;AAAA,IACjC;AAEA,UAAM,QAAQ,GAAG;AACjB,QAAI,CAAC,MAAO;AAEZ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI,CAAC,MAAM,CAAC,EAAE,SAAS,cAAc,EAAG;AAExC,UAAI,CAAC,MAAM,MAAM,MAAM,CAAC,CAAC,EAAG,OAAM,IAAI,MAAM,iCAAiC,MAAM,CAAC,CAAC,EAAE;AACvF,YAAM,CAAC,IAAI,MAAM,MAAM,MAAM,CAAC,CAAC;AAAA,IAChC;AAEA,OAAG,gBAAgB;AAAA,EACpB;AACD;AAEO,MAAM,iBAAiB,CAC7B,OACA,cAC+B;AAC/B,QAAM,UAAmC,CAAC;AAC1C,QAAM,cAAc,YAAY,MAAM,QAAQ;AAC9C,QAAM,cAAc,YAAY,MAAM,KAAK;AAE3C,aAAW,CAAC,KAAK,IAAI,KAAK,YAAY,QAAQ,GAAG;AAChD,YAAQ,KAAK,EAAE,IAAI,KAAK,MAAM,WAAW,KAAK,CAA0B;AAAA,EACzE;AACA,aAAW,CAAC,KAAK,IAAI,KAAK,YAAY,QAAQ,GAAG;AAChD,YAAQ,KAAK;AAAA,MACZ,IAAI,MAAM,YAAY;AAAA,MACtB,MAAM;AAAA,MACN,MAAM;AAAA,IACP,CAA0B;AAAA,EAC3B;AAEA,SAAO,MAAM,SAAS,SAAS;AAChC;AAEA,MAAM,cAAc,CAAI,QAAkB,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC;AAE1D,MAAM,QAAQ,CAAI,KAAU,SAAwB;AACnD,QAAM,UAAU,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK,MAAM;AAC1C,YAAQ,KAAK,IAAI,MAAM,GAAG,IAAI,IAAI,CAAC;AAAA,EACpC;AACA,SAAO;AACR;", "names": []}