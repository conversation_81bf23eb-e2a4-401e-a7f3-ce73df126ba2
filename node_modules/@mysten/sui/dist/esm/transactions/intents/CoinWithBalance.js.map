{"version": 3, "sources": ["../../../../src/transactions/intents/CoinWithBalance.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { InferInput } from 'valibot';\nimport { bigint, object, parse, string } from 'valibot';\n\nimport { bcs } from '../../bcs/index.js';\nimport type { CoinStruct, SuiClient } from '../../client/index.js';\nimport { normalizeStructTag } from '../../utils/sui-types.js';\nimport { Commands } from '../Commands.js';\nimport type { Argument } from '../data/internal.js';\nimport { Inputs } from '../Inputs.js';\nimport type { BuildTransactionOptions } from '../json-rpc-resolver.js';\nimport { getClient } from '../json-rpc-resolver.js';\nimport type { Transaction } from '../Transaction.js';\nimport type { TransactionDataBuilder } from '../TransactionData.js';\n\nconst COIN_WITH_BALANCE = 'CoinWithBalance';\nconst SUI_TYPE = normalizeStructTag('0x2::sui::SUI');\n\nexport function coinWithBalance({\n\ttype = SUI_TYPE,\n\tbalance,\n\tuseGasCoin = true,\n}: {\n\tbalance: bigint | number;\n\ttype?: string;\n\tuseGasCoin?: boolean;\n}) {\n\treturn (tx: Transaction) => {\n\t\ttx.addIntentResolver(COIN_WITH_BALANCE, resolveCoinBalance);\n\t\tconst coinType = type === 'gas' ? type : normalizeStructTag(type);\n\n\t\treturn tx.add(\n\t\t\tCommands.Intent({\n\t\t\t\tname: COIN_WITH_BALANCE,\n\t\t\t\tinputs: {},\n\t\t\t\tdata: {\n\t\t\t\t\ttype: coinType === SUI_TYPE && useGasCoin ? 'gas' : coinType,\n\t\t\t\t\tbalance: BigInt(balance),\n\t\t\t\t} satisfies InferInput<typeof CoinWithBalanceData>,\n\t\t\t}),\n\t\t);\n\t};\n}\n\nconst CoinWithBalanceData = object({\n\ttype: string(),\n\tbalance: bigint(),\n});\n\nasync function resolveCoinBalance(\n\ttransactionData: TransactionDataBuilder,\n\tbuildOptions: BuildTransactionOptions,\n\tnext: () => Promise<void>,\n) {\n\tconst coinTypes = new Set<string>();\n\tconst totalByType = new Map<string, bigint>();\n\n\tif (!transactionData.sender) {\n\t\tthrow new Error('Sender must be set to resolve CoinWithBalance');\n\t}\n\n\tfor (const command of transactionData.commands) {\n\t\tif (command.$kind === '$Intent' && command.$Intent.name === COIN_WITH_BALANCE) {\n\t\t\tconst { type, balance } = parse(CoinWithBalanceData, command.$Intent.data);\n\n\t\t\tif (type !== 'gas' && balance > 0n) {\n\t\t\t\tcoinTypes.add(type);\n\t\t\t}\n\n\t\t\ttotalByType.set(type, (totalByType.get(type) ?? 0n) + balance);\n\t\t}\n\t}\n\tconst usedIds = new Set<string>();\n\n\tfor (const input of transactionData.inputs) {\n\t\tif (input.Object?.ImmOrOwnedObject) {\n\t\t\tusedIds.add(input.Object.ImmOrOwnedObject.objectId);\n\t\t}\n\t\tif (input.UnresolvedObject?.objectId) {\n\t\t\tusedIds.add(input.UnresolvedObject.objectId);\n\t\t}\n\t}\n\n\tconst coinsByType = new Map<string, CoinStruct[]>();\n\tconst client = getClient(buildOptions);\n\tawait Promise.all(\n\t\t[...coinTypes].map(async (coinType) => {\n\t\t\tcoinsByType.set(\n\t\t\t\tcoinType,\n\t\t\t\tawait getCoinsOfType({\n\t\t\t\t\tcoinType,\n\t\t\t\t\tbalance: totalByType.get(coinType)!,\n\t\t\t\t\tclient,\n\t\t\t\t\towner: transactionData.sender!,\n\t\t\t\t\tusedIds,\n\t\t\t\t}),\n\t\t\t);\n\t\t}),\n\t);\n\n\tconst mergedCoins = new Map<string, Argument>();\n\n\tmergedCoins.set('gas', { $kind: 'GasCoin', GasCoin: true });\n\n\tfor (const [index, transaction] of transactionData.commands.entries()) {\n\t\tif (transaction.$kind !== '$Intent' || transaction.$Intent.name !== COIN_WITH_BALANCE) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst { type, balance } = transaction.$Intent.data as {\n\t\t\ttype: string;\n\t\t\tbalance: bigint;\n\t\t};\n\n\t\tif (balance === 0n) {\n\t\t\ttransactionData.replaceCommand(\n\t\t\t\tindex,\n\t\t\t\tCommands.MoveCall({ target: '0x2::coin::zero', typeArguments: [type] }),\n\t\t\t);\n\t\t\tcontinue;\n\t\t}\n\n\t\tconst commands = [];\n\n\t\tif (!mergedCoins.has(type)) {\n\t\t\tconst [first, ...rest] = coinsByType.get(type)!.map((coin) =>\n\t\t\t\ttransactionData.addInput(\n\t\t\t\t\t'object',\n\t\t\t\t\tInputs.ObjectRef({\n\t\t\t\t\t\tobjectId: coin.coinObjectId,\n\t\t\t\t\t\tdigest: coin.digest,\n\t\t\t\t\t\tversion: coin.version,\n\t\t\t\t\t}),\n\t\t\t\t),\n\t\t\t);\n\n\t\t\tif (rest.length > 0) {\n\t\t\t\tcommands.push(Commands.MergeCoins(first, rest));\n\t\t\t}\n\n\t\t\tmergedCoins.set(type, first);\n\t\t}\n\n\t\tcommands.push(\n\t\t\tCommands.SplitCoins(mergedCoins.get(type)!, [\n\t\t\t\ttransactionData.addInput('pure', Inputs.Pure(bcs.u64().serialize(balance))),\n\t\t\t]),\n\t\t);\n\n\t\ttransactionData.replaceCommand(index, commands);\n\n\t\ttransactionData.mapArguments((arg) => {\n\t\t\tif (arg.$kind === 'Result' && arg.Result === index) {\n\t\t\t\treturn {\n\t\t\t\t\t$kind: 'NestedResult',\n\t\t\t\t\tNestedResult: [index + commands.length - 1, 0],\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn arg;\n\t\t});\n\t}\n\n\treturn next();\n}\n\nasync function getCoinsOfType({\n\tcoinType,\n\tbalance,\n\tclient,\n\towner,\n\tusedIds,\n}: {\n\tcoinType: string;\n\tbalance: bigint;\n\tclient: SuiClient;\n\towner: string;\n\tusedIds: Set<string>;\n}): Promise<CoinStruct[]> {\n\tlet remainingBalance = balance;\n\tconst coins: CoinStruct[] = [];\n\n\treturn loadMoreCoins();\n\n\tasync function loadMoreCoins(cursor: string | null = null): Promise<CoinStruct[]> {\n\t\tconst { data, hasNextPage, nextCursor } = await client.getCoins({ owner, coinType, cursor });\n\n\t\tconst sortedCoins = data.sort((a, b) => Number(BigInt(b.balance) - BigInt(a.balance)));\n\n\t\tfor (const coin of sortedCoins) {\n\t\t\tif (usedIds.has(coin.coinObjectId)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tconst coinBalance = BigInt(coin.balance);\n\n\t\t\tcoins.push(coin);\n\t\t\tremainingBalance -= coinBalance;\n\n\t\t\tif (remainingBalance <= 0) {\n\t\t\t\treturn coins;\n\t\t\t}\n\t\t}\n\n\t\tif (hasNextPage) {\n\t\t\treturn loadMoreCoins(nextCursor);\n\t\t}\n\n\t\tthrow new Error(`Not enough coins of type ${coinType} to satisfy requested balance`);\n\t}\n}\n"], "mappings": "AAIA,SAAS,QAAQ,QAAQ,OAAO,cAAc;AAE9C,SAAS,WAAW;AAEpB,SAAS,0BAA0B;AACnC,SAAS,gBAAgB;AAEzB,SAAS,cAAc;AAEvB,SAAS,iBAAiB;AAI1B,MAAM,oBAAoB;AAC1B,MAAM,WAAW,mBAAmB,eAAe;AAE5C,SAAS,gBAAgB;AAAA,EAC/B,OAAO;AAAA,EACP;AAAA,EACA,aAAa;AACd,GAIG;AACF,SAAO,CAAC,OAAoB;AAC3B,OAAG,kBAAkB,mBAAmB,kBAAkB;AAC1D,UAAM,WAAW,SAAS,QAAQ,OAAO,mBAAmB,IAAI;AAEhE,WAAO,GAAG;AAAA,MACT,SAAS,OAAO;AAAA,QACf,MAAM;AAAA,QACN,QAAQ,CAAC;AAAA,QACT,MAAM;AAAA,UACL,MAAM,aAAa,YAAY,aAAa,QAAQ;AAAA,UACpD,SAAS,OAAO,OAAO;AAAA,QACxB;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AACD;AAEA,MAAM,sBAAsB,OAAO;AAAA,EAClC,MAAM,OAAO;AAAA,EACb,SAAS,OAAO;AACjB,CAAC;AAED,eAAe,mBACd,iBACA,cACA,MACC;AACD,QAAM,YAAY,oBAAI,IAAY;AAClC,QAAM,cAAc,oBAAI,IAAoB;AAE5C,MAAI,CAAC,gBAAgB,QAAQ;AAC5B,UAAM,IAAI,MAAM,+CAA+C;AAAA,EAChE;AAEA,aAAW,WAAW,gBAAgB,UAAU;AAC/C,QAAI,QAAQ,UAAU,aAAa,QAAQ,QAAQ,SAAS,mBAAmB;AAC9E,YAAM,EAAE,MAAM,QAAQ,IAAI,MAAM,qBAAqB,QAAQ,QAAQ,IAAI;AAEzE,UAAI,SAAS,SAAS,UAAU,IAAI;AACnC,kBAAU,IAAI,IAAI;AAAA,MACnB;AAEA,kBAAY,IAAI,OAAO,YAAY,IAAI,IAAI,KAAK,MAAM,OAAO;AAAA,IAC9D;AAAA,EACD;AACA,QAAM,UAAU,oBAAI,IAAY;AAEhC,aAAW,SAAS,gBAAgB,QAAQ;AAC3C,QAAI,MAAM,QAAQ,kBAAkB;AACnC,cAAQ,IAAI,MAAM,OAAO,iBAAiB,QAAQ;AAAA,IACnD;AACA,QAAI,MAAM,kBAAkB,UAAU;AACrC,cAAQ,IAAI,MAAM,iBAAiB,QAAQ;AAAA,IAC5C;AAAA,EACD;AAEA,QAAM,cAAc,oBAAI,IAA0B;AAClD,QAAM,SAAS,UAAU,YAAY;AACrC,QAAM,QAAQ;AAAA,IACb,CAAC,GAAG,SAAS,EAAE,IAAI,OAAO,aAAa;AACtC,kBAAY;AAAA,QACX;AAAA,QACA,MAAM,eAAe;AAAA,UACpB;AAAA,UACA,SAAS,YAAY,IAAI,QAAQ;AAAA,UACjC;AAAA,UACA,OAAO,gBAAgB;AAAA,UACvB;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAAA,EACF;AAEA,QAAM,cAAc,oBAAI,IAAsB;AAE9C,cAAY,IAAI,OAAO,EAAE,OAAO,WAAW,SAAS,KAAK,CAAC;AAE1D,aAAW,CAAC,OAAO,WAAW,KAAK,gBAAgB,SAAS,QAAQ,GAAG;AACtE,QAAI,YAAY,UAAU,aAAa,YAAY,QAAQ,SAAS,mBAAmB;AACtF;AAAA,IACD;AAEA,UAAM,EAAE,MAAM,QAAQ,IAAI,YAAY,QAAQ;AAK9C,QAAI,YAAY,IAAI;AACnB,sBAAgB;AAAA,QACf;AAAA,QACA,SAAS,SAAS,EAAE,QAAQ,mBAAmB,eAAe,CAAC,IAAI,EAAE,CAAC;AAAA,MACvE;AACA;AAAA,IACD;AAEA,UAAM,WAAW,CAAC;AAElB,QAAI,CAAC,YAAY,IAAI,IAAI,GAAG;AAC3B,YAAM,CAAC,OAAO,GAAG,IAAI,IAAI,YAAY,IAAI,IAAI,EAAG;AAAA,QAAI,CAAC,SACpD,gBAAgB;AAAA,UACf;AAAA,UACA,OAAO,UAAU;AAAA,YAChB,UAAU,KAAK;AAAA,YACf,QAAQ,KAAK;AAAA,YACb,SAAS,KAAK;AAAA,UACf,CAAC;AAAA,QACF;AAAA,MACD;AAEA,UAAI,KAAK,SAAS,GAAG;AACpB,iBAAS,KAAK,SAAS,WAAW,OAAO,IAAI,CAAC;AAAA,MAC/C;AAEA,kBAAY,IAAI,MAAM,KAAK;AAAA,IAC5B;AAEA,aAAS;AAAA,MACR,SAAS,WAAW,YAAY,IAAI,IAAI,GAAI;AAAA,QAC3C,gBAAgB,SAAS,QAAQ,OAAO,KAAK,IAAI,IAAI,EAAE,UAAU,OAAO,CAAC,CAAC;AAAA,MAC3E,CAAC;AAAA,IACF;AAEA,oBAAgB,eAAe,OAAO,QAAQ;AAE9C,oBAAgB,aAAa,CAAC,QAAQ;AACrC,UAAI,IAAI,UAAU,YAAY,IAAI,WAAW,OAAO;AACnD,eAAO;AAAA,UACN,OAAO;AAAA,UACP,cAAc,CAAC,QAAQ,SAAS,SAAS,GAAG,CAAC;AAAA,QAC9C;AAAA,MACD;AAEA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAEA,SAAO,KAAK;AACb;AAEA,eAAe,eAAe;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,GAM0B;AACzB,MAAI,mBAAmB;AACvB,QAAM,QAAsB,CAAC;AAE7B,SAAO,cAAc;AAErB,iBAAe,cAAc,SAAwB,MAA6B;AACjF,UAAM,EAAE,MAAM,aAAa,WAAW,IAAI,MAAM,OAAO,SAAS,EAAE,OAAO,UAAU,OAAO,CAAC;AAE3F,UAAM,cAAc,KAAK,KAAK,CAAC,GAAG,MAAM,OAAO,OAAO,EAAE,OAAO,IAAI,OAAO,EAAE,OAAO,CAAC,CAAC;AAErF,eAAW,QAAQ,aAAa;AAC/B,UAAI,QAAQ,IAAI,KAAK,YAAY,GAAG;AACnC;AAAA,MACD;AAEA,YAAM,cAAc,OAAO,KAAK,OAAO;AAEvC,YAAM,KAAK,IAAI;AACf,0BAAoB;AAEpB,UAAI,oBAAoB,GAAG;AAC1B,eAAO;AAAA,MACR;AAAA,IACD;AAEA,QAAI,aAAa;AAChB,aAAO,cAAc,UAAU;AAAA,IAChC;AAEA,UAAM,IAAI,MAAM,4BAA4B,QAAQ,+BAA+B;AAAA,EACpF;AACD;", "names": []}