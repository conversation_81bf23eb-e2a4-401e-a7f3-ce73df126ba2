import { isSerializedBcs } from "@mysten/bcs";
import { bcs } from "../bcs/index.js";
function createPure(makePure) {
  function pure(typeOrSerializedValue, value) {
    if (typeof typeOrSerializedValue === "string") {
      return makePure(schemaFromName(typeOrSerializedValue).serialize(value));
    }
    if (typeOrSerializedValue instanceof Uint8Array || isSerializedBcs(typeOrSerializedValue)) {
      return makePure(typeOrSerializedValue);
    }
    throw new Error("tx.pure must be called either a bcs type name, or a serialized bcs value");
  }
  pure.u8 = (value) => makePure(bcs.U8.serialize(value));
  pure.u16 = (value) => makePure(bcs.U16.serialize(value));
  pure.u32 = (value) => makePure(bcs.U32.serialize(value));
  pure.u64 = (value) => makePure(bcs.U64.serialize(value));
  pure.u128 = (value) => makePure(bcs.U128.serialize(value));
  pure.u256 = (value) => makePure(bcs.U256.serialize(value));
  pure.bool = (value) => makePure(bcs.Bool.serialize(value));
  pure.string = (value) => makePure(bcs.String.serialize(value));
  pure.address = (value) => makePure(bcs.Address.serialize(value));
  pure.id = pure.address;
  pure.vector = (type, value) => {
    return makePure(bcs.vector(schemaFromName(type)).serialize(value));
  };
  pure.option = (type, value) => {
    return makePure(bcs.option(schemaFromName(type)).serialize(value));
  };
  return pure;
}
function schemaFromName(name) {
  switch (name) {
    case "u8":
      return bcs.u8();
    case "u16":
      return bcs.u16();
    case "u32":
      return bcs.u32();
    case "u64":
      return bcs.u64();
    case "u128":
      return bcs.u128();
    case "u256":
      return bcs.u256();
    case "bool":
      return bcs.bool();
    case "string":
      return bcs.string();
    case "id":
    case "address":
      return bcs.Address;
  }
  const generic = name.match(/^(vector|option)<(.+)>$/);
  if (generic) {
    const [kind, inner] = generic.slice(1);
    if (kind === "vector") {
      return bcs.vector(schemaFromName(inner));
    } else {
      return bcs.option(schemaFromName(inner));
    }
  }
  throw new Error(`Invalid Pure type name: ${name}`);
}
export {
  createPure
};
//# sourceMappingURL=pure.js.map
