{"version": 3, "sources": ["../../../src/client/rpc-websocket-client.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { JsonRpcError } from './errors.js';\n\nfunction getWebsocketUrl(httpUrl: string): string {\n\tconst url = new URL(httpUrl);\n\turl.protocol = url.protocol.replace('http', 'ws');\n\treturn url.toString();\n}\n\ntype JsonRpcMessage =\n\t| {\n\t\t\tid: number;\n\t\t\tresult: never;\n\t\t\terror: {\n\t\t\t\tcode: number;\n\t\t\t\tmessage: string;\n\t\t\t};\n\t  }\n\t| {\n\t\t\tid: number;\n\t\t\tresult: unknown;\n\t\t\terror: never;\n\t  }\n\t| {\n\t\t\tmethod: string;\n\t\t\tparams: NotificationMessageParams;\n\t  };\n\ntype NotificationMessageParams = {\n\tsubscription?: number;\n\tresult: object;\n};\n\ntype SubscriptionRequest<T = any> = {\n\tmethod: string;\n\tunsubscribe: string;\n\tparams: any[];\n\tonMessage: (event: T) => void;\n};\n\n/**\n * Configuration options for the websocket connection\n */\nexport type WebsocketClientOptions = {\n\t/**\n\t * Custom WebSocket class to use. Defaults to the global WebSocket class, if available.\n\t */\n\tWebSocketConstructor?: typeof WebSocket;\n\t/**\n\t * Milliseconds before timing out while calling an RPC method\n\t */\n\tcallTimeout?: number;\n\t/**\n\t * Milliseconds between attempts to connect\n\t */\n\treconnectTimeout?: number;\n\t/**\n\t * Maximum number of times to try connecting before giving up\n\t */\n\tmaxReconnects?: number;\n};\n\nexport const DEFAULT_CLIENT_OPTIONS = {\n\t// We fudge the typing because we also check for undefined in the constructor:\n\tWebSocketConstructor: (typeof WebSocket !== 'undefined'\n\t\t? WebSocket\n\t\t: undefined) as typeof WebSocket,\n\tcallTimeout: 30000,\n\treconnectTimeout: 3000,\n\tmaxReconnects: 5,\n} satisfies WebsocketClientOptions;\n\nexport class WebsocketClient {\n\tendpoint: string;\n\toptions: Required<WebsocketClientOptions>;\n\t#requestId = 0;\n\t#disconnects = 0;\n\t#webSocket: WebSocket | null = null;\n\t#connectionPromise: Promise<WebSocket> | null = null;\n\t#subscriptions = new Set<RpcSubscription>();\n\t#pendingRequests = new Map<\n\t\tnumber,\n\t\t{\n\t\t\tresolve: (result: Extract<JsonRpcMessage, { id: number }>) => void;\n\t\t\treject: (reason: unknown) => void;\n\t\t\ttimeout: ReturnType<typeof setTimeout>;\n\t\t}\n\t>();\n\n\tconstructor(endpoint: string, options: WebsocketClientOptions = {}) {\n\t\tthis.endpoint = endpoint;\n\t\tthis.options = { ...DEFAULT_CLIENT_OPTIONS, ...options };\n\n\t\tif (!this.options.WebSocketConstructor) {\n\t\t\tthrow new Error('Missing WebSocket constructor');\n\t\t}\n\n\t\tif (this.endpoint.startsWith('http')) {\n\t\t\tthis.endpoint = getWebsocketUrl(this.endpoint);\n\t\t}\n\t}\n\n\tasync makeRequest<T>(method: string, params: any[]): Promise<T> {\n\t\tconst webSocket = await this.#setupWebSocket();\n\n\t\treturn new Promise<Extract<JsonRpcMessage, { id: number }>>((resolve, reject) => {\n\t\t\tthis.#requestId += 1;\n\t\t\tthis.#pendingRequests.set(this.#requestId, {\n\t\t\t\tresolve: resolve,\n\t\t\t\treject,\n\t\t\t\ttimeout: setTimeout(() => {\n\t\t\t\t\tthis.#pendingRequests.delete(this.#requestId);\n\t\t\t\t\treject(new Error(`Request timeout: ${method}`));\n\t\t\t\t}, this.options.callTimeout),\n\t\t\t});\n\n\t\t\twebSocket.send(JSON.stringify({ jsonrpc: '2.0', id: this.#requestId, method, params }));\n\t\t}).then(({ error, result }) => {\n\t\t\tif (error) {\n\t\t\t\tthrow new JsonRpcError(error.message, error.code);\n\t\t\t}\n\n\t\t\treturn result as T;\n\t\t});\n\t}\n\n\t#setupWebSocket() {\n\t\tif (this.#connectionPromise) {\n\t\t\treturn this.#connectionPromise;\n\t\t}\n\n\t\tthis.#connectionPromise = new Promise<WebSocket>((resolve) => {\n\t\t\tthis.#webSocket?.close();\n\t\t\tthis.#webSocket = new this.options.WebSocketConstructor(this.endpoint);\n\n\t\t\tthis.#webSocket.addEventListener('open', () => {\n\t\t\t\tthis.#disconnects = 0;\n\t\t\t\tresolve(this.#webSocket!);\n\t\t\t});\n\n\t\t\tthis.#webSocket.addEventListener('close', () => {\n\t\t\t\tthis.#disconnects++;\n\t\t\t\tif (this.#disconnects <= this.options.maxReconnects) {\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tthis.#reconnect();\n\t\t\t\t\t}, this.options.reconnectTimeout);\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tthis.#webSocket.addEventListener('message', ({ data }: { data: string }) => {\n\t\t\t\tlet json: JsonRpcMessage;\n\t\t\t\ttry {\n\t\t\t\t\tjson = JSON.parse(data) as JsonRpcMessage;\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(new Error(`Failed to parse RPC message: ${data}`, { cause: error }));\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif ('id' in json && json.id != null && this.#pendingRequests.has(json.id)) {\n\t\t\t\t\tconst { resolve, timeout } = this.#pendingRequests.get(json.id)!;\n\n\t\t\t\t\tclearTimeout(timeout);\n\t\t\t\t\tresolve(json);\n\t\t\t\t} else if ('params' in json) {\n\t\t\t\t\tconst { params } = json;\n\t\t\t\t\tthis.#subscriptions.forEach((subscription) => {\n\t\t\t\t\t\tif (subscription.subscriptionId === params.subscription)\n\t\t\t\t\t\t\tif (params.subscription === subscription.subscriptionId) {\n\t\t\t\t\t\t\t\tsubscription.onMessage(params.result);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t});\n\n\t\treturn this.#connectionPromise;\n\t}\n\n\tasync #reconnect() {\n\t\tthis.#webSocket?.close();\n\t\tthis.#connectionPromise = null;\n\n\t\treturn Promise.allSettled(\n\t\t\t[...this.#subscriptions].map((subscription) => subscription.subscribe(this)),\n\t\t);\n\t}\n\n\tasync subscribe<T>(input: SubscriptionRequest<T>) {\n\t\tconst subscription = new RpcSubscription(input);\n\t\tthis.#subscriptions.add(subscription);\n\t\tawait subscription.subscribe(this);\n\t\treturn () => subscription.unsubscribe(this);\n\t}\n}\n\nclass RpcSubscription {\n\tsubscriptionId: number | null = null;\n\tinput: SubscriptionRequest<any>;\n\tsubscribed = false;\n\n\tconstructor(input: SubscriptionRequest) {\n\t\tthis.input = input;\n\t}\n\n\tonMessage(message: unknown) {\n\t\tif (this.subscribed) {\n\t\t\tthis.input.onMessage(message);\n\t\t}\n\t}\n\n\tasync unsubscribe(client: WebsocketClient) {\n\t\tconst { subscriptionId } = this;\n\t\tthis.subscribed = false;\n\t\tif (subscriptionId == null) return false;\n\t\tthis.subscriptionId = null;\n\n\t\treturn client.makeRequest(this.input.unsubscribe, [subscriptionId]);\n\t}\n\n\tasync subscribe(client: WebsocketClient) {\n\t\tthis.subscriptionId = null;\n\t\tthis.subscribed = true;\n\t\tconst newSubscriptionId = await client.makeRequest<number>(\n\t\t\tthis.input.method,\n\t\t\tthis.input.params,\n\t\t);\n\n\t\tif (this.subscribed) {\n\t\t\tthis.subscriptionId = newSubscriptionId;\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAGA,SAAS,oBAAoB;AAE7B,SAAS,gBAAgB,SAAyB;AACjD,QAAM,MAAM,IAAI,IAAI,OAAO;AAC3B,MAAI,WAAW,IAAI,SAAS,QAAQ,QAAQ,IAAI;AAChD,SAAO,IAAI,SAAS;AACrB;AAuDO,MAAM,yBAAyB;AAAA;AAAA,EAErC,sBAAuB,OAAO,cAAc,cACzC,YACA;AAAA,EACH,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,eAAe;AAChB;AAEO,MAAM,gBAAgB;AAAA,EAiB5B,YAAY,UAAkB,UAAkC,CAAC,GAAG;AAjB9D;AAGN,mCAAa;AACb,qCAAe;AACf,mCAA+B;AAC/B,2CAAgD;AAChD,uCAAiB,oBAAI,IAAqB;AAC1C,yCAAmB,oBAAI,IAOrB;AAGD,SAAK,WAAW;AAChB,SAAK,UAAU,EAAE,GAAG,wBAAwB,GAAG,QAAQ;AAEvD,QAAI,CAAC,KAAK,QAAQ,sBAAsB;AACvC,YAAM,IAAI,MAAM,+BAA+B;AAAA,IAChD;AAEA,QAAI,KAAK,SAAS,WAAW,MAAM,GAAG;AACrC,WAAK,WAAW,gBAAgB,KAAK,QAAQ;AAAA,IAC9C;AAAA,EACD;AAAA,EAEA,MAAM,YAAe,QAAgB,QAA2B;AAC/D,UAAM,YAAY,MAAM,sBAAK,+CAAL;AAExB,WAAO,IAAI,QAAiD,CAAC,SAAS,WAAW;AAChF,yBAAK,YAAL,mBAAK,cAAc;AACnB,yBAAK,kBAAiB,IAAI,mBAAK,aAAY;AAAA,QAC1C;AAAA,QACA;AAAA,QACA,SAAS,WAAW,MAAM;AACzB,6BAAK,kBAAiB,OAAO,mBAAK,WAAU;AAC5C,iBAAO,IAAI,MAAM,oBAAoB,MAAM,EAAE,CAAC;AAAA,QAC/C,GAAG,KAAK,QAAQ,WAAW;AAAA,MAC5B,CAAC;AAED,gBAAU,KAAK,KAAK,UAAU,EAAE,SAAS,OAAO,IAAI,mBAAK,aAAY,QAAQ,OAAO,CAAC,CAAC;AAAA,IACvF,CAAC,EAAE,KAAK,CAAC,EAAE,OAAO,OAAO,MAAM;AAC9B,UAAI,OAAO;AACV,cAAM,IAAI,aAAa,MAAM,SAAS,MAAM,IAAI;AAAA,MACjD;AAEA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EA+DA,MAAM,UAAa,OAA+B;AACjD,UAAM,eAAe,IAAI,gBAAgB,KAAK;AAC9C,uBAAK,gBAAe,IAAI,YAAY;AACpC,UAAM,aAAa,UAAU,IAAI;AACjC,WAAO,MAAM,aAAa,YAAY,IAAI;AAAA,EAC3C;AACD;AAtHC;AACA;AACA;AACA;AACA;AACA;AARM;AAsDN,oBAAe,WAAG;AACjB,MAAI,mBAAK,qBAAoB;AAC5B,WAAO,mBAAK;AAAA,EACb;AAEA,qBAAK,oBAAqB,IAAI,QAAmB,CAAC,YAAY;AAC7D,uBAAK,aAAY,MAAM;AACvB,uBAAK,YAAa,IAAI,KAAK,QAAQ,qBAAqB,KAAK,QAAQ;AAErE,uBAAK,YAAW,iBAAiB,QAAQ,MAAM;AAC9C,yBAAK,cAAe;AACpB,cAAQ,mBAAK,WAAW;AAAA,IACzB,CAAC;AAED,uBAAK,YAAW,iBAAiB,SAAS,MAAM;AAC/C,6BAAK,cAAL;AACA,UAAI,mBAAK,iBAAgB,KAAK,QAAQ,eAAe;AACpD,mBAAW,MAAM;AAChB,gCAAK,0CAAL;AAAA,QACD,GAAG,KAAK,QAAQ,gBAAgB;AAAA,MACjC;AAAA,IACD,CAAC;AAED,uBAAK,YAAW,iBAAiB,WAAW,CAAC,EAAE,KAAK,MAAwB;AAC3E,UAAI;AACJ,UAAI;AACH,eAAO,KAAK,MAAM,IAAI;AAAA,MACvB,SAAS,OAAO;AACf,gBAAQ,MAAM,IAAI,MAAM,gCAAgC,IAAI,IAAI,EAAE,OAAO,MAAM,CAAC,CAAC;AACjF;AAAA,MACD;AAEA,UAAI,QAAQ,QAAQ,KAAK,MAAM,QAAQ,mBAAK,kBAAiB,IAAI,KAAK,EAAE,GAAG;AAC1E,cAAM,EAAE,SAAAA,UAAS,QAAQ,IAAI,mBAAK,kBAAiB,IAAI,KAAK,EAAE;AAE9D,qBAAa,OAAO;AACpB,QAAAA,SAAQ,IAAI;AAAA,MACb,WAAW,YAAY,MAAM;AAC5B,cAAM,EAAE,OAAO,IAAI;AACnB,2BAAK,gBAAe,QAAQ,CAAC,iBAAiB;AAC7C,cAAI,aAAa,mBAAmB,OAAO;AAC1C,gBAAI,OAAO,iBAAiB,aAAa,gBAAgB;AACxD,2BAAa,UAAU,OAAO,MAAM;AAAA,YACrC;AAAA;AAAA,QACF,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AAED,SAAO,mBAAK;AACb;AAEM,eAAU,iBAAG;AAClB,qBAAK,aAAY,MAAM;AACvB,qBAAK,oBAAqB;AAE1B,SAAO,QAAQ;AAAA,IACd,CAAC,GAAG,mBAAK,eAAc,EAAE,IAAI,CAAC,iBAAiB,aAAa,UAAU,IAAI,CAAC;AAAA,EAC5E;AACD;AAUD,MAAM,gBAAgB;AAAA,EAKrB,YAAY,OAA4B;AAJxC,0BAAgC;AAEhC,sBAAa;AAGZ,SAAK,QAAQ;AAAA,EACd;AAAA,EAEA,UAAU,SAAkB;AAC3B,QAAI,KAAK,YAAY;AACpB,WAAK,MAAM,UAAU,OAAO;AAAA,IAC7B;AAAA,EACD;AAAA,EAEA,MAAM,YAAY,QAAyB;AAC1C,UAAM,EAAE,eAAe,IAAI;AAC3B,SAAK,aAAa;AAClB,QAAI,kBAAkB,KAAM,QAAO;AACnC,SAAK,iBAAiB;AAEtB,WAAO,OAAO,YAAY,KAAK,MAAM,aAAa,CAAC,cAAc,CAAC;AAAA,EACnE;AAAA,EAEA,MAAM,UAAU,QAAyB;AACxC,SAAK,iBAAiB;AACtB,SAAK,aAAa;AAClB,UAAM,oBAAoB,MAAM,OAAO;AAAA,MACtC,KAAK,MAAM;AAAA,MACX,KAAK,MAAM;AAAA,IACZ;AAEA,QAAI,KAAK,YAAY;AACpB,WAAK,iBAAiB;AAAA,IACvB;AAAA,EACD;AACD;", "names": ["resolve"]}