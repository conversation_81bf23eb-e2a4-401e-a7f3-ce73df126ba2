{"version": 3, "sources": ["../../../src/client/http-transport.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { PACKAGE_VERSION, TARGETED_RPC_VERSION } from '../version.js';\nimport { JsonRpcError, SuiHTTPStatusError } from './errors.js';\nimport type { WebsocketClientOptions } from './rpc-websocket-client.js';\nimport { WebsocketClient } from './rpc-websocket-client.js';\n\n/**\n * An object defining headers to be passed to the RPC server\n */\nexport type HttpHeaders = { [header: string]: string };\n\nexport interface SuiHTTPTransportOptions {\n\tfetch?: typeof fetch;\n\tWebSocketConstructor?: typeof WebSocket;\n\turl: string;\n\trpc?: {\n\t\theaders?: HttpHeaders;\n\t\turl?: string;\n\t};\n\twebsocket?: WebsocketClientOptions & {\n\t\turl?: string;\n\t};\n}\n\nexport interface SuiTransportRequestOptions {\n\tmethod: string;\n\tparams: unknown[];\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\n\nexport interface SuiTransportSubscribeOptions<T> {\n\tmethod: string;\n\tunsubscribe: string;\n\tparams: unknown[];\n\tonMessage: (event: T) => void;\n}\n\nexport interface SuiTransport {\n\trequest<T = unknown>(input: SuiTransportRequestOptions): Promise<T>;\n\tsubscribe<T = unknown>(input: SuiTransportSubscribeOptions<T>): Promise<() => Promise<boolean>>;\n}\n\nexport class SuiHTTPTransport implements SuiTransport {\n\t#requestId = 0;\n\t#options: SuiHTTPTransportOptions;\n\t#websocketClient?: WebsocketClient;\n\n\tconstructor(options: SuiHTTPTransportOptions) {\n\t\tthis.#options = options;\n\t}\n\n\tfetch(input: RequestInfo, init?: RequestInit): Promise<Response> {\n\t\tconst fetchFn = this.#options.fetch ?? fetch;\n\n\t\tif (!fetchFn) {\n\t\t\tthrow new Error(\n\t\t\t\t'The current environment does not support fetch, you can provide a fetch implementation in the options for SuiHTTPTransport.',\n\t\t\t);\n\t\t}\n\n\t\treturn fetchFn(input, init);\n\t}\n\n\t#getWebsocketClient(): WebsocketClient {\n\t\tif (!this.#websocketClient) {\n\t\t\tconst WebSocketConstructor = this.#options.WebSocketConstructor ?? WebSocket;\n\t\t\tif (!WebSocketConstructor) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'The current environment does not support WebSocket, you can provide a WebSocketConstructor in the options for SuiHTTPTransport.',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.#websocketClient = new WebsocketClient(\n\t\t\t\tthis.#options.websocket?.url ?? this.#options.url,\n\t\t\t\t{\n\t\t\t\t\tWebSocketConstructor,\n\t\t\t\t\t...this.#options.websocket,\n\t\t\t\t},\n\t\t\t);\n\t\t}\n\n\t\treturn this.#websocketClient;\n\t}\n\n\tasync request<T>(input: SuiTransportRequestOptions): Promise<T> {\n\t\tthis.#requestId += 1;\n\n\t\tconst res = await this.fetch(this.#options.rpc?.url ?? this.#options.url, {\n\t\t\tmethod: 'POST',\n\t\t\theaders: {\n\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t'Client-Sdk-Type': 'typescript',\n\t\t\t\t'Client-Sdk-Version': PACKAGE_VERSION,\n\t\t\t\t'Client-Target-Api-Version': TARGETED_RPC_VERSION,\n\t\t\t\t'Client-Request-Method': input.method,\n\t\t\t\t...this.#options.rpc?.headers,\n\t\t\t},\n\t\t\tbody: JSON.stringify({\n\t\t\t\tjsonrpc: '2.0',\n\t\t\t\tid: this.#requestId,\n\t\t\t\tmethod: input.method,\n\t\t\t\tparams: input.params,\n\t\t\t}),\n\t\t});\n\n\t\tif (!res.ok) {\n\t\t\tthrow new SuiHTTPStatusError(\n\t\t\t\t`Unexpected status code: ${res.status}`,\n\t\t\t\tres.status,\n\t\t\t\tres.statusText,\n\t\t\t);\n\t\t}\n\n\t\tconst data = await res.json();\n\n\t\tif ('error' in data && data.error != null) {\n\t\t\tthrow new JsonRpcError(data.error.message, data.error.code);\n\t\t}\n\n\t\treturn data.result;\n\t}\n\n\tasync subscribe<T>(input: SuiTransportSubscribeOptions<T>): Promise<() => Promise<boolean>> {\n\t\tconst unsubscribe = await this.#getWebsocketClient().subscribe(input);\n\n\t\treturn async () => !!(await unsubscribe());\n\t}\n}\n"], "mappings": ";;;;;;;;AAAA;AAGA,SAAS,iBAAiB,4BAA4B;AACtD,SAAS,cAAc,0BAA0B;AAEjD,SAAS,uBAAuB;AAuCzB,MAAM,iBAAyC;AAAA,EAKrD,YAAY,SAAkC;AALxC;AACN,mCAAa;AACb;AACA;AAGC,uBAAK,UAAW;AAAA,EACjB;AAAA,EAEA,MAAM,OAAoB,MAAuC;AAChE,UAAM,UAAU,mBAAK,UAAS,SAAS;AAEvC,QAAI,CAAC,SAAS;AACb,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,WAAO,QAAQ,OAAO,IAAI;AAAA,EAC3B;AAAA,EAuBA,MAAM,QAAW,OAA+C;AAC/D,uBAAK,YAAL,mBAAK,cAAc;AAEnB,UAAM,MAAM,MAAM,KAAK,MAAM,mBAAK,UAAS,KAAK,OAAO,mBAAK,UAAS,KAAK;AAAA,MACzE,QAAQ;AAAA,MACR,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,mBAAmB;AAAA,QACnB,sBAAsB;AAAA,QACtB,6BAA6B;AAAA,QAC7B,yBAAyB,MAAM;AAAA,QAC/B,GAAG,mBAAK,UAAS,KAAK;AAAA,MACvB;AAAA,MACA,MAAM,KAAK,UAAU;AAAA,QACpB,SAAS;AAAA,QACT,IAAI,mBAAK;AAAA,QACT,QAAQ,MAAM;AAAA,QACd,QAAQ,MAAM;AAAA,MACf,CAAC;AAAA,IACF,CAAC;AAED,QAAI,CAAC,IAAI,IAAI;AACZ,YAAM,IAAI;AAAA,QACT,2BAA2B,IAAI,MAAM;AAAA,QACrC,IAAI;AAAA,QACJ,IAAI;AAAA,MACL;AAAA,IACD;AAEA,UAAM,OAAO,MAAM,IAAI,KAAK;AAE5B,QAAI,WAAW,QAAQ,KAAK,SAAS,MAAM;AAC1C,YAAM,IAAI,aAAa,KAAK,MAAM,SAAS,KAAK,MAAM,IAAI;AAAA,IAC3D;AAEA,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,MAAM,UAAa,OAAyE;AAC3F,UAAM,cAAc,MAAM,sBAAK,oDAAL,WAA2B,UAAU,KAAK;AAEpE,WAAO,YAAY,CAAC,CAAE,MAAM,YAAY;AAAA,EACzC;AACD;AApFC;AACA;AACA;AAHM;AAqBN,wBAAmB,WAAoB;AACtC,MAAI,CAAC,mBAAK,mBAAkB;AAC3B,UAAM,uBAAuB,mBAAK,UAAS,wBAAwB;AACnE,QAAI,CAAC,sBAAsB;AAC1B,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,uBAAK,kBAAmB,IAAI;AAAA,MAC3B,mBAAK,UAAS,WAAW,OAAO,mBAAK,UAAS;AAAA,MAC9C;AAAA,QACC;AAAA,QACA,GAAG,mBAAK,UAAS;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAEA,SAAO,mBAAK;AACb;", "names": []}