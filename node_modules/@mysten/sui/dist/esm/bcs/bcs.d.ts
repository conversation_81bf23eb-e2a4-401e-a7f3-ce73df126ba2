import type { BcsType } from '@mysten/bcs';
import type { TypeTag as TypeTagType } from './types.js';
export declare const Address: BcsType<string, string | Uint8Array>;
export declare const ObjectDigest: BcsType<string, string>;
export declare const SuiObjectRef: BcsType<{
    objectId: string;
    version: string;
    digest: string;
}, {
    objectId: string | Uint8Array;
    version: string | number | bigint;
    digest: string;
}>;
export declare const SharedObjectRef: BcsType<{
    objectId: string;
    initialSharedVersion: string;
    mutable: boolean;
}, {
    objectId: string | Uint8Array;
    initialSharedVersion: string | number | bigint;
    mutable: boolean;
}>;
export declare const ObjectArg: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    ImmOrOwnedObject: {
        objectId: string;
        version: string;
        digest: string;
    };
    SharedObject: {
        objectId: string;
        initialSharedVersion: string;
        mutable: boolean;
    };
    Receiving: {
        objectId: string;
        version: string;
        digest: string;
    };
}, "ImmOrOwnedObject" | "SharedObject" | "Receiving">, import("@mysten/bcs").EnumInputShape<{
    ImmOrOwnedObject: {
        objectId: string | Uint8Array;
        version: string | number | bigint;
        digest: string;
    };
    SharedObject: {
        objectId: string | Uint8Array;
        initialSharedVersion: string | number | bigint;
        mutable: boolean;
    };
    Receiving: {
        objectId: string | Uint8Array;
        version: string | number | bigint;
        digest: string;
    };
}>>;
export declare const CallArg: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    Pure: {
        bytes: string;
    };
    Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ImmOrOwnedObject: {
            objectId: string;
            version: string;
            digest: string;
        };
        SharedObject: {
            objectId: string;
            initialSharedVersion: string;
            mutable: boolean;
        };
        Receiving: {
            objectId: string;
            version: string;
            digest: string;
        };
    }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
}, "Pure" | "Object">, import("@mysten/bcs").EnumInputShape<{
    Pure: {
        bytes: string | Uint8Array;
    };
    Object: import("@mysten/bcs").EnumInputShape<{
        ImmOrOwnedObject: {
            objectId: string | Uint8Array;
            version: string | number | bigint;
            digest: string;
        };
        SharedObject: {
            objectId: string | Uint8Array;
            initialSharedVersion: string | number | bigint;
            mutable: boolean;
        };
        Receiving: {
            objectId: string | Uint8Array;
            version: string | number | bigint;
            digest: string;
        };
    }>;
}>>;
export declare const TypeTag: BcsType<string, string | TypeTagType>;
export declare const Argument: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    GasCoin: true;
    Input: number;
    Result: number;
    NestedResult: [number, number];
}, "GasCoin" | "Input" | "Result" | "NestedResult">, import("@mysten/bcs").EnumInputShape<{
    GasCoin: boolean | object | null;
    Input: number;
    Result: number;
    NestedResult: readonly [number, number];
}>>;
export declare const ProgrammableMoveCall: BcsType<{
    package: string;
    module: string;
    function: string;
    typeArguments: string[];
    arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        GasCoin: true;
        Input: number;
        Result: number;
        NestedResult: [number, number];
    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
}, {
    package: string | Uint8Array;
    module: string;
    function: string;
    typeArguments: Iterable<string | TypeTagType> & {
        length: number;
    };
    arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
        GasCoin: boolean | object | null;
        Input: number;
        Result: number;
        NestedResult: readonly [number, number];
    }>> & {
        length: number;
    };
}>;
export declare const Command: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    MoveCall: {
        package: string;
        module: string;
        function: string;
        typeArguments: string[];
        arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
    };
    TransferObjects: {
        objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
    };
    SplitCoins: {
        coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
        amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
    };
    MergeCoins: {
        destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
        sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
    };
    Publish: {
        modules: string[];
        dependencies: string[];
    };
    MakeMoveVec: {
        type: string | null;
        elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
    };
    Upgrade: {
        modules: string[];
        dependencies: string[];
        package: string;
        ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
    };
}, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">, import("@mysten/bcs").EnumInputShape<{
    MoveCall: {
        package: string | Uint8Array;
        module: string;
        function: string;
        typeArguments: Iterable<string | TypeTagType> & {
            length: number;
        };
        arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>> & {
            length: number;
        };
    };
    TransferObjects: {
        objects: Iterable<import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>> & {
            length: number;
        };
        address: import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>;
    };
    SplitCoins: {
        coin: import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>;
        amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>> & {
            length: number;
        };
    };
    MergeCoins: {
        destination: import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>;
        sources: Iterable<import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>> & {
            length: number;
        };
    };
    Publish: {
        modules: Iterable<string | Uint8Array> & {
            length: number;
        };
        dependencies: Iterable<string | Uint8Array> & {
            length: number;
        };
    };
    MakeMoveVec: {
        type: string | null;
        elements: Iterable<import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>> & {
            length: number;
        };
    };
    Upgrade: {
        modules: Iterable<string | Uint8Array> & {
            length: number;
        };
        dependencies: Iterable<string | Uint8Array> & {
            length: number;
        };
        package: string | Uint8Array;
        ticket: import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>;
    };
}>>;
export declare const ProgrammableTransaction: BcsType<{
    inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        Pure: {
            bytes: string;
        };
        Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ImmOrOwnedObject: {
                objectId: string;
                version: string;
                digest: string;
            };
            SharedObject: {
                objectId: string;
                initialSharedVersion: string;
                mutable: boolean;
            };
            Receiving: {
                objectId: string;
                version: string;
                digest: string;
            };
        }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
    }, "Pure" | "Object">[];
    commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        MoveCall: {
            package: string;
            module: string;
            function: string;
            typeArguments: string[];
            arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        TransferObjects: {
            objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
        };
        SplitCoins: {
            coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        MergeCoins: {
            destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        Publish: {
            modules: string[];
            dependencies: string[];
        };
        MakeMoveVec: {
            type: string | null;
            elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        Upgrade: {
            modules: string[];
            dependencies: string[];
            package: string;
            ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
        };
    }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
}, {
    inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
        Pure: {
            bytes: string | Uint8Array;
        };
        Object: import("@mysten/bcs").EnumInputShape<{
            ImmOrOwnedObject: {
                objectId: string | Uint8Array;
                version: string | number | bigint;
                digest: string;
            };
            SharedObject: {
                objectId: string | Uint8Array;
                initialSharedVersion: string | number | bigint;
                mutable: boolean;
            };
            Receiving: {
                objectId: string | Uint8Array;
                version: string | number | bigint;
                digest: string;
            };
        }>;
    }>> & {
        length: number;
    };
    commands: Iterable<import("@mysten/bcs").EnumInputShape<{
        MoveCall: {
            package: string | Uint8Array;
            module: string;
            function: string;
            typeArguments: Iterable<string | TypeTagType> & {
                length: number;
            };
            arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        TransferObjects: {
            objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
            address: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
        };
        SplitCoins: {
            coin: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
            amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        MergeCoins: {
            destination: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
            sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        Publish: {
            modules: Iterable<string | Uint8Array> & {
                length: number;
            };
            dependencies: Iterable<string | Uint8Array> & {
                length: number;
            };
        };
        MakeMoveVec: {
            type: string | null;
            elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        Upgrade: {
            modules: Iterable<string | Uint8Array> & {
                length: number;
            };
            dependencies: Iterable<string | Uint8Array> & {
                length: number;
            };
            package: string | Uint8Array;
            ticket: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
        };
    }>> & {
        length: number;
    };
}>;
export declare const TransactionKind: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    ProgrammableTransaction: {
        inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            Pure: {
                bytes: string;
            };
            Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                ImmOrOwnedObject: {
                    objectId: string;
                    version: string;
                    digest: string;
                };
                SharedObject: {
                    objectId: string;
                    initialSharedVersion: string;
                    mutable: boolean;
                };
                Receiving: {
                    objectId: string;
                    version: string;
                    digest: string;
                };
            }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
        }, "Pure" | "Object">[];
        commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            MoveCall: {
                package: string;
                module: string;
                function: string;
                typeArguments: string[];
                arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            TransferObjects: {
                objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            };
            SplitCoins: {
                coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            MergeCoins: {
                destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            Publish: {
                modules: string[];
                dependencies: string[];
            };
            MakeMoveVec: {
                type: string | null;
                elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            Upgrade: {
                modules: string[];
                dependencies: string[];
                package: string;
                ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            };
        }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
    };
    ChangeEpoch: true;
    Genesis: true;
    ConsensusCommitPrologue: true;
}, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">, import("@mysten/bcs").EnumInputShape<{
    ProgrammableTransaction: {
        inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
            Pure: {
                bytes: string | Uint8Array;
            };
            Object: import("@mysten/bcs").EnumInputShape<{
                ImmOrOwnedObject: {
                    objectId: string | Uint8Array;
                    version: string | number | bigint;
                    digest: string;
                };
                SharedObject: {
                    objectId: string | Uint8Array;
                    initialSharedVersion: string | number | bigint;
                    mutable: boolean;
                };
                Receiving: {
                    objectId: string | Uint8Array;
                    version: string | number | bigint;
                    digest: string;
                };
            }>;
        }>> & {
            length: number;
        };
        commands: Iterable<import("@mysten/bcs").EnumInputShape<{
            MoveCall: {
                package: string | Uint8Array;
                module: string;
                function: string;
                typeArguments: Iterable<string | TypeTagType> & {
                    length: number;
                };
                arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            TransferObjects: {
                objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
                address: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
            };
            SplitCoins: {
                coin: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
                amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            MergeCoins: {
                destination: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
                sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            Publish: {
                modules: Iterable<string | Uint8Array> & {
                    length: number;
                };
                dependencies: Iterable<string | Uint8Array> & {
                    length: number;
                };
            };
            MakeMoveVec: {
                type: string | null;
                elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            Upgrade: {
                modules: Iterable<string | Uint8Array> & {
                    length: number;
                };
                dependencies: Iterable<string | Uint8Array> & {
                    length: number;
                };
                package: string | Uint8Array;
                ticket: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
            };
        }>> & {
            length: number;
        };
    };
    ChangeEpoch: boolean | object | null;
    Genesis: boolean | object | null;
    ConsensusCommitPrologue: boolean | object | null;
}>>;
export declare const TransactionExpiration: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    None: true;
    Epoch: number;
}, "None" | "Epoch">, import("@mysten/bcs").EnumInputShape<{
    None: boolean | object | null;
    Epoch: string | number;
}>>;
export declare const StructTag: BcsType<{
    address: string;
    module: string;
    name: string;
    typeParams: TypeTagType[];
}, {
    address: string | Uint8Array;
    module: string;
    name: string;
    typeParams: Iterable<TypeTagType> & {
        length: number;
    };
}>;
export declare const GasData: BcsType<{
    payment: {
        objectId: string;
        version: string;
        digest: string;
    }[];
    owner: string;
    price: string;
    budget: string;
}, {
    payment: Iterable<{
        objectId: string | Uint8Array;
        version: string | number | bigint;
        digest: string;
    }> & {
        length: number;
    };
    owner: string | Uint8Array;
    price: string | number | bigint;
    budget: string | number | bigint;
}>;
export declare const TransactionDataV1: BcsType<{
    kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ProgrammableTransaction: {
            inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                Pure: {
                    bytes: string;
                };
                Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    ImmOrOwnedObject: {
                        objectId: string;
                        version: string;
                        digest: string;
                    };
                    SharedObject: {
                        objectId: string;
                        initialSharedVersion: string;
                        mutable: boolean;
                    };
                    Receiving: {
                        objectId: string;
                        version: string;
                        digest: string;
                    };
                }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
            }, "Pure" | "Object">[];
            commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                MoveCall: {
                    package: string;
                    module: string;
                    function: string;
                    typeArguments: string[];
                    arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                TransferObjects: {
                    objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                };
                SplitCoins: {
                    coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                MergeCoins: {
                    destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                Publish: {
                    modules: string[];
                    dependencies: string[];
                };
                MakeMoveVec: {
                    type: string | null;
                    elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                Upgrade: {
                    modules: string[];
                    dependencies: string[];
                    package: string;
                    ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                };
            }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
        };
        ChangeEpoch: true;
        Genesis: true;
        ConsensusCommitPrologue: true;
    }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
    sender: string;
    gasData: {
        payment: {
            objectId: string;
            version: string;
            digest: string;
        }[];
        owner: string;
        price: string;
        budget: string;
    };
    expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        None: true;
        Epoch: number;
    }, "None" | "Epoch">;
}, {
    kind: import("@mysten/bcs").EnumInputShape<{
        ProgrammableTransaction: {
            inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                Pure: {
                    bytes: string | Uint8Array;
                };
                Object: import("@mysten/bcs").EnumInputShape<{
                    ImmOrOwnedObject: {
                        objectId: string | Uint8Array;
                        version: string | number | bigint;
                        digest: string;
                    };
                    SharedObject: {
                        objectId: string | Uint8Array;
                        initialSharedVersion: string | number | bigint;
                        mutable: boolean;
                    };
                    Receiving: {
                        objectId: string | Uint8Array;
                        version: string | number | bigint;
                        digest: string;
                    };
                }>;
            }>> & {
                length: number;
            };
            commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                MoveCall: {
                    package: string | Uint8Array;
                    module: string;
                    function: string;
                    typeArguments: Iterable<string | TypeTagType> & {
                        length: number;
                    };
                    arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                TransferObjects: {
                    objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                    address: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                };
                SplitCoins: {
                    coin: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                    amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                MergeCoins: {
                    destination: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                    sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                Publish: {
                    modules: Iterable<string | Uint8Array> & {
                        length: number;
                    };
                    dependencies: Iterable<string | Uint8Array> & {
                        length: number;
                    };
                };
                MakeMoveVec: {
                    type: string | null;
                    elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                Upgrade: {
                    modules: Iterable<string | Uint8Array> & {
                        length: number;
                    };
                    dependencies: Iterable<string | Uint8Array> & {
                        length: number;
                    };
                    package: string | Uint8Array;
                    ticket: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                };
            }>> & {
                length: number;
            };
        };
        ChangeEpoch: boolean | object | null;
        Genesis: boolean | object | null;
        ConsensusCommitPrologue: boolean | object | null;
    }>;
    sender: string | Uint8Array;
    gasData: {
        payment: Iterable<{
            objectId: string | Uint8Array;
            version: string | number | bigint;
            digest: string;
        }> & {
            length: number;
        };
        owner: string | Uint8Array;
        price: string | number | bigint;
        budget: string | number | bigint;
    };
    expiration: import("@mysten/bcs").EnumInputShape<{
        None: boolean | object | null;
        Epoch: string | number;
    }>;
}>;
export declare const TransactionData: BcsType<{
    V1: {
        kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ProgrammableTransaction: {
                inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    Pure: {
                        bytes: string;
                    };
                    Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        ImmOrOwnedObject: {
                            objectId: string;
                            version: string;
                            digest: string;
                        };
                        SharedObject: {
                            objectId: string;
                            initialSharedVersion: string;
                            mutable: boolean;
                        };
                        Receiving: {
                            objectId: string;
                            version: string;
                            digest: string;
                        };
                    }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                }, "Pure" | "Object">[];
                commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    MoveCall: {
                        package: string;
                        module: string;
                        function: string;
                        typeArguments: string[];
                        arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    TransferObjects: {
                        objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                        address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    };
                    SplitCoins: {
                        coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                        amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    MergeCoins: {
                        destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                        sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    Publish: {
                        modules: string[];
                        dependencies: string[];
                    };
                    MakeMoveVec: {
                        type: string | null;
                        elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    Upgrade: {
                        modules: string[];
                        dependencies: string[];
                        package: string;
                        ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    };
                }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
            };
            ChangeEpoch: true;
            Genesis: true;
            ConsensusCommitPrologue: true;
        }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
        sender: string;
        gasData: {
            payment: {
                objectId: string;
                version: string;
                digest: string;
            }[];
            owner: string;
            price: string;
            budget: string;
        };
        expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            None: true;
            Epoch: number;
        }, "None" | "Epoch">;
    };
    $kind: "V1";
}, {
    V1: {
        kind: import("@mysten/bcs").EnumInputShape<{
            ProgrammableTransaction: {
                inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                    Pure: {
                        bytes: string | Uint8Array;
                    };
                    Object: import("@mysten/bcs").EnumInputShape<{
                        ImmOrOwnedObject: {
                            objectId: string | Uint8Array;
                            version: string | number | bigint;
                            digest: string;
                        };
                        SharedObject: {
                            objectId: string | Uint8Array;
                            initialSharedVersion: string | number | bigint;
                            mutable: boolean;
                        };
                        Receiving: {
                            objectId: string | Uint8Array;
                            version: string | number | bigint;
                            digest: string;
                        };
                    }>;
                }>> & {
                    length: number;
                };
                commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                    MoveCall: {
                        package: string | Uint8Array;
                        module: string;
                        function: string;
                        typeArguments: Iterable<string | TypeTagType> & {
                            length: number;
                        };
                        arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    TransferObjects: {
                        objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                        address: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                    };
                    SplitCoins: {
                        coin: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                        amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    MergeCoins: {
                        destination: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                        sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    Publish: {
                        modules: Iterable<string | Uint8Array> & {
                            length: number;
                        };
                        dependencies: Iterable<string | Uint8Array> & {
                            length: number;
                        };
                    };
                    MakeMoveVec: {
                        type: string | null;
                        elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    Upgrade: {
                        modules: Iterable<string | Uint8Array> & {
                            length: number;
                        };
                        dependencies: Iterable<string | Uint8Array> & {
                            length: number;
                        };
                        package: string | Uint8Array;
                        ticket: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                    };
                }>> & {
                    length: number;
                };
            };
            ChangeEpoch: boolean | object | null;
            Genesis: boolean | object | null;
            ConsensusCommitPrologue: boolean | object | null;
        }>;
        sender: string | Uint8Array;
        gasData: {
            payment: Iterable<{
                objectId: string | Uint8Array;
                version: string | number | bigint;
                digest: string;
            }> & {
                length: number;
            };
            owner: string | Uint8Array;
            price: string | number | bigint;
            budget: string | number | bigint;
        };
        expiration: import("@mysten/bcs").EnumInputShape<{
            None: boolean | object | null;
            Epoch: string | number;
        }>;
    };
}>;
export declare const IntentScope: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    TransactionData: true;
    TransactionEffects: true;
    CheckpointSummary: true;
    PersonalMessage: true;
}, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">, import("@mysten/bcs").EnumInputShape<{
    TransactionData: boolean | object | null;
    TransactionEffects: boolean | object | null;
    CheckpointSummary: boolean | object | null;
    PersonalMessage: boolean | object | null;
}>>;
export declare const IntentVersion: BcsType<{
    V0: true;
    $kind: "V0";
}, {
    V0: boolean | object | null;
}>;
export declare const AppId: BcsType<{
    Sui: true;
    $kind: "Sui";
}, {
    Sui: boolean | object | null;
}>;
export declare const Intent: BcsType<{
    scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        TransactionData: true;
        TransactionEffects: true;
        CheckpointSummary: true;
        PersonalMessage: true;
    }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
    version: {
        V0: true;
        $kind: "V0";
    };
    appId: {
        Sui: true;
        $kind: "Sui";
    };
}, {
    scope: import("@mysten/bcs").EnumInputShape<{
        TransactionData: boolean | object | null;
        TransactionEffects: boolean | object | null;
        CheckpointSummary: boolean | object | null;
        PersonalMessage: boolean | object | null;
    }>;
    version: {
        V0: boolean | object | null;
    };
    appId: {
        Sui: boolean | object | null;
    };
}>;
export declare function IntentMessage<T extends BcsType<any>>(T: T): BcsType<{
    intent: {
        scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            TransactionData: true;
            TransactionEffects: true;
            CheckpointSummary: true;
            PersonalMessage: true;
        }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
        version: {
            V0: true;
            $kind: "V0";
        };
        appId: {
            Sui: true;
            $kind: "Sui";
        };
    };
    value: T extends BcsType<infer U, any> ? U : never;
}, {
    intent: {
        scope: import("@mysten/bcs").EnumInputShape<{
            TransactionData: boolean | object | null;
            TransactionEffects: boolean | object | null;
            CheckpointSummary: boolean | object | null;
            PersonalMessage: boolean | object | null;
        }>;
        version: {
            V0: boolean | object | null;
        };
        appId: {
            Sui: boolean | object | null;
        };
    };
    value: T extends BcsType<any, infer U_1> ? U_1 : never;
}>;
export declare const CompressedSignature: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    ED25519: number[];
    Secp256k1: number[];
    Secp256r1: number[];
    ZkLogin: number[];
}, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">, import("@mysten/bcs").EnumInputShape<{
    ED25519: Iterable<number> & {
        length: number;
    };
    Secp256k1: Iterable<number> & {
        length: number;
    };
    Secp256r1: Iterable<number> & {
        length: number;
    };
    ZkLogin: Iterable<number> & {
        length: number;
    };
}>>;
export declare const PublicKey: BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    ED25519: number[];
    Secp256k1: number[];
    Secp256r1: number[];
    ZkLogin: number[];
}, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">, import("@mysten/bcs").EnumInputShape<{
    ED25519: Iterable<number> & {
        length: number;
    };
    Secp256k1: Iterable<number> & {
        length: number;
    };
    Secp256r1: Iterable<number> & {
        length: number;
    };
    ZkLogin: Iterable<number> & {
        length: number;
    };
}>>;
export declare const MultiSigPkMap: BcsType<{
    pubKey: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ED25519: number[];
        Secp256k1: number[];
        Secp256r1: number[];
        ZkLogin: number[];
    }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">;
    weight: number;
}, {
    pubKey: import("@mysten/bcs").EnumInputShape<{
        ED25519: Iterable<number> & {
            length: number;
        };
        Secp256k1: Iterable<number> & {
            length: number;
        };
        Secp256r1: Iterable<number> & {
            length: number;
        };
        ZkLogin: Iterable<number> & {
            length: number;
        };
    }>;
    weight: number;
}>;
export declare const MultiSigPublicKey: BcsType<{
    pk_map: {
        pubKey: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ED25519: number[];
            Secp256k1: number[];
            Secp256r1: number[];
            ZkLogin: number[];
        }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">;
        weight: number;
    }[];
    threshold: number;
}, {
    pk_map: Iterable<{
        pubKey: import("@mysten/bcs").EnumInputShape<{
            ED25519: Iterable<number> & {
                length: number;
            };
            Secp256k1: Iterable<number> & {
                length: number;
            };
            Secp256r1: Iterable<number> & {
                length: number;
            };
            ZkLogin: Iterable<number> & {
                length: number;
            };
        }>;
        weight: number;
    }> & {
        length: number;
    };
    threshold: number;
}>;
export declare const MultiSig: BcsType<{
    sigs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ED25519: number[];
        Secp256k1: number[];
        Secp256r1: number[];
        ZkLogin: number[];
    }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">[];
    bitmap: number;
    multisig_pk: {
        pk_map: {
            pubKey: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                ED25519: number[];
                Secp256k1: number[];
                Secp256r1: number[];
                ZkLogin: number[];
            }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">;
            weight: number;
        }[];
        threshold: number;
    };
}, {
    sigs: Iterable<import("@mysten/bcs").EnumInputShape<{
        ED25519: Iterable<number> & {
            length: number;
        };
        Secp256k1: Iterable<number> & {
            length: number;
        };
        Secp256r1: Iterable<number> & {
            length: number;
        };
        ZkLogin: Iterable<number> & {
            length: number;
        };
    }>> & {
        length: number;
    };
    bitmap: number;
    multisig_pk: {
        pk_map: Iterable<{
            pubKey: import("@mysten/bcs").EnumInputShape<{
                ED25519: Iterable<number> & {
                    length: number;
                };
                Secp256k1: Iterable<number> & {
                    length: number;
                };
                Secp256r1: Iterable<number> & {
                    length: number;
                };
                ZkLogin: Iterable<number> & {
                    length: number;
                };
            }>;
            weight: number;
        }> & {
            length: number;
        };
        threshold: number;
    };
}>;
export declare const base64String: BcsType<string, string | Uint8Array>;
export declare const SenderSignedTransaction: BcsType<{
    intentMessage: {
        intent: {
            scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                TransactionData: true;
                TransactionEffects: true;
                CheckpointSummary: true;
                PersonalMessage: true;
            }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
            version: {
                V0: true;
                $kind: "V0";
            };
            appId: {
                Sui: true;
                $kind: "Sui";
            };
        };
        value: {
            V1: {
                kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    ProgrammableTransaction: {
                        inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            Pure: {
                                bytes: string;
                            };
                            Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                ImmOrOwnedObject: {
                                    objectId: string;
                                    version: string;
                                    digest: string;
                                };
                                SharedObject: {
                                    objectId: string;
                                    initialSharedVersion: string;
                                    mutable: boolean;
                                };
                                Receiving: {
                                    objectId: string;
                                    version: string;
                                    digest: string;
                                };
                            }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                        }, "Pure" | "Object">[];
                        commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            MoveCall: {
                                package: string;
                                module: string;
                                function: string;
                                typeArguments: string[];
                                arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            TransferObjects: {
                                objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                            };
                            SplitCoins: {
                                coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            MergeCoins: {
                                destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            Publish: {
                                modules: string[];
                                dependencies: string[];
                            };
                            MakeMoveVec: {
                                type: string | null;
                                elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            Upgrade: {
                                modules: string[];
                                dependencies: string[];
                                package: string;
                                ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                            };
                        }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
                    };
                    ChangeEpoch: true;
                    Genesis: true;
                    ConsensusCommitPrologue: true;
                }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
                sender: string;
                gasData: {
                    payment: {
                        objectId: string;
                        version: string;
                        digest: string;
                    }[];
                    owner: string;
                    price: string;
                    budget: string;
                };
                expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    None: true;
                    Epoch: number;
                }, "None" | "Epoch">;
            };
            $kind: "V1";
        };
    };
    txSignatures: string[];
}, {
    intentMessage: {
        intent: {
            scope: import("@mysten/bcs").EnumInputShape<{
                TransactionData: boolean | object | null;
                TransactionEffects: boolean | object | null;
                CheckpointSummary: boolean | object | null;
                PersonalMessage: boolean | object | null;
            }>;
            version: {
                V0: boolean | object | null;
            };
            appId: {
                Sui: boolean | object | null;
            };
        };
        value: {
            V1: {
                kind: import("@mysten/bcs").EnumInputShape<{
                    ProgrammableTransaction: {
                        inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                            Pure: {
                                bytes: string | Uint8Array;
                            };
                            Object: import("@mysten/bcs").EnumInputShape<{
                                ImmOrOwnedObject: {
                                    objectId: string | Uint8Array;
                                    version: string | number | bigint;
                                    digest: string;
                                };
                                SharedObject: {
                                    objectId: string | Uint8Array;
                                    initialSharedVersion: string | number | bigint;
                                    mutable: boolean;
                                };
                                Receiving: {
                                    objectId: string | Uint8Array;
                                    version: string | number | bigint;
                                    digest: string;
                                };
                            }>;
                        }>> & {
                            length: number;
                        };
                        commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                            MoveCall: {
                                package: string | Uint8Array;
                                module: string;
                                function: string;
                                typeArguments: Iterable<string | TypeTagType> & {
                                    length: number;
                                };
                                arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            TransferObjects: {
                                objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                                address: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                            };
                            SplitCoins: {
                                coin: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                                amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            MergeCoins: {
                                destination: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                                sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            Publish: {
                                modules: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                                dependencies: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                            };
                            MakeMoveVec: {
                                type: string | null;
                                elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            Upgrade: {
                                modules: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                                dependencies: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                                package: string | Uint8Array;
                                ticket: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                            };
                        }>> & {
                            length: number;
                        };
                    };
                    ChangeEpoch: boolean | object | null;
                    Genesis: boolean | object | null;
                    ConsensusCommitPrologue: boolean | object | null;
                }>;
                sender: string | Uint8Array;
                gasData: {
                    payment: Iterable<{
                        objectId: string | Uint8Array;
                        version: string | number | bigint;
                        digest: string;
                    }> & {
                        length: number;
                    };
                    owner: string | Uint8Array;
                    price: string | number | bigint;
                    budget: string | number | bigint;
                };
                expiration: import("@mysten/bcs").EnumInputShape<{
                    None: boolean | object | null;
                    Epoch: string | number;
                }>;
            };
        };
    };
    txSignatures: Iterable<string | Uint8Array> & {
        length: number;
    };
}>;
export declare const SenderSignedData: BcsType<{
    intentMessage: {
        intent: {
            scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                TransactionData: true;
                TransactionEffects: true;
                CheckpointSummary: true;
                PersonalMessage: true;
            }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
            version: {
                V0: true;
                $kind: "V0";
            };
            appId: {
                Sui: true;
                $kind: "Sui";
            };
        };
        value: {
            V1: {
                kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    ProgrammableTransaction: {
                        inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            Pure: {
                                bytes: string;
                            };
                            Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                ImmOrOwnedObject: {
                                    objectId: string;
                                    version: string;
                                    digest: string;
                                };
                                SharedObject: {
                                    objectId: string;
                                    initialSharedVersion: string;
                                    mutable: boolean;
                                };
                                Receiving: {
                                    objectId: string;
                                    version: string;
                                    digest: string;
                                };
                            }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                        }, "Pure" | "Object">[];
                        commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            MoveCall: {
                                package: string;
                                module: string;
                                function: string;
                                typeArguments: string[];
                                arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            TransferObjects: {
                                objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                            };
                            SplitCoins: {
                                coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            MergeCoins: {
                                destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            Publish: {
                                modules: string[];
                                dependencies: string[];
                            };
                            MakeMoveVec: {
                                type: string | null;
                                elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            };
                            Upgrade: {
                                modules: string[];
                                dependencies: string[];
                                package: string;
                                ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    GasCoin: true;
                                    Input: number;
                                    Result: number;
                                    NestedResult: [number, number];
                                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                            };
                        }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
                    };
                    ChangeEpoch: true;
                    Genesis: true;
                    ConsensusCommitPrologue: true;
                }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
                sender: string;
                gasData: {
                    payment: {
                        objectId: string;
                        version: string;
                        digest: string;
                    }[];
                    owner: string;
                    price: string;
                    budget: string;
                };
                expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    None: true;
                    Epoch: number;
                }, "None" | "Epoch">;
            };
            $kind: "V1";
        };
    };
    txSignatures: string[];
}[], Iterable<{
    intentMessage: {
        intent: {
            scope: import("@mysten/bcs").EnumInputShape<{
                TransactionData: boolean | object | null;
                TransactionEffects: boolean | object | null;
                CheckpointSummary: boolean | object | null;
                PersonalMessage: boolean | object | null;
            }>;
            version: {
                V0: boolean | object | null;
            };
            appId: {
                Sui: boolean | object | null;
            };
        };
        value: {
            V1: {
                kind: import("@mysten/bcs").EnumInputShape<{
                    ProgrammableTransaction: {
                        inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                            Pure: {
                                bytes: string | Uint8Array;
                            };
                            Object: import("@mysten/bcs").EnumInputShape<{
                                ImmOrOwnedObject: {
                                    objectId: string | Uint8Array;
                                    version: string | number | bigint;
                                    digest: string;
                                };
                                SharedObject: {
                                    objectId: string | Uint8Array;
                                    initialSharedVersion: string | number | bigint;
                                    mutable: boolean;
                                };
                                Receiving: {
                                    objectId: string | Uint8Array;
                                    version: string | number | bigint;
                                    digest: string;
                                };
                            }>;
                        }>> & {
                            length: number;
                        };
                        commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                            MoveCall: {
                                package: string | Uint8Array;
                                module: string;
                                function: string;
                                typeArguments: Iterable<string | TypeTagType> & {
                                    length: number;
                                };
                                arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            TransferObjects: {
                                objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                                address: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                            };
                            SplitCoins: {
                                coin: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                                amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            MergeCoins: {
                                destination: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                                sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            Publish: {
                                modules: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                                dependencies: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                            };
                            MakeMoveVec: {
                                type: string | null;
                                elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>> & {
                                    length: number;
                                };
                            };
                            Upgrade: {
                                modules: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                                dependencies: Iterable<string | Uint8Array> & {
                                    length: number;
                                };
                                package: string | Uint8Array;
                                ticket: import("@mysten/bcs").EnumInputShape<{
                                    GasCoin: boolean | object | null;
                                    Input: number;
                                    Result: number;
                                    NestedResult: readonly [number, number];
                                }>;
                            };
                        }>> & {
                            length: number;
                        };
                    };
                    ChangeEpoch: boolean | object | null;
                    Genesis: boolean | object | null;
                    ConsensusCommitPrologue: boolean | object | null;
                }>;
                sender: string | Uint8Array;
                gasData: {
                    payment: Iterable<{
                        objectId: string | Uint8Array;
                        version: string | number | bigint;
                        digest: string;
                    }> & {
                        length: number;
                    };
                    owner: string | Uint8Array;
                    price: string | number | bigint;
                    budget: string | number | bigint;
                };
                expiration: import("@mysten/bcs").EnumInputShape<{
                    None: boolean | object | null;
                    Epoch: string | number;
                }>;
            };
        };
    };
    txSignatures: Iterable<string | Uint8Array> & {
        length: number;
    };
}> & {
    length: number;
}>;
export declare const PasskeyAuthenticator: BcsType<{
    authenticatorData: number[];
    clientDataJson: string;
    userSignature: number[];
}, {
    authenticatorData: Iterable<number> & {
        length: number;
    };
    clientDataJson: string;
    userSignature: Iterable<number> & {
        length: number;
    };
}>;
