{"version": 3, "sources": ["../../../src/bcs/effects.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '@mysten/bcs';\n\nimport { Address, ObjectDigest, SuiObjectRef } from './bcs.js';\n\nconst PackageUpgradeError = bcs.enum('PackageUpgradeError', {\n\tUnableToFetchPackage: bcs.struct('UnableToFetchPackage', { packageId: Address }),\n\tNotAPackage: bcs.struct('NotAPackage', { objectId: Address }),\n\tIncompatibleUpgrade: null,\n\tDigestDoesNotMatch: bcs.struct('DigestDoesNotMatch', { digest: bcs.vector(bcs.u8()) }),\n\tUnknownUpgradePolicy: bcs.struct('UnknownUpgradePolicy', { policy: bcs.u8() }),\n\tPackageIDDoesNotMatch: bcs.struct('PackageIDDoesNotMatch', {\n\t\tpackageId: Address,\n\t\tticketId: Address,\n\t}),\n});\n\nconst ModuleId = bcs.struct('ModuleId', {\n\taddress: Address,\n\tname: bcs.string(),\n});\nconst MoveLocation = bcs.struct('MoveLocation', {\n\tmodule: ModuleId,\n\tfunction: bcs.u16(),\n\tinstruction: bcs.u16(),\n\tfunctionName: bcs.option(bcs.string()),\n});\n\nconst CommandArgumentError = bcs.enum('CommandArgumentError', {\n\tTypeMismatch: null,\n\tInvalidBCSBytes: null,\n\tInvalidUsageOfPureArg: null,\n\tInvalidArgumentToPrivateEntryFunction: null,\n\tIndexOutOfBounds: bcs.struct('IndexOutOfBounds', { idx: bcs.u16() }),\n\tSecondaryIndexOutOfBounds: bcs.struct('SecondaryIndexOutOfBounds', {\n\t\tresultIdx: bcs.u16(),\n\t\tsecondaryIdx: bcs.u16(),\n\t}),\n\tInvalidResultArity: bcs.struct('InvalidResultArity', { resultIdx: bcs.u16() }),\n\tInvalidGasCoinUsage: null,\n\tInvalidValueUsage: null,\n\tInvalidObjectByValue: null,\n\tInvalidObjectByMutRef: null,\n\tSharedObjectOperationNotAllowed: null,\n});\n\nconst TypeArgumentError = bcs.enum('TypeArgumentError', {\n\tTypeNotFound: null,\n\tConstraintNotSatisfied: null,\n});\n\nconst ExecutionFailureStatus = bcs.enum('ExecutionFailureStatus', {\n\tInsufficientGas: null,\n\tInvalidGasObject: null,\n\tInvariantViolation: null,\n\tFeatureNotYetSupported: null,\n\tMoveObjectTooBig: bcs.struct('MoveObjectTooBig', {\n\t\tobjectSize: bcs.u64(),\n\t\tmaxObjectSize: bcs.u64(),\n\t}),\n\tMovePackageTooBig: bcs.struct('MovePackageTooBig', {\n\t\tobjectSize: bcs.u64(),\n\t\tmaxObjectSize: bcs.u64(),\n\t}),\n\tCircularObjectOwnership: bcs.struct('CircularObjectOwnership', { object: Address }),\n\tInsufficientCoinBalance: null,\n\tCoinBalanceOverflow: null,\n\tPublishErrorNonZeroAddress: null,\n\tSuiMoveVerificationError: null,\n\tMovePrimitiveRuntimeError: bcs.option(MoveLocation),\n\tMoveAbort: bcs.tuple([MoveLocation, bcs.u64()]),\n\tVMVerificationOrDeserializationError: null,\n\tVMInvariantViolation: null,\n\tFunctionNotFound: null,\n\tArityMismatch: null,\n\tTypeArityMismatch: null,\n\tNonEntryFunctionInvoked: null,\n\tCommandArgumentError: bcs.struct('CommandArgumentError', {\n\t\targIdx: bcs.u16(),\n\t\tkind: CommandArgumentError,\n\t}),\n\tTypeArgumentError: bcs.struct('TypeArgumentError', {\n\t\targumentIdx: bcs.u16(),\n\t\tkind: TypeArgumentError,\n\t}),\n\tUnusedValueWithoutDrop: bcs.struct('UnusedValueWithoutDrop', {\n\t\tresultIdx: bcs.u16(),\n\t\tsecondaryIdx: bcs.u16(),\n\t}),\n\tInvalidPublicFunctionReturnType: bcs.struct('InvalidPublicFunctionReturnType', {\n\t\tidx: bcs.u16(),\n\t}),\n\tInvalidTransferObject: null,\n\tEffectsTooLarge: bcs.struct('EffectsTooLarge', { currentSize: bcs.u64(), maxSize: bcs.u64() }),\n\tPublishUpgradeMissingDependency: null,\n\tPublishUpgradeDependencyDowngrade: null,\n\tPackageUpgradeError: bcs.struct('PackageUpgradeError', { upgradeError: PackageUpgradeError }),\n\tWrittenObjectsTooLarge: bcs.struct('WrittenObjectsTooLarge', {\n\t\tcurrentSize: bcs.u64(),\n\t\tmaxSize: bcs.u64(),\n\t}),\n\tCertificateDenied: null,\n\tSuiMoveVerificationTimedout: null,\n\tSharedObjectOperationNotAllowed: null,\n\tInputObjectDeleted: null,\n\tExecutionCancelledDueToSharedObjectCongestion: bcs.struct(\n\t\t'ExecutionCancelledDueToSharedObjectCongestion',\n\t\t{\n\t\t\tcongestedObjects: bcs.vector(Address),\n\t\t},\n\t),\n\tAddressDeniedForCoin: bcs.struct('AddressDeniedForCoin', {\n\t\taddress: Address,\n\t\tcoinType: bcs.string(),\n\t}),\n\tCoinTypeGlobalPause: bcs.struct('CoinTypeGlobalPause', { coinType: bcs.string() }),\n\tExecutionCancelledDueToRandomnessUnavailable: null,\n});\n\nconst ExecutionStatus = bcs.enum('ExecutionStatus', {\n\tSuccess: null,\n\tFailed: bcs.struct('ExecutionFailed', {\n\t\terror: ExecutionFailureStatus,\n\t\tcommand: bcs.option(bcs.u64()),\n\t}),\n});\n\nconst GasCostSummary = bcs.struct('GasCostSummary', {\n\tcomputationCost: bcs.u64(),\n\tstorageCost: bcs.u64(),\n\tstorageRebate: bcs.u64(),\n\tnonRefundableStorageFee: bcs.u64(),\n});\n\nconst Owner = bcs.enum('Owner', {\n\tAddressOwner: Address,\n\tObjectOwner: Address,\n\tShared: bcs.struct('Shared', {\n\t\tinitialSharedVersion: bcs.u64(),\n\t}),\n\tImmutable: null,\n});\n\nconst TransactionEffectsV1 = bcs.struct('TransactionEffectsV1', {\n\tstatus: ExecutionStatus,\n\texecutedEpoch: bcs.u64(),\n\tgasUsed: GasCostSummary,\n\tmodifiedAtVersions: bcs.vector(bcs.tuple([Address, bcs.u64()])),\n\tsharedObjects: bcs.vector(SuiObjectRef),\n\ttransactionDigest: ObjectDigest,\n\tcreated: bcs.vector(bcs.tuple([SuiObjectRef, Owner])),\n\tmutated: bcs.vector(bcs.tuple([SuiObjectRef, Owner])),\n\tunwrapped: bcs.vector(bcs.tuple([SuiObjectRef, Owner])),\n\tdeleted: bcs.vector(SuiObjectRef),\n\tunwrappedThenDeleted: bcs.vector(SuiObjectRef),\n\twrapped: bcs.vector(SuiObjectRef),\n\tgasObject: bcs.tuple([SuiObjectRef, Owner]),\n\teventsDigest: bcs.option(ObjectDigest),\n\tdependencies: bcs.vector(ObjectDigest),\n});\n\nconst VersionDigest = bcs.tuple([bcs.u64(), ObjectDigest]);\n\nconst ObjectIn = bcs.enum('ObjectIn', {\n\tNotExist: null,\n\tExist: bcs.tuple([VersionDigest, Owner]),\n});\n\nconst ObjectOut = bcs.enum('ObjectOut', {\n\tNotExist: null,\n\tObjectWrite: bcs.tuple([ObjectDigest, Owner]),\n\tPackageWrite: VersionDigest,\n});\n\nconst IDOperation = bcs.enum('IDOperation', {\n\tNone: null,\n\tCreated: null,\n\tDeleted: null,\n});\n\nconst EffectsObjectChange = bcs.struct('EffectsObjectChange', {\n\tinputState: ObjectIn,\n\toutputState: ObjectOut,\n\tidOperation: IDOperation,\n});\n\nconst UnchangedSharedKind = bcs.enum('UnchangedSharedKind', {\n\tReadOnlyRoot: VersionDigest,\n\tMutateDeleted: bcs.u64(),\n\tReadDeleted: bcs.u64(),\n\tCancelled: bcs.u64(),\n\tPerEpochConfig: null,\n});\n\nconst TransactionEffectsV2 = bcs.struct('TransactionEffectsV2', {\n\tstatus: ExecutionStatus,\n\texecutedEpoch: bcs.u64(),\n\tgasUsed: GasCostSummary,\n\ttransactionDigest: ObjectDigest,\n\tgasObjectIndex: bcs.option(bcs.u32()),\n\teventsDigest: bcs.option(ObjectDigest),\n\tdependencies: bcs.vector(ObjectDigest),\n\tlamportVersion: bcs.u64(),\n\tchangedObjects: bcs.vector(bcs.tuple([Address, EffectsObjectChange])),\n\tunchangedSharedObjects: bcs.vector(bcs.tuple([Address, UnchangedSharedKind])),\n\tauxDataDigest: bcs.option(ObjectDigest),\n});\n\nexport const TransactionEffects = bcs.enum('TransactionEffects', {\n\tV1: TransactionEffectsV1,\n\tV2: TransactionEffectsV2,\n});\n"], "mappings": "AAGA,SAAS,WAAW;AAEpB,SAAS,SAAS,cAAc,oBAAoB;AAEpD,MAAM,sBAAsB,IAAI,KAAK,uBAAuB;AAAA,EAC3D,sBAAsB,IAAI,OAAO,wBAAwB,EAAE,WAAW,QAAQ,CAAC;AAAA,EAC/E,aAAa,IAAI,OAAO,eAAe,EAAE,UAAU,QAAQ,CAAC;AAAA,EAC5D,qBAAqB;AAAA,EACrB,oBAAoB,IAAI,OAAO,sBAAsB,EAAE,QAAQ,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC;AAAA,EACrF,sBAAsB,IAAI,OAAO,wBAAwB,EAAE,QAAQ,IAAI,GAAG,EAAE,CAAC;AAAA,EAC7E,uBAAuB,IAAI,OAAO,yBAAyB;AAAA,IAC1D,WAAW;AAAA,IACX,UAAU;AAAA,EACX,CAAC;AACF,CAAC;AAED,MAAM,WAAW,IAAI,OAAO,YAAY;AAAA,EACvC,SAAS;AAAA,EACT,MAAM,IAAI,OAAO;AAClB,CAAC;AACD,MAAM,eAAe,IAAI,OAAO,gBAAgB;AAAA,EAC/C,QAAQ;AAAA,EACR,UAAU,IAAI,IAAI;AAAA,EAClB,aAAa,IAAI,IAAI;AAAA,EACrB,cAAc,IAAI,OAAO,IAAI,OAAO,CAAC;AACtC,CAAC;AAED,MAAM,uBAAuB,IAAI,KAAK,wBAAwB;AAAA,EAC7D,cAAc;AAAA,EACd,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,kBAAkB,IAAI,OAAO,oBAAoB,EAAE,KAAK,IAAI,IAAI,EAAE,CAAC;AAAA,EACnE,2BAA2B,IAAI,OAAO,6BAA6B;AAAA,IAClE,WAAW,IAAI,IAAI;AAAA,IACnB,cAAc,IAAI,IAAI;AAAA,EACvB,CAAC;AAAA,EACD,oBAAoB,IAAI,OAAO,sBAAsB,EAAE,WAAW,IAAI,IAAI,EAAE,CAAC;AAAA,EAC7E,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,iCAAiC;AAClC,CAAC;AAED,MAAM,oBAAoB,IAAI,KAAK,qBAAqB;AAAA,EACvD,cAAc;AAAA,EACd,wBAAwB;AACzB,CAAC;AAED,MAAM,yBAAyB,IAAI,KAAK,0BAA0B;AAAA,EACjE,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,wBAAwB;AAAA,EACxB,kBAAkB,IAAI,OAAO,oBAAoB;AAAA,IAChD,YAAY,IAAI,IAAI;AAAA,IACpB,eAAe,IAAI,IAAI;AAAA,EACxB,CAAC;AAAA,EACD,mBAAmB,IAAI,OAAO,qBAAqB;AAAA,IAClD,YAAY,IAAI,IAAI;AAAA,IACpB,eAAe,IAAI,IAAI;AAAA,EACxB,CAAC;AAAA,EACD,yBAAyB,IAAI,OAAO,2BAA2B,EAAE,QAAQ,QAAQ,CAAC;AAAA,EAClF,yBAAyB;AAAA,EACzB,qBAAqB;AAAA,EACrB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,2BAA2B,IAAI,OAAO,YAAY;AAAA,EAClD,WAAW,IAAI,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,CAAC;AAAA,EAC9C,sCAAsC;AAAA,EACtC,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,sBAAsB,IAAI,OAAO,wBAAwB;AAAA,IACxD,QAAQ,IAAI,IAAI;AAAA,IAChB,MAAM;AAAA,EACP,CAAC;AAAA,EACD,mBAAmB,IAAI,OAAO,qBAAqB;AAAA,IAClD,aAAa,IAAI,IAAI;AAAA,IACrB,MAAM;AAAA,EACP,CAAC;AAAA,EACD,wBAAwB,IAAI,OAAO,0BAA0B;AAAA,IAC5D,WAAW,IAAI,IAAI;AAAA,IACnB,cAAc,IAAI,IAAI;AAAA,EACvB,CAAC;AAAA,EACD,iCAAiC,IAAI,OAAO,mCAAmC;AAAA,IAC9E,KAAK,IAAI,IAAI;AAAA,EACd,CAAC;AAAA,EACD,uBAAuB;AAAA,EACvB,iBAAiB,IAAI,OAAO,mBAAmB,EAAE,aAAa,IAAI,IAAI,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC;AAAA,EAC7F,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qBAAqB,IAAI,OAAO,uBAAuB,EAAE,cAAc,oBAAoB,CAAC;AAAA,EAC5F,wBAAwB,IAAI,OAAO,0BAA0B;AAAA,IAC5D,aAAa,IAAI,IAAI;AAAA,IACrB,SAAS,IAAI,IAAI;AAAA,EAClB,CAAC;AAAA,EACD,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,+CAA+C,IAAI;AAAA,IAClD;AAAA,IACA;AAAA,MACC,kBAAkB,IAAI,OAAO,OAAO;AAAA,IACrC;AAAA,EACD;AAAA,EACA,sBAAsB,IAAI,OAAO,wBAAwB;AAAA,IACxD,SAAS;AAAA,IACT,UAAU,IAAI,OAAO;AAAA,EACtB,CAAC;AAAA,EACD,qBAAqB,IAAI,OAAO,uBAAuB,EAAE,UAAU,IAAI,OAAO,EAAE,CAAC;AAAA,EACjF,8CAA8C;AAC/C,CAAC;AAED,MAAM,kBAAkB,IAAI,KAAK,mBAAmB;AAAA,EACnD,SAAS;AAAA,EACT,QAAQ,IAAI,OAAO,mBAAmB;AAAA,IACrC,OAAO;AAAA,IACP,SAAS,IAAI,OAAO,IAAI,IAAI,CAAC;AAAA,EAC9B,CAAC;AACF,CAAC;AAED,MAAM,iBAAiB,IAAI,OAAO,kBAAkB;AAAA,EACnD,iBAAiB,IAAI,IAAI;AAAA,EACzB,aAAa,IAAI,IAAI;AAAA,EACrB,eAAe,IAAI,IAAI;AAAA,EACvB,yBAAyB,IAAI,IAAI;AAClC,CAAC;AAED,MAAM,QAAQ,IAAI,KAAK,SAAS;AAAA,EAC/B,cAAc;AAAA,EACd,aAAa;AAAA,EACb,QAAQ,IAAI,OAAO,UAAU;AAAA,IAC5B,sBAAsB,IAAI,IAAI;AAAA,EAC/B,CAAC;AAAA,EACD,WAAW;AACZ,CAAC;AAED,MAAM,uBAAuB,IAAI,OAAO,wBAAwB;AAAA,EAC/D,QAAQ;AAAA,EACR,eAAe,IAAI,IAAI;AAAA,EACvB,SAAS;AAAA,EACT,oBAAoB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC;AAAA,EAC9D,eAAe,IAAI,OAAO,YAAY;AAAA,EACtC,mBAAmB;AAAA,EACnB,SAAS,IAAI,OAAO,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;AAAA,EACpD,SAAS,IAAI,OAAO,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;AAAA,EACpD,WAAW,IAAI,OAAO,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC,CAAC;AAAA,EACtD,SAAS,IAAI,OAAO,YAAY;AAAA,EAChC,sBAAsB,IAAI,OAAO,YAAY;AAAA,EAC7C,SAAS,IAAI,OAAO,YAAY;AAAA,EAChC,WAAW,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC;AAAA,EAC1C,cAAc,IAAI,OAAO,YAAY;AAAA,EACrC,cAAc,IAAI,OAAO,YAAY;AACtC,CAAC;AAED,MAAM,gBAAgB,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,YAAY,CAAC;AAEzD,MAAM,WAAW,IAAI,KAAK,YAAY;AAAA,EACrC,UAAU;AAAA,EACV,OAAO,IAAI,MAAM,CAAC,eAAe,KAAK,CAAC;AACxC,CAAC;AAED,MAAM,YAAY,IAAI,KAAK,aAAa;AAAA,EACvC,UAAU;AAAA,EACV,aAAa,IAAI,MAAM,CAAC,cAAc,KAAK,CAAC;AAAA,EAC5C,cAAc;AACf,CAAC;AAED,MAAM,cAAc,IAAI,KAAK,eAAe;AAAA,EAC3C,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AACV,CAAC;AAED,MAAM,sBAAsB,IAAI,OAAO,uBAAuB;AAAA,EAC7D,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AACd,CAAC;AAED,MAAM,sBAAsB,IAAI,KAAK,uBAAuB;AAAA,EAC3D,cAAc;AAAA,EACd,eAAe,IAAI,IAAI;AAAA,EACvB,aAAa,IAAI,IAAI;AAAA,EACrB,WAAW,IAAI,IAAI;AAAA,EACnB,gBAAgB;AACjB,CAAC;AAED,MAAM,uBAAuB,IAAI,OAAO,wBAAwB;AAAA,EAC/D,QAAQ;AAAA,EACR,eAAe,IAAI,IAAI;AAAA,EACvB,SAAS;AAAA,EACT,mBAAmB;AAAA,EACnB,gBAAgB,IAAI,OAAO,IAAI,IAAI,CAAC;AAAA,EACpC,cAAc,IAAI,OAAO,YAAY;AAAA,EACrC,cAAc,IAAI,OAAO,YAAY;AAAA,EACrC,gBAAgB,IAAI,IAAI;AAAA,EACxB,gBAAgB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,mBAAmB,CAAC,CAAC;AAAA,EACpE,wBAAwB,IAAI,OAAO,IAAI,MAAM,CAAC,SAAS,mBAAmB,CAAC,CAAC;AAAA,EAC5E,eAAe,IAAI,OAAO,YAAY;AACvC,CAAC;AAEM,MAAM,qBAAqB,IAAI,KAAK,sBAAsB;AAAA,EAChE,IAAI;AAAA,EACJ,IAAI;AACL,CAAC;", "names": []}