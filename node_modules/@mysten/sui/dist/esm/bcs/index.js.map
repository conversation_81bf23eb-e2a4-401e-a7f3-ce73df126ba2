{"version": 3, "sources": ["../../../src/bcs/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '@mysten/bcs';\n\nimport {\n\tAddress,\n\tAppId,\n\tArgument,\n\tCallArg,\n\tCommand,\n\tCompressedSignature,\n\tGasData,\n\tIntent,\n\tIntentMessage,\n\tIntentScope,\n\tIntentVersion,\n\tMultiSig,\n\tMultiSigPkMap,\n\tMultiSigPublicKey,\n\tObjectArg,\n\tObjectDigest,\n\tPasskeyAuthenticator,\n\tProgrammableMoveCall,\n\tProgrammableTransaction,\n\tPublicKey,\n\tSenderSignedData,\n\tSenderSignedTransaction,\n\tSharedObjectRef,\n\tStructTag,\n\tSuiObjectRef,\n\tTransactionData,\n\tTransactionDataV1,\n\tTransactionExpiration,\n\tTransactionKind,\n\tTypeTag,\n} from './bcs.js';\nimport { TransactionEffects } from './effects.js';\n\nexport type { TypeTag } from './types.js';\n\nexport { TypeTagSerializer } from './type-tag-serializer.js';\nexport { BcsType, type BcsTypeOptions } from '@mysten/bcs';\n\nconst suiBcs = {\n\t...bcs,\n\tU8: bcs.u8(),\n\tU16: bcs.u16(),\n\tU32: bcs.u32(),\n\tU64: bcs.u64(),\n\tU128: bcs.u128(),\n\tU256: bcs.u256(),\n\tULEB128: bcs.uleb128(),\n\tBool: bcs.bool(),\n\tString: bcs.string(),\n\tAddress,\n\tAppId,\n\tArgument,\n\tCallArg,\n\tCompressedSignature,\n\tGasData,\n\tIntent,\n\tIntentMessage,\n\tIntentScope,\n\tIntentVersion,\n\tMultiSig,\n\tMultiSigPkMap,\n\tMultiSigPublicKey,\n\tObjectArg,\n\tObjectDigest,\n\tProgrammableMoveCall,\n\tProgrammableTransaction,\n\tPublicKey,\n\tSenderSignedData,\n\tSenderSignedTransaction,\n\tSharedObjectRef,\n\tStructTag,\n\tSuiObjectRef,\n\tCommand,\n\tTransactionData,\n\tTransactionDataV1,\n\tTransactionExpiration,\n\tTransactionKind,\n\tTypeTag,\n\tTransactionEffects,\n\tPasskeyAuthenticator,\n};\n\nexport { suiBcs as bcs };\n"], "mappings": "AAGA,SAAS,WAAW;AAEpB;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AACP,SAAS,0BAA0B;AAInC,SAAS,yBAAyB;AAClC,SAAS,eAAoC;AAE7C,MAAM,SAAS;AAAA,EACd,GAAG;AAAA,EACH,IAAI,IAAI,GAAG;AAAA,EACX,KAAK,IAAI,IAAI;AAAA,EACb,KAAK,IAAI,IAAI;AAAA,EACb,KAAK,IAAI,IAAI;AAAA,EACb,MAAM,IAAI,KAAK;AAAA,EACf,MAAM,IAAI,KAAK;AAAA,EACf,SAAS,IAAI,QAAQ;AAAA,EACrB,MAAM,IAAI,KAAK;AAAA,EACf,QAAQ,IAAI,OAAO;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;", "names": []}