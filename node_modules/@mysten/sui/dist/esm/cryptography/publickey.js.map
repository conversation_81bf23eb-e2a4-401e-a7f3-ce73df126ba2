{"version": 3, "sources": ["../../../src/cryptography/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/bcs';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { bytesToHex } from '@noble/hashes/utils';\n\nimport { bcs } from '../bcs/index.js';\nimport { normalizeSuiAddress, SUI_ADDRESS_LENGTH } from '../utils/sui-types.js';\nimport type { IntentScope } from './intent.js';\nimport { messageWithIntent } from './intent.js';\n\n/**\n * Value to be converted into public key.\n */\nexport type PublicKeyInitData = string | Uint8Array | Iterable<number>;\n\nexport function bytesEqual(a: Uint8Array, b: Uint8Array) {\n\tif (a === b) return true;\n\n\tif (a.length !== b.length) {\n\t\treturn false;\n\t}\n\n\tfor (let i = 0; i < a.length; i++) {\n\t\tif (a[i] !== b[i]) {\n\t\t\treturn false;\n\t\t}\n\t}\n\treturn true;\n}\n\n/**\n * A public key\n */\nexport abstract class PublicKey {\n\t/**\n\t * Checks if two public keys are equal\n\t */\n\tequals(publicKey: PublicKey) {\n\t\treturn bytesEqual(this.toRawBytes(), publicKey.toRawBytes());\n\t}\n\n\t/**\n\t * Return the base-64 representation of the public key\n\t */\n\ttoBase64() {\n\t\treturn toBase64(this.toRawBytes());\n\t}\n\n\ttoString(): never {\n\t\tthrow new Error(\n\t\t\t'`toString` is not implemented on public keys. Use `toBase64()` or `toRawBytes()` instead.',\n\t\t);\n\t}\n\n\t/**\n\t * Return the Sui representation of the public key encoded in\n\t * base-64. A Sui public key is formed by the concatenation\n\t * of the scheme flag with the raw bytes of the public key\n\t */\n\ttoSuiPublicKey(): string {\n\t\tconst bytes = this.toSuiBytes();\n\t\treturn toBase64(bytes);\n\t}\n\n\tverifyWithIntent(\n\t\tbytes: Uint8Array,\n\t\tsignature: Uint8Array | string,\n\t\tintent: IntentScope,\n\t): Promise<boolean> {\n\t\tconst intentMessage = messageWithIntent(intent, bytes);\n\t\tconst digest = blake2b(intentMessage, { dkLen: 32 });\n\n\t\treturn this.verify(digest, signature);\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided PersonalMessage\n\t */\n\tverifyPersonalMessage(message: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\treturn this.verifyWithIntent(\n\t\t\tbcs.vector(bcs.u8()).serialize(message).toBytes(),\n\t\t\tsignature,\n\t\t\t'PersonalMessage',\n\t\t);\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided Transaction\n\t */\n\tverifyTransaction(transaction: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\treturn this.verifyWithIntent(transaction, signature, 'TransactionData');\n\t}\n\n\t/**\n\t * Verifies that the public key is associated with the provided address\n\t */\n\tverifyAddress(address: string): boolean {\n\t\treturn this.toSuiAddress() === address;\n\t}\n\n\t/**\n\t * Returns the bytes representation of the public key\n\t * prefixed with the signature scheme flag\n\t */\n\ttoSuiBytes(): Uint8Array {\n\t\tconst rawBytes = this.toRawBytes();\n\t\tconst suiBytes = new Uint8Array(rawBytes.length + 1);\n\t\tsuiBytes.set([this.flag()]);\n\t\tsuiBytes.set(rawBytes, 1);\n\n\t\treturn suiBytes;\n\t}\n\n\t/**\n\t * Return the Sui address associated with this Ed25519 public key\n\t */\n\ttoSuiAddress(): string {\n\t\t// Each hex char represents half a byte, hence hex address doubles the length\n\t\treturn normalizeSuiAddress(\n\t\t\tbytesToHex(blake2b(this.toSuiBytes(), { dkLen: 32 })).slice(0, SUI_ADDRESS_LENGTH * 2),\n\t\t);\n\t}\n\n\t/**\n\t * Return the byte array representation of the public key\n\t */\n\tabstract toRawBytes(): Uint8Array;\n\n\t/**\n\t * Return signature scheme flag of the public key\n\t */\n\tabstract flag(): number;\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tabstract verify(data: Uint8Array, signature: Uint8Array | string): Promise<boolean>;\n}\n"], "mappings": "AAGA,SAAS,gBAAgB;AACzB,SAAS,eAAe;AACxB,SAAS,kBAAkB;AAE3B,SAAS,WAAW;AACpB,SAAS,qBAAqB,0BAA0B;AAExD,SAAS,yBAAyB;AAO3B,SAAS,WAAW,GAAe,GAAe;AACxD,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,EAAE,WAAW,EAAE,QAAQ;AAC1B,WAAO;AAAA,EACR;AAEA,WAAS,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAClC,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AAClB,aAAO;AAAA,IACR;AAAA,EACD;AACA,SAAO;AACR;AAKO,MAAe,UAAU;AAAA;AAAA;AAAA;AAAA,EAI/B,OAAO,WAAsB;AAC5B,WAAO,WAAW,KAAK,WAAW,GAAG,UAAU,WAAW,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW;AACV,WAAO,SAAS,KAAK,WAAW,CAAC;AAAA,EAClC;AAAA,EAEA,WAAkB;AACjB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,iBAAyB;AACxB,UAAM,QAAQ,KAAK,WAAW;AAC9B,WAAO,SAAS,KAAK;AAAA,EACtB;AAAA,EAEA,iBACC,OACA,WACA,QACmB;AACnB,UAAM,gBAAgB,kBAAkB,QAAQ,KAAK;AACrD,UAAM,SAAS,QAAQ,eAAe,EAAE,OAAO,GAAG,CAAC;AAEnD,WAAO,KAAK,OAAO,QAAQ,SAAS;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAqB,WAAkD;AAC5F,WAAO,KAAK;AAAA,MACX,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU,OAAO,EAAE,QAAQ;AAAA,MAChD;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAyB,WAAkD;AAC5F,WAAO,KAAK,iBAAiB,aAAa,WAAW,iBAAiB;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAA0B;AACvC,WAAO,KAAK,aAAa,MAAM;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAyB;AACxB,UAAM,WAAW,KAAK,WAAW;AACjC,UAAM,WAAW,IAAI,WAAW,SAAS,SAAS,CAAC;AACnD,aAAS,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC;AAC1B,aAAS,IAAI,UAAU,CAAC;AAExB,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,eAAuB;AAEtB,WAAO;AAAA,MACN,WAAW,QAAQ,KAAK,WAAW,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,qBAAqB,CAAC;AAAA,IACtF;AAAA,EACD;AAgBD;", "names": []}