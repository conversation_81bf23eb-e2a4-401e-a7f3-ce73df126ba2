import { <PERSON>Key } from '../cryptography/publickey.js';
import type { PublicKeyInitData } from '../cryptography/publickey.js';
import { SuiGraphQLClient } from '../graphql/client.js';
/**
 * A zkLogin public identifier
 */
export declare class ZkLoginPublicIdentifier extends PublicKey {
    #private;
    /**
     * Create a new ZkLoginPublicIdentifier object
     * @param value zkLogin public identifier as buffer or base-64 encoded string
     */
    constructor(value: PublicKeyInitData, { client }?: {
        client?: SuiGraphQLClient;
    });
    /**
     * Checks if two zkLogin public identifiers are equal
     */
    equals(publicKey: ZkLoginPublicIdentifier): boolean;
    toSuiAddress(): string;
    /**
     * Return the byte array representation of the zkLogin public identifier
     */
    toRawBytes(): Uint8Array;
    /**
     * Return the Sui address associated with this ZkLogin public identifier
     */
    flag(): number;
    /**
     * Verifies that the signature is valid for for the provided message
     */
    verify(_message: Uint8Array, _signature: Uint8Array | string): Promise<boolean>;
    /**
     * Verifies that the signature is valid for for the provided PersonalMessage
     */
    verifyPersonalMessage(message: Uint8Array, signature: Uint8Array | string): Promise<boolean>;
    /**
     * Verifies that the signature is valid for for the provided Transaction
     */
    verifyTransaction(transaction: Uint8Array, signature: Uint8Array | string): Promise<boolean>;
    /**
     * Verifies that the public key is associated with the provided address
     */
    verifyAddress(address: string): boolean;
}
export declare function toZkLoginPublicIdentifier(addressSeed: bigint, iss: string, options?: {
    client?: SuiGraphQLClient;
    legacyAddress?: boolean;
}): ZkLoginPublicIdentifier;
export declare function parseSerializedZkLoginSignature(signature: Uint8Array | string): {
    serializedSignature: string;
    signatureScheme: "ZkLogin";
    zkLogin: {
        inputs: {
            proofPoints: {
                a: string[];
                b: string[][];
                c: string[];
            };
            issBase64Details: {
                value: string;
                indexMod4: number;
            };
            headerBase64: string;
            addressSeed: string;
        };
        maxEpoch: string;
        userSignature: number[];
        iss: string;
        addressSeed: bigint;
    };
    signature: Uint8Array;
    publicKey: Uint8Array;
};
