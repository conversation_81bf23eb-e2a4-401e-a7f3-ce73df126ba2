{"version": 3, "sources": ["../../../src/zklogin/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64, toHex } from '@mysten/bcs';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { bytesToHex } from '@noble/hashes/utils';\n\nimport { PublicKey } from '../cryptography/publickey.js';\nimport type { PublicKeyInitData } from '../cryptography/publickey.js';\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../cryptography/signature-scheme.js';\nimport { SuiGraphQLClient } from '../graphql/client.js';\nimport { graphql } from '../graphql/schemas/latest/index.js';\nimport { normalizeSuiAddress, SUI_ADDRESS_LENGTH } from '../utils/sui-types.js';\nimport { extractClaimValue } from './jwt-utils.js';\nimport { parseZkLoginSignature } from './signature.js';\nimport { toBigEndianBytes, toPaddedBigEndianBytes } from './utils.js';\n\n/**\n * A zkLogin public identifier\n */\nexport class ZkLoginPublicIdentifier extends PublicKey {\n\t#data: Uint8Array;\n\t#client?: SuiGraphQLClient;\n\t#legacyAddress: boolean;\n\n\t/**\n\t * Create a new ZkLoginPublicIdentifier object\n\t * @param value zkLogin public identifier as buffer or base-64 encoded string\n\t */\n\tconstructor(value: PublicKeyInitData, { client }: { client?: SuiGraphQLClient } = {}) {\n\t\tsuper();\n\n\t\tthis.#client = client;\n\n\t\tif (typeof value === 'string') {\n\t\t\tthis.#data = fromBase64(value);\n\t\t} else if (value instanceof Uint8Array) {\n\t\t\tthis.#data = value;\n\t\t} else {\n\t\t\tthis.#data = Uint8Array.from(value);\n\t\t}\n\t\tthis.#legacyAddress = this.#data.length !== this.#data[0] + 1 + 32;\n\n\t\tif (this.#legacyAddress) {\n\t\t\tthis.#data = normalizeZkLoginPublicKeyBytes(this.#data);\n\t\t}\n\t}\n\n\t/**\n\t * Checks if two zkLogin public identifiers are equal\n\t */\n\toverride equals(publicKey: ZkLoginPublicIdentifier): boolean {\n\t\treturn super.equals(publicKey);\n\t}\n\n\toverride toSuiAddress(): string {\n\t\tif (this.#legacyAddress) {\n\t\t\treturn this.#toLegacyAddress();\n\t\t}\n\n\t\treturn super.toSuiAddress();\n\t}\n\n\t#toLegacyAddress() {\n\t\tconst legacyBytes = normalizeZkLoginPublicKeyBytes(this.#data, true);\n\t\tconst addressBytes = new Uint8Array(legacyBytes.length + 1);\n\t\taddressBytes[0] = this.flag();\n\t\taddressBytes.set(legacyBytes, 1);\n\t\treturn normalizeSuiAddress(\n\t\t\tbytesToHex(blake2b(addressBytes, { dkLen: 32 })).slice(0, SUI_ADDRESS_LENGTH * 2),\n\t\t);\n\t}\n\n\t/**\n\t * Return the byte array representation of the zkLogin public identifier\n\t */\n\ttoRawBytes(): Uint8Array {\n\t\treturn this.#data;\n\t}\n\n\t/**\n\t * Return the Sui address associated with this ZkLogin public identifier\n\t */\n\tflag(): number {\n\t\treturn SIGNATURE_SCHEME_TO_FLAG['ZkLogin'];\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tasync verify(_message: Uint8Array, _signature: Uint8Array | string): Promise<boolean> {\n\t\tthrow Error('does not support');\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided PersonalMessage\n\t */\n\tverifyPersonalMessage(message: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tconst parsedSignature = parseSerializedZkLoginSignature(signature);\n\t\tconst address = new ZkLoginPublicIdentifier(parsedSignature.publicKey).toSuiAddress();\n\n\t\treturn graphqlVerifyZkLoginSignature({\n\t\t\taddress: address,\n\t\t\tbytes: toBase64(message),\n\t\t\tsignature: parsedSignature.serializedSignature,\n\t\t\tintentScope: 'PERSONAL_MESSAGE',\n\t\t\tclient: this.#client,\n\t\t});\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided Transaction\n\t */\n\tverifyTransaction(transaction: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tconst parsedSignature = parseSerializedZkLoginSignature(signature);\n\t\tconst address = new ZkLoginPublicIdentifier(parsedSignature.publicKey).toSuiAddress();\n\t\treturn graphqlVerifyZkLoginSignature({\n\t\t\taddress: address,\n\t\t\tbytes: toBase64(transaction),\n\t\t\tsignature: parsedSignature.serializedSignature,\n\t\t\tintentScope: 'TRANSACTION_DATA',\n\t\t\tclient: this.#client,\n\t\t});\n\t}\n\n\t/**\n\t * Verifies that the public key is associated with the provided address\n\t */\n\toverride verifyAddress(address: string): boolean {\n\t\treturn address === super.toSuiAddress() || address === this.#toLegacyAddress();\n\t}\n}\n\n// Derive the public identifier for zklogin based on address seed and iss.\nexport function toZkLoginPublicIdentifier(\n\taddressSeed: bigint,\n\tiss: string,\n\toptions?: { client?: SuiGraphQLClient; legacyAddress?: boolean },\n): ZkLoginPublicIdentifier {\n\t// Consists of iss_bytes_len || iss_bytes || padded_32_byte_address_seed.\n\tconst addressSeedBytesBigEndian = options?.legacyAddress\n\t\t? toBigEndianBytes(addressSeed, 32)\n\t\t: toPaddedBigEndianBytes(addressSeed, 32);\n\n\tconst issBytes = new TextEncoder().encode(iss);\n\tconst tmp = new Uint8Array(1 + issBytes.length + addressSeedBytesBigEndian.length);\n\ttmp.set([issBytes.length], 0);\n\ttmp.set(issBytes, 1);\n\ttmp.set(addressSeedBytesBigEndian, 1 + issBytes.length);\n\treturn new ZkLoginPublicIdentifier(tmp, options);\n}\n\nconst VerifyZkLoginSignatureQuery = graphql(`\n\tquery Zklogin(\n\t\t$bytes: Base64!\n\t\t$signature: Base64!\n\t\t$intentScope: ZkLoginIntentScope!\n\t\t$author: SuiAddress!\n\t) {\n\t\tverifyZkloginSignature(\n\t\t\tbytes: $bytes\n\t\t\tsignature: $signature\n\t\t\tintentScope: $intentScope\n\t\t\tauthor: $author\n\t\t) {\n\t\t\tsuccess\n\t\t\terrors\n\t\t}\n\t}\n`);\n\nfunction normalizeZkLoginPublicKeyBytes(bytes: Uint8Array, legacyAddress = false) {\n\tconst issByteLength = bytes[0] + 1;\n\tconst addressSeed = BigInt(`0x${toHex(bytes.slice(issByteLength))}`);\n\tconst seedBytes = legacyAddress\n\t\t? toBigEndianBytes(addressSeed, 32)\n\t\t: toPaddedBigEndianBytes(addressSeed, 32);\n\tconst data = new Uint8Array(issByteLength + seedBytes.length);\n\tdata.set(bytes.slice(0, issByteLength), 0);\n\tdata.set(seedBytes, issByteLength);\n\treturn data;\n}\n\nasync function graphqlVerifyZkLoginSignature({\n\taddress,\n\tbytes,\n\tsignature,\n\tintentScope,\n\tclient = new SuiGraphQLClient({\n\t\turl: 'https://sui-mainnet.mystenlabs.com/graphql',\n\t}),\n}: {\n\taddress: string;\n\tbytes: string;\n\tsignature: string;\n\tintentScope: 'PERSONAL_MESSAGE' | 'TRANSACTION_DATA';\n\tclient?: SuiGraphQLClient;\n}) {\n\tconst resp = await client.query({\n\t\tquery: VerifyZkLoginSignatureQuery,\n\t\tvariables: {\n\t\t\tbytes,\n\t\t\tsignature,\n\t\t\tintentScope,\n\t\t\tauthor: address,\n\t\t},\n\t});\n\n\treturn (\n\t\tresp.data?.verifyZkloginSignature.success === true &&\n\t\tresp.data?.verifyZkloginSignature.errors.length === 0\n\t);\n}\n\nexport function parseSerializedZkLoginSignature(signature: Uint8Array | string) {\n\tconst bytes = typeof signature === 'string' ? fromBase64(signature) : signature;\n\n\tif (bytes[0] !== SIGNATURE_SCHEME_TO_FLAG.ZkLogin) {\n\t\tthrow new Error('Invalid signature scheme');\n\t}\n\n\tconst signatureBytes = bytes.slice(1);\n\tconst { inputs, maxEpoch, userSignature } = parseZkLoginSignature(signatureBytes);\n\tconst { issBase64Details, addressSeed } = inputs;\n\tconst iss = extractClaimValue<string>(issBase64Details, 'iss');\n\tconst publicIdentifer = toZkLoginPublicIdentifier(BigInt(addressSeed), iss);\n\treturn {\n\t\tserializedSignature: toBase64(bytes),\n\t\tsignatureScheme: 'ZkLogin' as const,\n\t\tzkLogin: {\n\t\t\tinputs,\n\t\t\tmaxEpoch,\n\t\t\tuserSignature,\n\t\t\tiss,\n\t\t\taddressSeed: BigInt(addressSeed),\n\t\t},\n\t\tsignature: bytes,\n\t\tpublicKey: publicIdentifer.toRawBytes(),\n\t};\n}\n"], "mappings": ";;;;;;;;AAAA;AAGA,SAAS,YAAY,UAAU,aAAa;AAC5C,SAAS,eAAe;AACxB,SAAS,kBAAkB;AAE3B,SAAS,iBAAiB;AAE1B,SAAS,gCAAgC;AACzC,SAAS,wBAAwB;AACjC,SAAS,eAAe;AACxB,SAAS,qBAAqB,0BAA0B;AACxD,SAAS,yBAAyB;AAClC,SAAS,6BAA6B;AACtC,SAAS,kBAAkB,8BAA8B;AAKlD,MAAM,2BAAN,MAAM,iCAAgC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAStD,YAAY,OAA0B,EAAE,OAAO,IAAmC,CAAC,GAAG;AACrF,UAAM;AAVD;AACN;AACA;AACA;AASC,uBAAK,SAAU;AAEf,QAAI,OAAO,UAAU,UAAU;AAC9B,yBAAK,OAAQ,WAAW,KAAK;AAAA,IAC9B,WAAW,iBAAiB,YAAY;AACvC,yBAAK,OAAQ;AAAA,IACd,OAAO;AACN,yBAAK,OAAQ,WAAW,KAAK,KAAK;AAAA,IACnC;AACA,uBAAK,gBAAiB,mBAAK,OAAM,WAAW,mBAAK,OAAM,CAAC,IAAI,IAAI;AAEhE,QAAI,mBAAK,iBAAgB;AACxB,yBAAK,OAAQ,+BAA+B,mBAAK,MAAK;AAAA,IACvD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKS,OAAO,WAA6C;AAC5D,WAAO,MAAM,OAAO,SAAS;AAAA,EAC9B;AAAA,EAES,eAAuB;AAC/B,QAAI,mBAAK,iBAAgB;AACxB,aAAO,sBAAK,wDAAL;AAAA,IACR;AAEA,WAAO,MAAM,aAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAeA,aAAyB;AACxB,WAAO,mBAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe;AACd,WAAO,yBAAyB,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,UAAsB,YAAmD;AACrF,UAAM,MAAM,kBAAkB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAqB,WAAkD;AAC5F,UAAM,kBAAkB,gCAAgC,SAAS;AACjE,UAAM,UAAU,IAAI,yBAAwB,gBAAgB,SAAS,EAAE,aAAa;AAEpF,WAAO,8BAA8B;AAAA,MACpC;AAAA,MACA,OAAO,SAAS,OAAO;AAAA,MACvB,WAAW,gBAAgB;AAAA,MAC3B,aAAa;AAAA,MACb,QAAQ,mBAAK;AAAA,IACd,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAyB,WAAkD;AAC5F,UAAM,kBAAkB,gCAAgC,SAAS;AACjE,UAAM,UAAU,IAAI,yBAAwB,gBAAgB,SAAS,EAAE,aAAa;AACpF,WAAO,8BAA8B;AAAA,MACpC;AAAA,MACA,OAAO,SAAS,WAAW;AAAA,MAC3B,WAAW,gBAAgB;AAAA,MAC3B,aAAa;AAAA,MACb,QAAQ,mBAAK;AAAA,IACd,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKS,cAAc,SAA0B;AAChD,WAAO,YAAY,MAAM,aAAa,KAAK,YAAY,sBAAK,wDAAL;AAAA,EACxD;AACD;AA9GC;AACA;AACA;AAHM;AA2CN,qBAAgB,WAAG;AAClB,QAAM,cAAc,+BAA+B,mBAAK,QAAO,IAAI;AACnE,QAAM,eAAe,IAAI,WAAW,YAAY,SAAS,CAAC;AAC1D,eAAa,CAAC,IAAI,KAAK,KAAK;AAC5B,eAAa,IAAI,aAAa,CAAC;AAC/B,SAAO;AAAA,IACN,WAAW,QAAQ,cAAc,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,qBAAqB,CAAC;AAAA,EACjF;AACD;AAnDM,IAAM,0BAAN;AAkHA,SAAS,0BACf,aACA,KACA,SAC0B;AAE1B,QAAM,4BAA4B,SAAS,gBACxC,iBAAiB,aAAa,EAAE,IAChC,uBAAuB,aAAa,EAAE;AAEzC,QAAM,WAAW,IAAI,YAAY,EAAE,OAAO,GAAG;AAC7C,QAAM,MAAM,IAAI,WAAW,IAAI,SAAS,SAAS,0BAA0B,MAAM;AACjF,MAAI,IAAI,CAAC,SAAS,MAAM,GAAG,CAAC;AAC5B,MAAI,IAAI,UAAU,CAAC;AACnB,MAAI,IAAI,2BAA2B,IAAI,SAAS,MAAM;AACtD,SAAO,IAAI,wBAAwB,KAAK,OAAO;AAChD;AAEA,MAAM,8BAA8B,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,CAiB3C;AAED,SAAS,+BAA+B,OAAmB,gBAAgB,OAAO;AACjF,QAAM,gBAAgB,MAAM,CAAC,IAAI;AACjC,QAAM,cAAc,OAAO,KAAK,MAAM,MAAM,MAAM,aAAa,CAAC,CAAC,EAAE;AACnE,QAAM,YAAY,gBACf,iBAAiB,aAAa,EAAE,IAChC,uBAAuB,aAAa,EAAE;AACzC,QAAM,OAAO,IAAI,WAAW,gBAAgB,UAAU,MAAM;AAC5D,OAAK,IAAI,MAAM,MAAM,GAAG,aAAa,GAAG,CAAC;AACzC,OAAK,IAAI,WAAW,aAAa;AACjC,SAAO;AACR;AAEA,eAAe,8BAA8B;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,IAAI,iBAAiB;AAAA,IAC7B,KAAK;AAAA,EACN,CAAC;AACF,GAMG;AACF,QAAM,OAAO,MAAM,OAAO,MAAM;AAAA,IAC/B,OAAO;AAAA,IACP,WAAW;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,MACA,QAAQ;AAAA,IACT;AAAA,EACD,CAAC;AAED,SACC,KAAK,MAAM,uBAAuB,YAAY,QAC9C,KAAK,MAAM,uBAAuB,OAAO,WAAW;AAEtD;AAEO,SAAS,gCAAgC,WAAgC;AAC/E,QAAM,QAAQ,OAAO,cAAc,WAAW,WAAW,SAAS,IAAI;AAEtE,MAAI,MAAM,CAAC,MAAM,yBAAyB,SAAS;AAClD,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC3C;AAEA,QAAM,iBAAiB,MAAM,MAAM,CAAC;AACpC,QAAM,EAAE,QAAQ,UAAU,cAAc,IAAI,sBAAsB,cAAc;AAChF,QAAM,EAAE,kBAAkB,YAAY,IAAI;AAC1C,QAAM,MAAM,kBAA0B,kBAAkB,KAAK;AAC7D,QAAM,kBAAkB,0BAA0B,OAAO,WAAW,GAAG,GAAG;AAC1E,SAAO;AAAA,IACN,qBAAqB,SAAS,KAAK;AAAA,IACnC,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,OAAO,WAAW;AAAA,IAChC;AAAA,IACA,WAAW;AAAA,IACX,WAAW,gBAAgB,WAAW;AAAA,EACvC;AACD;", "names": []}