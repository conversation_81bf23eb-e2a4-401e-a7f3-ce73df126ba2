{"name": "@cetusprotocol/cetus-sui-clmm-sdk", "version": "5.3.4", "description": "SDK for cetus swap and liquidity", "typings": "dist/index.d.ts", "main": "dist/index.js", "module": "dist/index.mjs", "scripts": {"test": "jest", "lint:fix": "eslint src/*.ts src/**/*.ts --fix", "build": "rm -rf dist/ && tsc", "testbuild": "tsc", "prepare": "husky install", "build:tsup": "npm run build:clean && npm run _build:node", "build:clean": "rm -rf dist", "_build:browser": "tsup --platform browser --format iife --global-name cetusAptosSDK --minify", "_build:node": "tsup --format cjs,esm --dts", "build:doc": "npx typedoc", "publish:test": "node version.js && npm publish --tag experimental"}, "repository": {"type": "git", "url": ""}, "keywords": [], "author": "test", "license": "Apache-2.0", "devDependencies": {"@commitlint/cli": "^17.1.2", "@mysten/bcs": "^1.3.0", "@mysten/sui": "^1.21.1", "@types/cors": "^2.8.13", "@types/jest": "^29.0.0", "@types/lossless-json": "^1.0.1", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.36.1", "@typescript-eslint/parser": "^5.36.1", "bip39-light": "^1.0.7", "eslint": "^8.23.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.1", "jest": "^29.0.0", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "standard-version": "^9.5.0", "ts-jest": "^29.0.0", "tsup": "^6.6.2", "typedoc": "^0.24.8", "typescript": "^5.4.5", "dotenv": "^16.4.5"}, "peerDependencies": {"@mysten/bcs": ">=0.8.1", "@mysten/sui": ">=1.1.2"}, "dependencies": {"@suchipi/femver": "^1.0.0", "@syntsugar/cc-graph": "^0.1.1", "@types/bn.js": "^5.1.1", "axios": "^1.4.0", "bn.js": "^5.2.1", "cors": "^2.8.5", "decimal.js": "^10.4.1", "dotenv": "^16.4.7", "isomorphic-fetch": "^3.0.0", "js-base64": "^3.7.4", "js-sha3": "^0.8.0", "superstruct": "^1.0.3", "tiny-invariant": "^1.1.0", "tweetnacl": "^1.0.3", "uuid": "^9.0.0"}, "lint-staged": {"src/**.{ts,js}": "eslint --fix", "src/*/**.{ts,js}": "eslint --fix"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}}