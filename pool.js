// Simulation script to find generated pool address without actual execution
require('dotenv').config();

const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { initCetusSDK } = require('@cetusprotocol/cetus-sui-clmm-sdk');
const BN = require('bn.js');
const { d, TickMath } = require('@cetusprotocol/cetus-sui-clmm-sdk');
const getPoolIdFromTxHash = require('./poolFinder');
const swap_cetus = require('./swapex');
const fs = require('fs').promises;

// Define colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    magenta: '\x1b[35m',
    white: '\x1b[37m'
};

// This function simulates pool creation to find the address
async function createPoolWithLiquidity(tokenAddress, suiAmount, tokenAmount = null, preferredTickSpacing = null, explicitTickLower = null, explicitTickUpper = null) {
    // Keep track of attempts
    let attempts = 0;
    const maxAttempts = 3;
    const isForceMode = process.env.FORCE_TICK_SPACING === "true";

    // IMPORTANT: Use the SAME tick spacing for all attempts if in force mode
    // Otherwise, have fallbacks in case of failure
    const tick_spacing_options = preferredTickSpacing
        ? [preferredTickSpacing]
        : (isForceMode ? [10] : [10, 200, 2000]);

    console.log(colors.green + "Pool creation parameters:" + colors.reset);
    console.log(`SUI amount: ${suiAmount} SUI`);
    console.log(`Token amount: ${tokenAmount !== null ? tokenAmount : 'Auto-calculated'}`);
    console.log(`Preferred tick spacing: ${preferredTickSpacing}`);

    // Log fee tier equivalent for clarity
    console.log(colors.cyan + `Fee tier mapping:` + colors.reset);
    console.log(`10 tick spacing = 1% fee (High tier)`)
    console.log(`200 tick spacing = 0.3% fee (Medium tier)`)
    console.log(`2000 tick spacing = 0.05% fee (Low tier)`)
    console.log(`10000 tick spacing = 0.01% fee (Lowest tier)`)

    if (explicitTickLower !== null && explicitTickUpper !== null) {
        console.log(`Using explicit tick range: ${explicitTickLower} to ${explicitTickUpper}`);
    } else {
        console.log(`Will calculate global liquidity bounds based on tick spacing`);
    }

    while (attempts < maxAttempts) {
        try {
            console.log(`Pool creation attempt ${attempts + 1} of ${maxAttempts}`);

            // Initialize clients (same as in the original code)
            const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
            const client = new SuiClient({ url: fullNodeUrl });
            const privateKey = process.env.PRIVATE_KEY;
            const keypair = Ed25519Keypair.fromSecretKey(privateKey);
            const address = keypair.getPublicKey().toSuiAddress();

            console.log('Wallet address:', address);

            // Define token types
            const BABY_TYPE = tokenAddress;
            const SUI_TYPE = '0x2::sui::SUI';

            // Initialize SDK with simulation flag
            const sdkConfig = {
                network: 'mainnet',
                fullNodeUrl: fullNodeUrl,
                simulationAccount: address,
                senderAddress: address,
                feePayer: address
            };

            const cetusSDK = await initCetusSDK(sdkConfig);
            cetusSDK.senderAddress = address;

            // Fetch coin metadata
            const babyCoinMetadata = await client.getCoinMetadata({ coinType: BABY_TYPE });
            const totalSupply = await client.getTotalSupply({ coinType: BABY_TYPE });

            const suiCoinMetadata = await client.getCoinMetadata({ coinType: SUI_TYPE });

            console.log('Token metadata:', {
                name: babyCoinMetadata.name,
                symbol: babyCoinMetadata.symbol,
                decimals: babyCoinMetadata.decimals,
                totalSupply: totalSupply.value
            });

            // Calculate initial price based on the desired ratio
            // If tokenAmount is provided, use that value, otherwise use total supply
            const desiredTokenAmount = tokenAmount !== null ? tokenAmount : Number(totalSupply.value);
            const initialPrice = suiAmount / desiredTokenAmount;
            console.log(`Setting initial price: ${initialPrice} SUI per token`);

            // Select tick spacing - ALWAYS use the first option (no more cycling)
            let tick_spacing = tick_spacing_options[0];

            // Log the exact values being used in smallest units
            const tokenDecimals = babyCoinMetadata.decimals;
            const suiDecimals = suiCoinMetadata.decimals;
            const tokenInSmallestUnit = tokenAmount * (10 ** tokenDecimals);
            const suiInSmallestUnit = suiAmount * (10 ** suiDecimals);
            console.log(`Amounts in smallest units: ${suiInSmallestUnit} MIST, ${tokenInSmallestUnit} token units`);
            console.log(colors.green + `Using tick spacing: ${tick_spacing} (${tick_spacing == 10 ? "high tier/1% fee" :
                    tick_spacing == 200 ? "medium tier/0.3% fee" :
                        tick_spacing == 2000 ? "low tier/0.05% fee" :
                            "lowest tier/0.01% fee"
                })` + colors.reset);

            let initialize_sqrt_price;

            // Override sqrt price calculation for exact SUI/token ratio
            if (tokenAmount !== null && suiAmount > 0) {
                console.log(colors.yellow + "Using forced ratio calculation to ensure exact SUI and token amounts" + colors.reset);

                // Calculate sqrt price using the proper method
                initialize_sqrt_price = TickMath.priceToSqrtPriceX64(
                    d(initialPrice),
                    tokenDecimals,
                    suiDecimals
                ).toString();

                console.log(`Forced sqrt price calculated: ${initialize_sqrt_price}`);
            } else {
                // Original calculation
                initialize_sqrt_price = TickMath.priceToSqrtPriceX64(
                    d(initialPrice),
                    babyCoinMetadata.decimals,
                    suiCoinMetadata.decimals
                ).toString();
            }

            const current_tick_index = TickMath.sqrtPriceX64ToTickIndex(new BN(initialize_sqrt_price));
            console.log(`Current tick index: ${current_tick_index.toString()}`);

            // FULL RANGE IMPLEMENTATION with support for explicit tick values:
            let tick_lower, tick_upper;

            if (explicitTickLower !== null && explicitTickUpper !== null) {
                // Use the explicit tick values provided without adjustment
                console.log(colors.green + `Using provided tick range exactly as specified: ${explicitTickLower} to ${explicitTickUpper}` + colors.reset);

                // DO NOT adjust these values - they're already correct for the Cetus contract
                tick_lower = explicitTickLower;
                tick_upper = explicitTickUpper;

                // Log warning if values aren't aligned with tick spacing, but don't adjust
                if (tick_lower % tick_spacing !== 0 || tick_upper % tick_spacing !== 0) {
                    console.log(colors.yellow + `Warning: Tick values are not aligned with tick spacing ${tick_spacing}, but using as-is:` + colors.reset);
                    console.log(`Lower: ${tick_lower}, Upper: ${tick_upper}`);
                }
            } else {
                // Calculate global liquidity range using formula directly from Cetus docs
                console.log(colors.green + "Calculating global liquidity tick bounds for tick spacing: " + tick_spacing + colors.reset);

                // Try multiple approaches for tick range calculation
                let approaches = [];

                // Approach 1: Cetus UI approach for full range (0 to ∞)
                // For SUI/token pairs, this means setting the lower tick to a reasonable MIN_TICK value
                // and the upper tick to a price that's effectively infinity
                // Note: For full range Cetus UI uses different values based on tick spacing
                let fullRangeLower, fullRangeUpper;

                if (tick_spacing === 200) {
                    // For tick spacing 200, use the exact values from the successful transaction
                    fullRangeLower = -443600;
                    fullRangeUpper = 443600;
                } else {
                    const MIN_TICK = -887272;
                    const MAX_TICK = 0;
                    fullRangeLower = Math.ceil(MIN_TICK / tick_spacing) * tick_spacing;
                    fullRangeUpper = Math.floor(MAX_TICK / tick_spacing) * tick_spacing;
                }

                approaches.push({
                    name: "Cetus UI Full Range (0 to ∞)",
                    lower: fullRangeLower,
                    upper: fullRangeUpper
                });

                // Approach 2: EXACT formula from Cetus docs (this was causing issues)
                const CETUS_MAX_TICK = 443636;
                const tickLowerFormula = -CETUS_MAX_TICK + (CETUS_MAX_TICK % tick_spacing);
                const tickUpperFormula = CETUS_MAX_TICK - (CETUS_MAX_TICK % tick_spacing);
                approaches.push({
                    name: "Exact Cetus Docs Formula",
                    lower: tickLowerFormula,
                    upper: tickUpperFormula
                });

                // Approach 3: More conservative range
                const SAFE_MIN_TICK = -300000;
                const SAFE_MAX_TICK = 300000;
                const safeTickLower = Math.ceil(SAFE_MIN_TICK / tick_spacing) * tick_spacing;
                const safeTickUpper = Math.floor(SAFE_MAX_TICK / tick_spacing) * tick_spacing;
                approaches.push({
                    name: "Conservative Range",
                    lower: safeTickLower,
                    upper: safeTickUpper
                });

                // Approach 4: Very conservative range
                const VERY_SAFE_MIN_TICK = -100000;
                const VERY_SAFE_MAX_TICK = 100000;
                const verySafeTickLower = Math.ceil(VERY_SAFE_MIN_TICK / tick_spacing) * tick_spacing;
                const verySafeTickUpper = Math.floor(VERY_SAFE_MAX_TICK / tick_spacing) * tick_spacing;
                approaches.push({
                    name: "Very Conservative Range",
                    lower: verySafeTickLower,
                    upper: verySafeTickUpper
                });

                // Display all approach options
                console.log(colors.cyan + "Tick range calculation approaches:" + colors.reset);
                approaches.forEach((approach, i) => {
                    console.log(`${i + 1}. ${approach.name}: ${approach.lower} to ${approach.upper}`);
                });

                // Use Cetus UI Full Range approach for first attempt, fall back to others if needed
                let selectedApproach = 0; // Default to Cetus UI Full Range
                if (attempts > 0) {
                    // Use more conservative approaches for retry attempts
                    selectedApproach = Math.min(attempts + 1, approaches.length - 1);
                }

                console.log(colors.green + `Using approach ${selectedApproach + 1}: ${approaches[selectedApproach].name}` + colors.reset);
                tick_lower = approaches[selectedApproach].lower;
                tick_upper = approaches[selectedApproach].upper;

                console.log(`Using tick range: ${tick_lower} to ${tick_upper}`);

                // If Cetus's formula gives NaN, fall back to conservative range
                if (isNaN(tick_lower) || isNaN(tick_upper)) {
                    console.log(colors.yellow + "Warning: Invalid tick values, falling back to conservative range" + colors.reset);
                    tick_lower = approaches[1].lower;
                    tick_upper = approaches[1].upper;
                }
            }

            console.log(colors.green + `Creating position with tick range:` + colors.reset);
            console.log(`Min tick: ${tick_lower} to Max tick: ${tick_upper} with spacing ${tick_spacing}`);

            // Explicitly set amounts
            // Convert token amount based on decimals
            const amount_a = tokenAmount !== null
                ? BigInt(tokenAmount) * BigInt(10 ** tokenDecimals)
                : Number(totalSupply.value);

            const amount_b = Math.floor(suiAmount * 1e9); // Convert SUI to MIST

            // This is another critical change: fix BOTH amounts
            const fix_amount_a = true; // Always fix token amount
            const fix_amount_b = true; // Always fix SUI amount too

            console.log('Liquidity amounts:', {
                TokenA: amount_a.toString(),
                'TokenA (formatted)': Number(amount_a) / (10 ** babyCoinMetadata.decimals),
                SUI: amount_b,
                'SUI (in SUI)': amount_b / 1e9
            });

            // Prepare pool data with explicit overrides for the fixed ratio
            const poolData = {
                coinTypeA: BABY_TYPE,
                coinTypeB: SUI_TYPE,
                tick_spacing,  // THIS IS THE CRITICAL PARAMETER FOR FEE TIER
                initialize_sqrt_price,
                uri: '',
                amount_a: amount_a.toString(),
                amount_b,
                fix_amount_a,
                fix_amount_b,
                tick_lower,
                tick_upper,
                metadata_a: babyCoinMetadata.id,
                metadata_b: suiCoinMetadata.id,
            };

            console.log('Pool parameters:', {
                tickSpacing: tick_spacing,
                feeTier: tick_spacing == 10 ? "1% (High)" :
                    tick_spacing == 200 ? "0.3% (Medium)" :
                        tick_spacing == 2000 ? "0.05% (Low)" : "0.01% (Lowest)",
                currentTickIndex: current_tick_index.toString(),
                lowerTick: tick_lower,
                upperTick: tick_upper,
                initialSqrtPrice: initialize_sqrt_price,
                forcingExactAmounts: tokenAmount !== null && suiAmount > 0,
                isFullRange: explicitTickLower === null || (tick_lower === TickMath.getPrevInitializableTickIndex(TickMath.MIN_TICK, new BN(tick_spacing).toNumber()))
            });

            // Create pool transaction
            console.log(colors.cyan + `Attempting to create pool with tick spacing ${tick_spacing} (${tick_spacing == 10 ? "1% fee" :
                    tick_spacing == 200 ? "0.3% fee" :
                        tick_spacing == 2000 ? "0.05% fee" :
                            "0.01% fee"
                })...` + colors.reset);
            try {
                // Make a modified copy of the poolData with fixed tick values
                const cleanPoolData = {
                    ...poolData,
                    tick_spacing, // Ensure this is explicitly set
                    // Make sure tick values are divisible by tick_spacing
                    tick_lower: Math.floor(poolData.tick_lower / tick_spacing) * tick_spacing,
                    tick_upper: Math.ceil(poolData.tick_upper / tick_spacing) * tick_spacing
                };

                // Log the final pool data being used
                console.log(colors.green + "Final pool data:" + colors.reset, {
                    tickLower: cleanPoolData.tick_lower,
                    tickUpper: cleanPoolData.tick_upper,
                    tickSpacing: cleanPoolData.tick_spacing
                });

                const txb = await cetusSDK.Pool.createPoolAndAddLiquidity(cleanPoolData);

                // Log detailed transaction data to debug
                console.log(colors.magenta + 'TRANSACTION DETAILS:' + colors.reset);
                console.log(`Type Arguments:`, txb.blockData.transactions[1].typeArguments);
                console.log(`Tick Spacing Parameter: ${tick_spacing} (${tick_spacing == 10 ? "1% fee tier" :
                        tick_spacing == 200 ? "0.3% fee tier" :
                            tick_spacing == 2000 ? "0.05% fee tier" :
                                "0.01% fee tier"
                    })`);

                // CRITICAL: Ensure the tick spacing parameter is correctly included in the transaction
                const tickSpacingFound = txb.blockData.transactions.some(tx =>
                    tx.arguments && tx.arguments.some(arg =>
                        arg.type === "pure" &&
                        arg.valueType === "u32" &&
                        parseInt(arg.value) === tick_spacing));

                if (!tickSpacingFound) {
                    console.log(colors.red + "WARNING: Tick spacing parameter not found in transaction! Checking all arguments:" + colors.reset);

                    // Print all arguments to help debug
                    txb.blockData.transactions.forEach((tx, i) => {
                        if (tx.arguments) {
                            console.log(`Transaction ${i} arguments:`);
                            tx.arguments.forEach((arg, j) => {
                                if (arg.type === "pure" && arg.valueType === "u32") {
                                    console.log(`  Arg ${j}: type=${arg.type}, valueType=${arg.valueType}, value=${arg.value}`);
                                }
                            });
                        }
                    });

                    // Try to find any tick spacing parameters that might be wrong
                    const foundArgs = [];
                    txb.blockData.transactions.forEach(tx => {
                        if (tx.arguments) {
                            tx.arguments.forEach(arg => {
                                if (arg.type === "pure" && arg.valueType === "u32") {
                                    // These are common tick spacing values
                                    if ([10, 200, 2000, 10000].includes(parseInt(arg.value))) {
                                        foundArgs.push(parseInt(arg.value));
                                    }
                                }
                            });
                        }
                    });

                    if (foundArgs.length > 0) {
                        console.log(colors.yellow + `Found potential tick spacing values in transaction: ${foundArgs.join(", ")}` + colors.reset);

                        if (foundArgs.some(val => val !== tick_spacing)) {
                            console.log(colors.red + `WARNING: Transaction contains tick spacing ${foundArgs.join(", ")} instead of requested ${tick_spacing}!` + colors.reset);

                            // If in force mode, abort the transaction
                            if (isForceMode) {
                                throw new Error(`Incorrect tick spacing detected in transaction: found ${foundArgs.join(", ")}, expected ${tick_spacing}. Aborting in force mode.`);
                            }
                        }
                    }
                } else {
                    console.log(colors.green + `Verified: Tick spacing ${tick_spacing} correctly included in transaction` + colors.reset);
                }

                // Extract transaction details for logging
                console.log('Transaction details:');
                console.log(txb.blockData);

                // Critical improvement: Set explicit gas budget
                const result = await client.signAndExecuteTransaction({
                    signer: keypair,
                    transaction: txb,
                    options: {
                        showEffects: true,
                        showEvents: true,
                        showInput: true,
                        showObjectChanges: true,
                        showBalanceChanges: true,
                        // Set a explicit gas budget like the successful transaction
                        gasBudget: 300000000 // Increasing to 0.3 SUI for more flexibility
                    }
                });

                // Check transaction result
                if (result.effects?.status?.status === 'success') {
                    console.log(colors.green + 'Successfully created pool with liquidity!' + colors.reset);
                    console.log(`TX: https://suivision.xyz/txblock/${result.digest}`);

                    try {
                        // Try to get pool ID
                        let poolID = await getPoolIdFromTxHash(result.digest);

                        if (poolID) {
                            console.log(`Found pool ID: ${poolID}`);
                            try {
                                // Execute buy operations with proper error handling
                                console.log("Starting buy operations...");
                                await bundleBuyModule(poolID);
                                console.log('Successfully completed buy operations for pool:', poolID);
                            } catch (buyError) {
                                console.error('Error during buy operations:', buyError.message);
                                console.log('You can manually run buys later using the pool ID:', poolID);
                            }
                        } else {
                            // If pool ID can't be retrieved, use the token address as a fallback
                            console.log("Couldn't retrieve pool ID, using token address as fallback for buys");
                            try {
                                console.log("Starting buy operations with token address...");
                                await bundleBuyModule(BABY_TYPE);
                                console.log('Successfully completed buy operations using token address');
                            } catch (fallbackError) {
                                console.error('Error during fallback buy operations:', fallbackError.message);
                                console.log('Pool was created successfully, but buys failed. The transaction hash is:', result.digest);
                                console.log('You can manually run buys later using menu option 12 with your token address.');
                            }
                        }
                    } catch (poolIdError) {
                        console.error('Error retrieving pool ID:', poolIdError.message);
                        // Still try to run buys using token address
                        console.log("Using token address for buys instead of pool ID");
                        try {
                            await bundleBuyModule(BABY_TYPE);
                            console.log('Successfully completed buy operations using token address');
                        } catch (tokenBuyError) {
                            console.error('Error during token-based buy operations:', tokenBuyError.message);
                            console.log('You can manually run buys later using menu option 12.');
                        }
                    }

                    // Successfully created pool, so we should exit the retry loop
                    return;
                } else {
                    console.error('Transaction failed:', result.effects?.status);
                    console.error('Full Effects:', JSON.stringify(result.effects, null, 2));

                    // Try again with different parameters
                    attempts++;
                    console.log(`Transaction failed. Trying again with different parameters (attempt ${attempts + 1})...`);

                    // Add a small delay before the next attempt
                    await new Promise(resolve => setTimeout(resolve, 2000));
                }

            } catch (poolError) {
                console.error(colors.red + `Pool creation error with tick spacing ${tick_spacing}:` + colors.reset, poolError.message);

                if (isForceMode) {
                    console.error(colors.red + `FORCE MODE: Not trying other tick spacings as requested.` + colors.reset);
                    throw new Error(`Failed to create pool with forced tick spacing ${tick_spacing}: ${poolError.message}`);
                }

                // Only rethrow after all attempts in the while loop
                throw poolError;
            }

        } catch (error) {
            console.error('tx liquidity error:', error);

            // Try again with different parameters
            attempts++;

            if (attempts < maxAttempts) {
                console.log(`Error: ${error.message}`);
                console.log(`Trying again with different parameters (attempt ${attempts + 1})...`);

                // Add a small delay before the next attempt
                await new Promise(resolve => setTimeout(resolve, 2000));
            } else {
                throw error; // Rethrow the error after max attempts
            }
        }
    }

    console.error(`Failed to create pool after ${maxAttempts} attempts`);
    throw new Error(`Failed to create pool after ${maxAttempts} attempts with different parameters`);
}

const bundleBuyModule = async (tokenAddress) => {
    try {
        console.log(colors.green + `Starting buy operations for token/pool: ${tokenAddress}` + colors.reset);
        const walletData = await fs.readFile('wallets.json', 'utf-8');
        const wallets = JSON.parse(walletData);

        console.log(`Processing ${wallets.length} wallets for token purchases...`);

        // Increase batch size to 10 wallets
        const batchSize = 10; // Increased from 5 to 10 as requested
        const walletBatches = [];

        // Create batches of wallets
        for (let i = 0; i < wallets.length; i += batchSize) {
            walletBatches.push(wallets.slice(i, i + batchSize));
        }

        let successCount = 0;
        let failureCount = 0;

        // Process batches sequentially with delays between batches
        for (let batchIndex = 0; batchIndex < walletBatches.length; batchIndex++) {
            const batch = walletBatches[batchIndex];
            console.log(`Batch ${batchIndex + 1}/${walletBatches.length} (${batch.length} wallets)`);

            // Process wallets in this batch concurrently
            const batchPromises = batch.map(async wallet => {
                // Increased retries with exponential backoff
                const maxRetries = 3;
                let currentRetry = 0;

                while (currentRetry <= maxRetries) {
                    try {
                        // Get amount from wallet configuration
                        let amount = wallet.buyAmount;

                        // Pass the entire wallet object to the swap function
                        const result = await swap_cetus(tokenAddress, wallet, amount);
                        console.log(`✅ ${wallet.address.substring(0, 8)}... bought ${amount.toFixed(4)} SUI worth`);
                        return { success: true, wallet };
                    } catch (error) {
                        currentRetry++;
                        const isRateLimit = error.message.includes('429') || error.message.includes('Too Many Requests');

                        if (isRateLimit) {
                            console.log(`⚠️ ${wallet.address.substring(0, 8)}... hit rate limit, retry ${currentRetry}/${maxRetries}`);
                            // Add exponential backoff for rate limit errors
                            const backoffDelay = Math.min(2000 * Math.pow(2, currentRetry), 16000);
                            console.log(`Rate limit detected: waiting ${backoffDelay / 1000} seconds before retry...`);
                            await new Promise(resolve => setTimeout(resolve, backoffDelay));
                        } else {
                            console.log(`❌ ${wallet.address.substring(0, 8)}... failed: ${error.message}`);

                            if (currentRetry <= maxRetries) {
                                // Standard retry delay
                                const delay = 1000 * currentRetry;
                                await new Promise(resolve => setTimeout(resolve, delay));
                            } else {
                                return { success: false, wallet, error: error.message };
                            }
                        }
                    }
                }
                return { success: false, wallet, error: "Max retries exceeded" };
            });

            // Wait for all transactions in this batch to complete
            const batchResults = await Promise.all(batchPromises);

            // Count successes and failures
            const batchSuccesses = batchResults.filter(result => result.success).length;
            successCount += batchSuccesses;
            failureCount += batch.length - batchSuccesses;

            // Update batch success count
            console.log(`Batch ${batchIndex + 1}: ${batchSuccesses}/${batch.length} successful`);

            // Add a 3-second delay between batches
            if (batchIndex < walletBatches.length - 1) {
                console.log('Waiting 3 seconds before next batch to avoid rate limits...');
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }

        // Log final results
        console.log(`\nBuy operations completed: ${successCount} successful, ${failureCount} failed out of ${wallets.length} total`);

        if (successCount > 0) {
            console.log(colors.green + "Successfully executed buy operations!" + colors.reset);
        } else if (failureCount === wallets.length) {
            console.log(colors.red + "All buy operations failed." + colors.reset);
        } else {
            console.log(colors.yellow + "Some buy operations completed successfully, others failed." + colors.reset);
        }

        return successCount;
    } catch (error) {
        console.error('Error in bundleBuyModule:', error);
        throw error;
    }
};

module.exports = createPoolWithLiquidity


