// Wallet Draining Tool
// This tool transfers all SUI from wallets in wallets.json to a specified destination address

require('dotenv').config();
const { Ed25519Keypair } = require('@mysten/sui/keypairs/ed25519');
const { getFullnodeUrl, SuiClient } = require('@mysten/sui/client');
const { Transaction } = require('@mysten/sui/transactions');
const { MIST_PER_SUI } = require('@mysten/sui/utils');
const fs = require('fs').promises;
const readline = require('readline');

// Initialize SUI client
const fullNodeUrl = "https://lively-sleek-violet.sui-mainnet.quiknode.pro/632bf33b2b28e62a3b173b730b0805a6a18ed42f/";
const client = new SuiClient({ url: fullNodeUrl });

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Helper function for prompts
const question = (query) => new Promise((resolve) => rl.question(query, resolve));

// ANSI colors for better readability
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Function to load wallets from wallets.json
async function loadWallets() {
  try {
    const data = await fs.readFile('wallets.json', 'utf8');
    return JSON.parse(data);
  } catch (error) {
    throw new Error(`Could not read wallets.json: ${error.message}`);
  }
}

// Function to check a wallet's balance
async function checkWalletBalance(address) {
  try {
    const balance = await client.getBalance({ owner: address });
    const suiBalance = Number.parseInt(balance.totalBalance) / Number(MIST_PER_SUI);
    return suiBalance;
  } catch (error) {
    throw new Error(`Error checking wallet balance: ${error.message}`);
  }
}

// Function to drain a single wallet
async function drainWallet(wallet, destinationAddress, retries = 3) {
  try {
    // Check wallet balance first
    const balance = await checkWalletBalance(wallet.address);

    // Skip if balance is too low for gas
    if (balance < 0.002) { // 0.002 SUI is a conservative estimate for minimum gas
      return {
        address: wallet.address,
        status: 'skipped',
        message: 'Balance too low for gas',
        balance: balance
      };
    }

    // Create keypair from the wallet's private key
    const keypair = Ed25519Keypair.fromSecretKey(wallet.privateKey);

    // Calculate amount to send (leave some for gas)
    // Reserve 0.002 SUI for gas fees
    const gasReserve = 0.002;
    const amountToSend = Math.max(0, balance - gasReserve);

    if (amountToSend <= 0) {
      return {
        address: wallet.address,
        status: 'skipped',
        message: 'Not enough balance after gas reservation',
        balance: balance
      };
    }

    // Convert SUI to MIST
    const amountInMist = Math.floor(amountToSend * Number(MIST_PER_SUI));

    // Execute with retries
    let attempt = 0;
    let lastError;

    while (attempt < retries) {
      try {
        // Create and execute transaction
        const tx = new Transaction();
        const [coin] = tx.splitCoins(tx.gas, [amountInMist]);
        tx.transferObjects([coin], destinationAddress);

        const txResult = await client.signAndExecuteTransaction({
          signer: keypair,
          transaction: tx
        });

        return {
          address: wallet.address,
          status: 'success',
          amount: amountToSend,
          originalBalance: balance,
          txHash: txResult.digest
        };
      } catch (error) {
        lastError = error;
        attempt++;

        if (attempt < retries) {
          // Exponential backoff
          const delay = Math.pow(2, attempt) * 1000 + Math.random() * 1000;
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    return {
      address: wallet.address,
      status: 'failed',
      message: lastError.message,
      balance: balance
    };
  } catch (error) {
    return {
      address: wallet.address,
      status: 'failed',
      message: error.message
    };
  }
}

// Main function
async function drainWallets() {
  console.log(`${colors.bright}${colors.cyan}===== WALLET DRAINING TOOL =====\n${colors.reset}`);

  try {
    // Get destination address
    const destinationAddress = await question(`${colors.yellow}Enter destination wallet address: ${colors.reset}`);

    // Validate address format (basic check for Sui address)
    if (!destinationAddress.startsWith('0x') || destinationAddress.length < 42) {
      console.log(`${colors.red}Invalid address format. Sui addresses start with '0x' and are at least 42 characters.${colors.reset}`);
      rl.close();
      return;
    }

    // Load wallets
    console.log(`\n${colors.yellow}Loading wallets from wallets.json...${colors.reset}`);
    const wallets = await loadWallets();
    console.log(`${colors.green}✓ Loaded ${wallets.length} wallets${colors.reset}`);

    // Check wallet balances
    console.log(`\n${colors.yellow}Checking wallet balances...${colors.reset}`);
    const balancePromises = wallets.map(wallet =>
      checkWalletBalance(wallet.address)
        .then(balance => ({ address: wallet.address, balance }))
        .catch(() => ({ address: wallet.address, balance: 0 }))
    );

    const balances = await Promise.all(balancePromises);
    const totalBalance = balances.reduce((sum, item) => sum + item.balance, 0);
    const walletsWithBalance = balances.filter(item => item.balance > 0.002);

    console.log(`${colors.green}✓ Found ${walletsWithBalance.length} wallets with sufficient balance${colors.reset}`);
    console.log(`${colors.green}✓ Total available balance: ${totalBalance.toFixed(6)} SUI${colors.reset}`);
    console.log(`${colors.green}✓ Estimated amount to recover (after gas): ${(totalBalance - (walletsWithBalance.length * 0.002)).toFixed(6)} SUI${colors.reset}`);

    if (walletsWithBalance.length === 0) {
      console.log(`${colors.yellow}No wallets with sufficient balance to transfer. Exiting.${colors.reset}`);
      rl.close();
      return;
    }

    // Confirm draining
    const confirm = await question(`\n${colors.yellow}You are about to drain ${walletsWithBalance.length} wallets to ${destinationAddress}. Proceed? (y/n): ${colors.reset}`);

    if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
      console.log(`${colors.yellow}Operation cancelled by user${colors.reset}`);
      rl.close();
      return;
    }

    // Extra confirmation
    const confirmAgain = await question(`${colors.red}⚠ WARNING: This action will transfer all SUI from your wallets. Type "CONFIRM" to proceed: ${colors.reset}`);

    if (confirmAgain !== 'CONFIRM') {
      console.log(`${colors.yellow}Operation cancelled by user${colors.reset}`);
      rl.close();
      return;
    }

    // Drain wallets sequentially
    console.log(`\n${colors.cyan}Starting draining process...${colors.reset}`);

    const results = [];
    let totalDrained = 0;

    for (let i = 0; i < wallets.length; i++) {
      const wallet = wallets[i];
      const walletBalance = balances.find(b => b.address === wallet.address)?.balance || 0;

      if (walletBalance <= 0.002) {
        console.log(`${colors.dim}Skipping wallet ${i + 1}/${wallets.length} (${wallet.address.substring(0, 8)}...): Insufficient balance (${walletBalance.toFixed(6)} SUI)${colors.reset}`);
        results.push({
          address: wallet.address,
          status: 'skipped',
          message: 'Insufficient balance',
          balance: walletBalance
        });
        continue;
      }

      console.log(`${colors.yellow}Draining wallet ${i + 1}/${wallets.length} (${wallet.address.substring(0, 8)}...): ${walletBalance.toFixed(6)} SUI${colors.reset}`);

      const result = await drainWallet(wallet, destinationAddress);
      results.push(result);

      if (result.status === 'success') {
        console.log(`${colors.green}✓ Successfully drained: ${result.amount.toFixed(6)} SUI${colors.reset}`);
        console.log(`${colors.dim}  Transaction: https://suiscan.xyz/mainnet/tx/${result.txHash}${colors.reset}`);
        totalDrained += result.amount;
      } else {
        console.log(`${colors.red}✗ Failed: ${result.message}${colors.reset}`);
      }

      // Add a small delay between transactions
      if (i < wallets.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Summary
    const successful = results.filter(r => r.status === 'success').length;
    const failed = results.filter(r => r.status === 'failed').length;
    const skipped = results.filter(r => r.status === 'skipped').length;

    console.log(`\n${colors.bright}${colors.cyan}===== DRAIN SUMMARY =====\n${colors.reset}`);
    console.log(`${colors.green}✓ Successfully drained: ${successful} wallets${colors.reset}`);
    console.log(`${colors.green}✓ Total SUI transferred: ${totalDrained.toFixed(6)} SUI${colors.reset}`);

    if (failed > 0) {
      console.log(`${colors.red}✗ Failed to drain: ${failed} wallets${colors.reset}`);
    }

    if (skipped > 0) {
      console.log(`${colors.yellow}ℹ Skipped (insufficient balance): ${skipped} wallets${colors.reset}`);
    }

    console.log(`\n${colors.green}Drain operation complete! Funds sent to: ${destinationAddress}${colors.reset}`);

  } catch (error) {
    console.error(`${colors.red}Error: ${error.message}${colors.reset}`);
  } finally {
    rl.close();
  }
}

// Run the script
drainWallets().catch(error => {
  console.error(`${colors.red}Fatal error: ${error.message}${colors.reset}`);
  process.exit(1);
}); 